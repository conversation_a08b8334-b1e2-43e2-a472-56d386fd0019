import { ComponentProps } from 'react'

export interface IDocsFormatIconsProps extends ComponentProps<'svg'> {
  width?: string
  height?: string
  fill?: string
}

export const PdfIcon = ({
  width = '40',
  height = '40',
  fill = 'none',
  ...rest
}: IDocsFormatIconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M4 4C4 1.79086 5.79086 0 8 0H24L36 12V36C36 38.2091 34.2091 40 32 40H8C5.79086 40 4 38.2091 4 36V4Z"
        fill="#D92D20"
      />
      <path
        opacity="0.3"
        d="M24 0L36 12H28C25.7909 12 24 10.2091 24 8V0Z"
        fill="white"
      />
      <path
        d="M11.7491 32V25.4545H14.3315C14.8279 25.4545 15.2508 25.5494 15.6003 25.739C15.9497 25.9265 16.216 26.1875 16.3993 26.522C16.5847 26.8544 16.6773 27.2379 16.6773 27.6726C16.6773 28.1072 16.5836 28.4908 16.3961 28.8232C16.2086 29.1555 15.9369 29.4144 15.5811 29.5998C15.2274 29.7852 14.7991 29.8778 14.2963 29.8778H12.6503V28.7688H14.0726C14.3389 28.7688 14.5584 28.723 14.731 28.6314C14.9057 28.5376 15.0356 28.4087 15.1209 28.2447C15.2082 28.0785 15.2519 27.8878 15.2519 27.6726C15.2519 27.4553 15.2082 27.2656 15.1209 27.1037C15.0356 26.9396 14.9057 26.8129 14.731 26.7234C14.5562 26.6317 14.3347 26.5859 14.0662 26.5859H13.1329V32H11.7491ZM19.8965 32H17.5762V25.4545H19.9157C20.5741 25.4545 21.1408 25.5856 21.616 25.8477C22.0911 26.1076 22.4565 26.4815 22.7122 26.9695C22.97 27.4574 23.0989 28.0412 23.0989 28.7209C23.0989 29.4027 22.97 29.9886 22.7122 30.4787C22.4565 30.9687 22.089 31.3448 21.6096 31.6069C21.1323 31.869 20.5613 32 19.8965 32ZM18.9601 30.8143H19.839C20.2481 30.8143 20.5922 30.7418 20.8713 30.5969C21.1526 30.4499 21.3635 30.223 21.5041 29.9162C21.6469 29.6072 21.7183 29.2088 21.7183 28.7209C21.7183 28.2372 21.6469 27.842 21.5041 27.5352C21.3635 27.2283 21.1536 27.0025 20.8745 26.8576C20.5954 26.7127 20.2513 26.6403 19.8422 26.6403H18.9601V30.8143ZM24.1241 32V25.4545H28.4579V26.5955H25.5079V28.1552H28.1702V29.2962H25.5079V32H24.1241Z"
        fill="white"
      />
    </svg>
  )
}

export const ExcelIcon = ({
  width = '40',
  height = '40',
  fill = 'none',
  ...rest
}: IDocsFormatIconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M4 4C4 1.79086 5.79086 0 8 0H24L36 12V36C36 38.2091 34.2091 40 32 40H8C5.79086 40 4 38.2091 4 36V4Z"
        fill="#079455"
      />
      <path
        opacity="0.3"
        d="M24 0L36 12H28C25.7909 12 24 10.2091 24 8V0Z"
        fill="white"
      />
      <path
        d="M13.1477 25.4545L14.4677 27.6854H14.5188L15.8452 25.4545H17.408L15.4105 28.7273L17.4528 32H15.8612L14.5188 29.766H14.4677L13.1254 32H11.5401L13.5888 28.7273L11.5785 25.4545H13.1477ZM18.2837 32V25.4545H19.6676V30.859H22.4737V32H18.2837ZM26.941 27.337C26.9154 27.0792 26.8057 26.8789 26.6118 26.7362C26.4179 26.5934 26.1547 26.522 25.8224 26.522C25.5965 26.522 25.4058 26.554 25.2503 26.6179C25.0947 26.6797 24.9754 26.766 24.8923 26.8768C24.8113 26.9876 24.7709 27.1133 24.7709 27.2539C24.7666 27.3711 24.7911 27.4734 24.8444 27.5607C24.8998 27.6481 24.9754 27.7237 25.0713 27.7876C25.1672 27.8494 25.278 27.9038 25.4037 27.9506C25.5294 27.9954 25.6636 28.0337 25.8064 28.0657L26.3944 28.2063C26.68 28.2702 26.942 28.3555 27.1807 28.462C27.4193 28.5685 27.626 28.6996 27.8007 28.8551C27.9754 29.0107 28.1107 29.1939 28.2066 29.4048C28.3046 29.6158 28.3547 29.8576 28.3568 30.1303C28.3547 30.5309 28.2524 30.8782 28.05 31.1722C27.8497 31.4641 27.5599 31.6911 27.1807 31.853C26.8035 32.0128 26.3486 32.0927 25.816 32.0927C25.2876 32.0927 24.8273 32.0117 24.4353 31.8498C24.0454 31.6879 23.7407 31.4482 23.5212 31.1307C23.3039 30.8111 23.1899 30.4158 23.1792 29.945H24.5184C24.5333 30.1644 24.5961 30.3477 24.7069 30.4947C24.8199 30.6396 24.9701 30.7493 25.1576 30.8239C25.3472 30.8963 25.5613 30.9325 25.8 30.9325C26.0344 30.9325 26.2378 30.8984 26.4104 30.8303C26.5851 30.7621 26.7204 30.6673 26.8163 30.5458C26.9122 30.4244 26.9601 30.2848 26.9601 30.1271C26.9601 29.9801 26.9165 29.8565 26.8291 29.7564C26.7439 29.6562 26.6182 29.571 26.452 29.5007C26.2879 29.4304 26.0866 29.3665 25.8479 29.3089L25.1352 29.13C24.5834 28.9957 24.1476 28.7859 23.828 28.5004C23.5084 28.2148 23.3497 27.8303 23.3518 27.3466C23.3497 26.9503 23.4552 26.604 23.6682 26.3079C23.8834 26.0117 24.1785 25.7805 24.5535 25.6143C24.9285 25.4482 25.3547 25.3651 25.8319 25.3651C26.3177 25.3651 26.7417 25.4482 27.104 25.6143C27.4683 25.7805 27.7517 26.0117 27.9541 26.3079C28.1565 26.604 28.2609 26.9471 28.2673 27.337H26.941Z"
        fill="white"
      />
    </svg>
  )
}
