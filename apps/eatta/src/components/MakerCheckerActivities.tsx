import { Stack, Typography } from '@mui/material'
import dayjs from 'dayjs'
import { StepDoneIcon } from '@dtbx/ui/icons'
import React from 'react'
import { MakerCheckerActivity } from '@/store/interfaces/makerChecker'

export interface CheckerActivitiesProps {
  activities: MakerCheckerActivity[]
}

const MakerCheckerActivities: React.FC<CheckerActivitiesProps> = ({
  activities = [],
}) => {
  return (
    <Stack spacing={4}>
      <Typography variant="subtitle1" fontWeight={600}>
        Activity
      </Typography>

      {activities.map((activity) => (
        <Stack spacing={1}>
          <Stack spacing={1} direction="row" alignItems="center" useFlexGap>
            <StepDoneIcon height={16} width={16} />

            <Stack>
              <Stack direction="row" spacing={1}>
                <Typography>{activity.action} </Typography>
                <Typography fontWeight="bold">{activity.actionedBy}</Typography>
                <Typography color="textDisabled">
                  {activity.actionedDate &&
                    dayjs(activity.actionedDate).format('MMMM D, YYYY hh:mm A')}
                </Typography>
              </Stack>

              {activity.comment && (
                <Typography>Comments:{activity.comment} </Typography>
              )}
            </Stack>
          </Stack>
        </Stack>
      ))}
    </Stack>
  )
}
export default MakerCheckerActivities
