/**
 * <AUTHOR> on 22/05/2025
 */
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ack, Typography } from '@mui/material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import React, { FC, useEffect, useState } from 'react'
import { CompanyFilters, PageFilters } from '@/store/interfaces'
import { resetOnboardingState } from '@/store/reducers'
import { useAppDispatch } from '@/store'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'

export interface CompanyPageHeaderProps {
  filters: CompanyFilters
  onSearchChange: (key: keyof CompanyFilters, value: string) => void
}

type CompanySearchByType = keyof Omit<CompanyFilters, keyof PageFilters>

export const CompanyHeader: FC<CompanyPageHeaderProps> = ({
  filters,
  onSearchChange,
}) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()

  const [searchBy, setSearchBy] = useState<CompanySearchByType>('name')
  const searchByValue = Object.keys(filters).filter(
    (key) => typeof filters[key as keyof CompanyFilters] === 'string'
  )
  const [searchValue, setSearchValue] = useState('')
  const debouncedSearchValue = useDebounce(searchValue, 500)

  const handleCreateCompany = () => {
    dispatch(resetOnboardingState())
    router.push('/backoffice/companies/create')
  }

  useEffect(() => {
    onSearchChange(searchBy, debouncedSearchValue)
  }, [debouncedSearchValue, searchBy])

  return (
    <Stack>
      <Stack paddingInline={3} paddingBlock={2}>
        <Typography
          variant="h5"
          sx={{
            fontWeight: 600,
            color: '#000A12',
          }}
        >
          Companies
        </Typography>
      </Stack>

      <Divider />

      <Stack
        paddingInline={3}
        paddingBlock={2}
        spacing={1}
        flexWrap="wrap"
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <CustomFilterUserBox
          searchValue={searchValue}
          searchByValues={searchByValue}
          selectedSearchBy={searchBy}
          onHandleSearch={(e) => setSearchValue(e.target.value)}
          setSearchByValue={(value: string) =>
            setSearchBy(value as CompanySearchByType)
          }
          searchPlaceHolder={'Search company'}
          prependSearchBy={true}
        />

        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_ORGANIZATION}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddOutlinedIcon />}
            sx={{ borderRadius: '0.5rem', marginTop: 0, textWrap: 'nowrap' }}
            onClick={handleCreateCompany}
          >
            New Company
          </Button>
        </AccessControlWrapper>
      </Stack>
      <Divider />
    </Stack>
  )
}
