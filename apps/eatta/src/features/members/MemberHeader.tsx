import { useState, FC, useEffect } from 'react'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { Stack, IconButton, Typography, Divider, Button } from '@mui/material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import { UserFilters, PageFilters } from '@/store/interfaces'
import { resetOnboardingState } from '@/store/reducers'
import { useAppDispatch } from '@/store'
import { AutoCompleteOnFilter } from '@/components/AutoCompleteOnFilter'
import { COMPANY_TYPES_COMBINED, Company } from '@/store/interfaces'

interface MemberHeaderProps {
  filters: UserFilters
  onSearchChange: (key: keyof UserFilters, value: string) => void
}

const MemberHeader: FC<MemberHeaderProps> = ({ filters, onSearchChange }) => {
  type UserSearchByType = keyof Omit<UserFilters, keyof PageFilters>
  const [searchValue, setSearchValue] = useState('')
  const [searchBy, setSearchBy] = useState<UserSearchByType>('email')
  const searchByValue = Object.keys(filters).filter(
    (key) => typeof filters[key as keyof UserFilters] === 'string'
  )

  const searchByLabels: Record<string, string> = {
    name: 'Name',
    email: 'Email',
    phoneNumber: 'Phone Number',
    username: 'Username',
    status: 'Status',
    companyName: 'Company Name',
    companyType: 'Company Type',
    companyCode: 'Company Code',
  }

  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null)

  const debouncedSearchValue = useDebounce(searchValue, 400)

  useEffect(() => {
    onSearchChange(searchBy, debouncedSearchValue)
  }, [debouncedSearchValue, searchBy])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(value)
  }

  const handleSearchByChange = (value: string) => {
    const newSearchBy = value as UserSearchByType
    setSearchBy(newSearchBy)
    setSearchValue('')
    onSearchChange(searchBy, '')
  }
  const handleCreateUser = () => {
    dispatch(resetOnboardingState())
    router.push(`/backoffice/members/create`)
  }

  const handleCompanySelect = (company: Company | null) => {
    setSelectedCompany(company)
    // You can add additional logic here when a company is selected
    console.log('Selected company:', company)
  }

  const handleUsersLoaded = () => {
    // This will be called when users are loaded for the selected company
    console.log('Users loaded for company:', selectedCompany?.name)
  }

  return (
    <Stack>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <IconButton
            sx={{
              borderRadius: '8px',
              border: '1px solid #D0D5DD',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              display: 'none',
            }}
            onClick={() => router.back()}
          >
            <ArrowBackIosNewOutlinedIcon />
          </IconButton>
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            Members
          </Typography>
        </Stack>
      </Stack>
      <Divider />
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          justifyContent: 'space-between',
        }}
        direction="row"
        spacing={2}
        paddingInline={3}
        paddingBlock={2}
        alignItems="center"
        justifyContent="flex-start"
      >
        <CustomFilterUserBox
          searchValue={searchValue}
          searchByValues={searchByValue}
          searchByLabel={searchByLabels}
          selectedSearchBy={searchBy}
          onHandleSearch={handleSearch}
          setSearchByValue={handleSearchByChange}
          searchPlaceHolder={`Search ${searchBy || 'member'}`}
          prependSearchBy={true}
        />
        <AutoCompleteOnFilter
          companyTypes={COMPANY_TYPES_COMBINED}
          onCompanySelect={handleCompanySelect}
          onUsersLoaded={handleUsersLoaded}
        />
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USER}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddOutlinedIcon />}
            sx={{ borderRadius: '8px' }}
            onClick={handleCreateUser}
          >
            Add Member
          </Button>
        </AccessControlWrapper>
      </Stack>
    </Stack>
  )
}

export default MemberHeader
