{"name": "eatta", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack --port 3003", "dev-client": "NEXT_PUBLIC_EATTA_BUILD='client' next dev --turbopack --port 3004", "build": "next build", "build-client": "NEXT_PUBLIC_EATTA_BUILD='client' next build", "start": "next start --port 3003", "start-client": "NEXT_PUBLIC_EATTA_BUILD='client' next start --port 3004", "lint": "next lint", "lint:fix": "next lint --fix", "clean": "rimraf .turbo .next .next-client __tests__/coverage", "test": "vitest run", "test:watch": "vitest --watch", "test:vitest-ui": "vitest --ui --coverage", "test:view-report": "open __tests__/coverage/index.html"}, "dependencies": {"@dtbx/store": "workspace:*", "@dtbx/ui": "workspace:*", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.0", "@mui/x-date-pickers": "^7.28.0", "@reduxjs/toolkit": "^2.4.0", "add": "^2.0.6", "axios": "^1.8.4", "dayjs": "^1.11.13", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "mui-tel-input": "^8.0.1", "next": "15.2.3", "node-forge": "^1.3.1", "openpgp": "^6.1.0", "react": "19.0.0", "react-dom": "19.0.0", "react-redux": "^9.1.2", "react-transition-group": "^4.4.5", "redux": "^5.0.1", "redux-persist": "^6.0.0", "tiny-case": "^1.0.3", "uuid": "^11.1.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "yup": "^1.5.0"}, "devDependencies": {"@dtbx/eslint-config": "workspace:*", "@dtbx/typescript-config": "workspace:*", "@dtbx/vitest-config": "workspace:*", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/lodash": "^4.17.13", "@types/node": "^20.17.10", "@types/node-forge": "^1.3.11", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "@types/react-transition-group": "^4.4.12", "@vitest/coverage-istanbul": "^3.0.9", "eslint": "^9.16.0", "eslint-config-next": "15.2.3", "nyc": "^17.1.0", "rimraf": "^6.0.1", "swc-plugin-coverage-instrument": "0.0.26", "typescript": "^5.8.2", "vitest": "^3.0.9"}}