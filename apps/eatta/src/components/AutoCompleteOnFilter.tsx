import React, { useState, useEffect } from 'react'
import { Autocomplete, Stack, TextField, Select, MenuItem } from '@mui/material'
import { KeyboardArrowDownRounded } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import { getCompanies, getUsersByOrganizationCode } from '@/store/actions'
import { Company, CompanyFilters, UserFilters } from '@/store/interfaces'

interface AutoCompleteOnFilterProps {
  companyTypes: readonly string[]
  onCompanySelect?: (company: Company | null) => void
  onUsersLoaded?: () => void
}

export const AutoCompleteOnFilter: React.FC<AutoCompleteOnFilterProps> = ({
  companyTypes,
  onCompanySelect,
  onUsersLoaded,
}) => {
  const dispatch = useAppDispatch()
  const { companiesResponse, isLoading } = useAppSelector(
    (state) => state.companies
  )

  const [selectedCompanyType, setSelectedCompanyType] = useState<string>(
    companyTypes[0] || ''
  )
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null)
  const [companySearchValue, setCompanySearchValue] = useState('')

  useEffect(() => {
    if (selectedCompanyType) {
      const filters: CompanyFilters = {
        page: 1,
        size: 10,
        type: selectedCompanyType,
        name: companySearchValue || undefined,
      }
      getCompanies(dispatch, filters)
    }
  }, [selectedCompanyType, companySearchValue, dispatch])

  useEffect(() => {
    if (selectedCompany?.code) {
      const userFilters: UserFilters = {
        page: 1,
        size: 10,
      }
      getUsersByOrganizationCode(dispatch, selectedCompany.code, userFilters)
      onUsersLoaded?.()
    }
  }, [selectedCompany, dispatch, onUsersLoaded])

  const handleCompanyTypeChange = (newType: string) => {
    setSelectedCompanyType(newType)
    setSelectedCompany(null)
    setCompanySearchValue('')
    onCompanySelect?.(null)
  }

  const handleCompanySelect = (company: Company | null) => {
    setSelectedCompany(company)
    onCompanySelect?.(company)
  }

  return (
    <Stack spacing={2} direction="row" width="50%">
      <FilterBox
        filterValues={companyTypes}
        selectedSearchBy={selectedCompanyType}
        onFilterChange={handleCompanyTypeChange}
      />
      <Autocomplete
        sx={{ width: '70%' }}
        size="small"
        loading={isLoading}
        options={companiesResponse.data || []}
        getOptionLabel={(option) => `${option.name} (${option.code})`}
        value={selectedCompany}
        onChange={(_, newValue) => handleCompanySelect(newValue)}
        inputValue={companySearchValue}
        onInputChange={(_, newInputValue) =>
          setCompanySearchValue(newInputValue)
        }
        renderInput={(params) => (
          <TextField
            {...params}
            hiddenLabel
            placeholder={`Select ${selectedCompanyType} company`}
          />
        )}
        noOptionsText={`No ${selectedCompanyType} companies found`}
      />
    </Stack>
  )
}

export const FilterBox = ({
  filterValues,
  selectedSearchBy,
  onFilterChange,
}: {
  selectedSearchBy: string
  filterValues: readonly string[]
  onFilterChange: (value: string) => void
}) => {
  return (
    <Stack direction={'row'}>
      <Select
        size="medium"
        onChange={(e) => onFilterChange(e.target.value)}
        IconComponent={(iconProps) => (
          <KeyboardArrowDownRounded
            {...iconProps}
            sx={{
              color: '#667085',
              marginLeft: '0.5rem',
              ...(iconProps?.sx || {}),
            }}
          />
        )}
        value={selectedSearchBy}
        sx={{
          width: '100%',
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '2px !important',
          },
          background: '#FFFFFF',
          '& fieldset': {
            border: `1px solid #D0D5DD !important`,
          },
          borderRadius: '8px',
          color: '#667085',
        }}
      >
        {filterValues.map((value) => (
          <MenuItem key={value} value={value}>
            {`Search for ${value}`}
          </MenuItem>
        ))}
      </Select>
    </Stack>
  )
}
