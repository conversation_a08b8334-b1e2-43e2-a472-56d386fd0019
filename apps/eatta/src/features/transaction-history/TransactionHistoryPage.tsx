'use client'
import React from 'react'
import TransactionHistoryList from './TransactionHistoryList'
import { PageHeader } from './pageHeader'
import { Stack } from '@mui/material'
import { useParams } from 'next/navigation'
const TransactionHistoryPage = () => {
  const params = useParams()
  const invoiceId = params.invoiceId as string

  return (
    <PageHeader>
      <Stack sx={{ overflow: 'auto' }}>
        <TransactionHistoryList invoiceId={invoiceId} />
      </Stack>
    </PageHeader>
  )
}

export default TransactionHistoryPage
