import React, { FC, useEffect, useState } from 'react'
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomSkeleton } from '@dtbx/ui/components'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { useAppDispatch, useAppSelector } from '@/store'
import { getTransactionHistory } from '@/store/actions'
import { checkIfBackOffice } from '@/utils/appTypeChecker'

interface TransactionHistoryListProps {
  invoiceId?: string
}
const TransactionHistoryList: FC<TransactionHistoryListProps> = ({
  invoiceId,
}) => {
  const { transactionHistoryResponse, isLoading } = useAppSelector(
    (state) => state.invoices
  )
  const dispatch = useAppDispatch()
  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
    totalPages: transactionHistoryResponse.totalNumberOfPages,
  })
  const isBackOffice = checkIfBackOffice()

  useEffect(() => {
    const fetchTransactions = async () => {
      await getTransactionHistory(
        dispatch,
        {
          size: paginationOptions.size,
          page: paginationOptions.page,
          ascending: true,
        },
        invoiceId
      )
    }
    fetchTransactions()
  }, [dispatch])
  /*************************start pagination handlers***************************/
  const handlePagination = (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
  }
  /*************************end pagination handlers***************************/
  return isLoading ? (
    <CustomSkeleton
      animation="pulse"
      variant="rectangular"
      width={'100%'}
      height={'60vh'}
    />
  ) : transactionHistoryResponse.data.length === 0 ? (
    <EmptyPage
      bgUrl={isBackOffice ? '/eatta/combo.svg' : '/combo.svg'}
      title="No transactions found"
      message="Your transaction history will appear here once you start making transactions."
    />
  ) : (
    <TableContainer
      component={Paper}
      sx={{ boxShadow: 'none', marginTop: '25px' }}
    >
      <Table
        sx={{ minWidth: 650 }}
        aria-label="transaction history table"
        size="small"
      >
        <CustomTableHeader
          rowCount={transactionHistoryResponse.data.length}
          numSelected={0}
          order={'asc'}
          headLabel={[
            {
              id: 'transactionId',
              label: 'Transaction Ref',
              alignRight: false,
            },
            { id: 'type', label: 'Transaction Type', alignRight: false },
            { id: 'date', label: 'Date', alignRight: false },
            { id: 'channel', label: 'Channel', alignRight: false },
            { id: 'source', label: 'Source', alignRight: false },
            { id: 'destination', label: 'Destination', alignRight: false },
            { id: 'amount', label: 'Amount', alignRight: false },
            // { id: 'balance', label: 'Wallet Balance', alignRight: false },
          ]}
          showCheckbox={false}
          onRequestSort={() => {}}
        />

        <TableBody>
          {transactionHistoryResponse.data.map((transaction, index) => (
            <TableRow
              hover
              key={`${transaction.transactionReference}-${index}`}
            >
              <TableCell>
                <Typography variant="body1">
                  {transaction.transactionReference}
                </Typography>
              </TableCell>
              <TableCell>
                {transaction.ledgerEntryType === 'CREDIT' ? (
                  <StatusChip label={transaction.ledgerEntryType} />
                ) : (
                  <StatusChip
                    status="error"
                    label={transaction.ledgerEntryType}
                  />
                )}
              </TableCell>
              <TableCell>
                <Typography variant="body1">
                  {new Date(transaction.dateCreated).toLocaleDateString(
                    'en-US',
                    {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    }
                  )}
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  {new Date(transaction.dateCreated).toLocaleTimeString(
                    'en-US',
                    {
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: true,
                    }
                  )}
                </Typography>
              </TableCell>
              <TableCell>{transaction.channel}</TableCell>
              <TableCell>
                <Typography variant="body1">{transaction.source}</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body1">
                  {transaction.destination}
                </Typography>
              </TableCell>
              <TableCell>
                {`$${new Intl.NumberFormat('en-US').format(transaction.amount)}`}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell
              colSpan={8}
              align="center"
              sx={{ paddingInline: 0, height: 40 }}
            >
              {transactionHistoryResponse.totalNumberOfPages > 0 && (
                <CustomPagination
                  options={{
                    ...paginationOptions,
                    totalPages: 1,
                  }}
                  handlePagination={handlePagination}
                />
              )}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  )
}

export default TransactionHistoryList
