import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import {
  AccountSalesEntry,
  BrokerInvoice,
  BrokerInvoicesEntryResponse,
  BrokerInvoicesResponse,
  DisbursementsEntryResponse,
  InvoiceEntry,
  InvoiceEslip,
  ProducerInvoiceEntryResponse,
  TransactionHistoryResponse,
} from '../interfaces'
  import { PaginatedResponse } from '@dtbx/store/interfaces'

export interface InvoicesState {
  //Broker Invoices
  brokerInvoicesResponse: BrokerInvoicesResponse
  brokerInvoice: BrokerInvoice | null
  isLoading: boolean
  isUploadingBrokerInvoices: boolean
  brokerInvoicesEntryResponse: BrokerInvoicesEntryResponse
  //Producer Invoices/ statements
  producerInvoiceEntryResponse: ProducerInvoiceEntryResponse
  transactionHistoryResponse: TransactionHistoryResponse
  isDispatchingInvoices: boolean
  selectedInvoices: BrokerInvoice[]
  //Disbursements
  disbursementsEntryResponse: DisbursementsEntryResponse
  //Account Sales
  isLoadingAccountSalesInfo: boolean
  accountSalesEntryResponse: AccountSalesEntry
  //Batching & Checkout
  selectedInvoiceLots: InvoiceEntry[]
  isCheckingOut: boolean
  invoiceEslipsResponse: PaginatedResponse<InvoiceEslip>
  invoiceEslip: InvoiceEslip | null
  isDownloadingEslip: boolean
}

const initialPaginatedState = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}

const initialAccountSalesEntryResponse: AccountSalesEntry = {
  brokerInvoiceEntries: [],
  mainGrades: {
    totalNetSoldWeight: 0,
    totalGrossValue: 0,
    totalBrokerCommissionFromProducer: 0,
    totalWareHouseCharges: 0,
    totalNetProceedsDue: 0,
    totalWithholdingTax: 0,
  },
  allGrades: {
    totalNetSoldWeight: 0,
    totalGrossValue: 0,
    totalBrokerCommissionFromProducer: 0,
    totalWareHouseCharges: 0,
    totalNetProceedsDue: 0,
    totalWithholdingTax: 0,
  },
  secondaryGrades: {
    totalNetSoldWeight: 0,
    totalGrossValue: 0,
    totalBrokerCommissionFromProducer: 0,
    totalWareHouseCharges: 0,
    totalNetProceedsDue: 0,
    totalWithholdingTax: 0,
  },
}

const initialState: InvoicesState = {
  //Broker Invoices
  brokerInvoicesResponse: initialPaginatedState,
  isLoading: false,
  isUploadingBrokerInvoices: false,
  selectedInvoiceLots: [],
  transactionHistoryResponse: initialPaginatedState,
  brokerInvoicesEntryResponse: initialPaginatedState,
  //Producer Invoices/ statements
  producerInvoiceEntryResponse: initialPaginatedState,
  brokerInvoice: null,
  isDispatchingInvoices: false,
  //Disbursements
  disbursementsEntryResponse: initialPaginatedState,
  //Account Sales
  isLoadingAccountSalesInfo: false,
  accountSalesEntryResponse: initialAccountSalesEntryResponse,
  //Batching & Checkout
  selectedInvoices: [],
  isCheckingOut: false,
  //Checkout
  invoiceEslipsResponse: initialPaginatedState,
  invoiceEslip: null,
  isDownloadingEslip: false,
 }

const invoicesSlice = createSlice({
  name: 'invoices',
  initialState,
  reducers: {
    //Broker Invoices
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setIsCheckingOut: (state, action: PayloadAction<boolean>) => {
      state.isCheckingOut = action.payload
    },
    setBrokerInvoicesResponse: (
      state,
      action: PayloadAction<BrokerInvoicesResponse>
    ) => {
      state.brokerInvoicesResponse = action.payload
    },
    resetBrokerInvoicesResponse: (state) => {
      state.brokerInvoicesResponse = initialPaginatedState
    },
    setIsUploadingBrokerInvoices: (state, action: PayloadAction<boolean>) => {
      state.isUploadingBrokerInvoices = action.payload
    },
    setTransactionHistoryResponse: (
      state,
      action: PayloadAction<TransactionHistoryResponse>
    ) => {
      state.transactionHistoryResponse = action.payload
    },
    setBrokerInvoiceResponse: (state, action: PayloadAction<BrokerInvoice>) => {
      state.brokerInvoice = action.payload
    },
    setBrokerInvoiceEntryResponse: (
      state,
      action: PayloadAction<BrokerInvoicesEntryResponse>
    ) => {
      state.brokerInvoicesEntryResponse = action.payload
    },
    //Producer Invoices/ statements
    setProducerInvoiceEntryResponse: (
      state,
      action: PayloadAction<ProducerInvoiceEntryResponse>
    ) => {
      state.producerInvoiceEntryResponse = action.payload
    },
    //Disbursements
    setDisbursementsEntryResponse: (
      state,
      action: PayloadAction<DisbursementsEntryResponse>
    ) => {
      state.disbursementsEntryResponse = action.payload
    },
    setIsDispatchingInvoices: (state, action: PayloadAction<boolean>) => {
      state.isDispatchingInvoices = action.payload
    },
    setSelectedInvoices: (state, action: PayloadAction<BrokerInvoice[]>) => {
      state.selectedInvoices = action.payload
    },
    setSelectedInvoice: (state, action: PayloadAction<BrokerInvoice>) => {
      const exist = state.selectedInvoices.some(
        (i) => i.id === action.payload.id
      )
      state.selectedInvoices = exist
        ? state.selectedInvoices.filter((i) => i.id !== action.payload.id)
        : [...state.selectedInvoices, action.payload]
    },
    setSelectedInvoiceLots: (state, action: PayloadAction<InvoiceEntry[]>) => {
      state.selectedInvoiceLots = action.payload
    },
    setSelectedInvoiceLot: (state, action: PayloadAction<InvoiceEntry>) => {
      const exist = state.selectedInvoiceLots.some(
        (i) => i.id === action.payload.id
      )
      state.selectedInvoiceLots = exist
        ? state.selectedInvoiceLots.filter((i) => i.id !== action.payload.id)
        : [...state.selectedInvoiceLots, action.payload]
    },
    setInvoiceEslipsResponse: (
      state,
      action: PayloadAction<PaginatedResponse<InvoiceEslip>>
    ) => {
      state.invoiceEslipsResponse = action.payload
    },
    setInvoiceEslip: (state, action: PayloadAction<InvoiceEslip>) => {
      state.invoiceEslip = action.payload
    },
    setIsDownloadingEslip: (state, action: PayloadAction<boolean>) => {
      state.isDownloadingEslip = action.payload
    },
    //Account Sales
    setIsLoadingAccountSalesInfo: (state, action: PayloadAction<boolean>) => {
      state.isLoadingAccountSalesInfo = action.payload
    },
    setAccountSalesEntryData: (
      state,
      action: PayloadAction<AccountSalesEntry>
    ) => {
      state.accountSalesEntryResponse = action.payload
    },
    resetInvoiceEslips: (state) => {
      state.invoiceEslipsResponse = initialPaginatedState
    },
    resetInvoicesStore: () => initialState,
  },
})

export const {
  //Broker Invoices
  setIsLoading,
  setBrokerInvoicesResponse,
  setIsUploadingBrokerInvoices,
  setTransactionHistoryResponse,
  setBrokerInvoiceResponse,
  setBrokerInvoiceEntryResponse,
  setIsDispatchingInvoices,
  //Producer Invoices/ statements
  setProducerInvoiceEntryResponse,
  //Disbursements
  setDisbursementsEntryResponse,
  //Account Sales
  setIsLoadingAccountSalesInfo,
  setAccountSalesEntryData,
  //Invoice Selection
  setSelectedInvoice,
  setSelectedInvoices,
  //Invoiced Lots Selection
  setSelectedInvoiceLots,
  setSelectedInvoiceLot,
  //Checkout & E-slips
  setIsCheckingOut,
  setInvoiceEslipsResponse,
  setInvoiceEslip,
  resetInvoiceEslips,
  setIsDownloadingEslip,
  //Reset store
  resetBrokerInvoicesResponse,
  resetInvoicesStore,
} = invoicesSlice.actions

export default invoicesSlice.reducer
