import { AuctionEntry } from './catalogues'
import { BrokerInvoice } from './invoices'
import { PageFilters } from './filters'
import { BrokerInvoiceStatus } from './invoices'

//Delivery Orders
export type DeliveryOrderStatus = 'PENDING_APPROVAL' | 'NEW' | 'SIGNED'

export interface DeliveryOrderFilters extends PageFilters {
  year: number
  saleDate: string
  brokerCode?: string
  dateCreated?: number
  status?: string
  docNumber?: string
  trdDocNumber?: string
  invoiceNumber?: string
  buyerCode?: string
  warehouse?: string
}

export interface DeliveryOrderResponse {
  id: string
  dateCreated: string
  dateModified: string
  docNumber: string
  status: DeliveryOrderStatus
  modifiedBy: string
  submittedBy: string
  approvedBy: string
  signedBy: string
  signingDate: string
  brokerLogoUrl: string
  trdDocNumber: string
  edoDownloadUrl: string
  trdDownloadUrl: string
  auctionNo: string
  invoiceNumber: string
  saleDate: string
  promptDate: string
  lotCounts: number
  buyerCode: string
  buyer: string
  warehouse: string
  weight: number
  brokerInvoice: string
  soldWeight: number
  lotCount: number
  brokerCode: string
  entries: DeliveryOrderEntry[]
}

export interface DeliveryOrderEntry {
  id: string
  invoiceNumber: string
  gardenInvoice: string
  lotNumber: number
  auctionDate: string
  buyerCode: string
  buyerName: string
  broker: string
  mark: string
  grade: string
  totalWeight: number
  saleDate: string
  category: string
  pricePerKg: number
  netWeight: number
  unitWeight: number
  totalValue: number
  brokerShare: number
  withholdingTax: number
  netAmount: number
  totalPenalties: number
  netPayable: number
  warehouse: string
  bags: string
  datePaid: string
  teaReleaseDocumentDate: string
  status: BrokerInvoiceStatus
  brokerCommissionFromProducer: number
  producerShare: number
  producerExpectedAmount: number
  errors: Record<string, string>
  auctionEntry: AuctionEntry
  brokerInvoice: BrokerInvoice
}

export interface SignDeliveryOrderPayload {
  orderId: string
  signer: string
}
