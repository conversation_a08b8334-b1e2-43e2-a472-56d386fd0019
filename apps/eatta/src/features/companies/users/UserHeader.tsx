import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack, Typography } from '@mui/material'
import React, { FC, useEffect, useState } from 'react'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { useAppDispatch, useAppSelector } from '@/store'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { PageFilters, UserFilters } from '@/store/interfaces'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { resetOnboardingState } from '@/store/reducers'

interface UserHeaderProps {
  filters: UserFilters
  onSearchChange: (key: keyof UserFilters, value: string) => void
}

type UserSearchByType = keyof Omit<UserFilters, keyof PageFilters>

export const UserHeader: FC<UserHeaderProps> = ({
  filters,
  onSearchChange,
}: UserHeaderProps) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { selectedCompany } = useAppSelector((state) => state.companies)

  const [searchBy, setSearchBy] = useState<UserSearchByType>('email')
  const searchByValue = Object.keys(filters).filter(
    (key) => typeof filters[key as keyof UserFilters] === 'string'
  )
  const [searchValue, setSearchValue] = useState('')
  const debouncedSearchValue = useDebounce(searchValue, 500)

  const handleCreateUser = (organizationId: string) => {
    dispatch(resetOnboardingState())
    router.push(`/backoffice/companies/${organizationId}/users/create`)
  }

  useEffect(() => {
    onSearchChange(searchBy, debouncedSearchValue)
  }, [debouncedSearchValue, searchBy])

  return (
    <Stack>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <IconButton
            sx={{
              borderRadius: '8px',
              border: '1px solid #D0D5DD',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            onClick={() => router.back()}
          >
            <ArrowBackIosNewOutlinedIcon />
          </IconButton>
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            {selectedCompany?.name}
          </Typography>
        </Stack>
      </Stack>
      <Divider />
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          justifyContent: 'space-between',
        }}
        direction="row"
        spacing={2}
        paddingInline={3}
        paddingBlock={2}
        alignItems="center"
        justifyContent="flex-start"
      >
        <CustomFilterUserBox
          searchValue={searchValue}
          searchByValues={searchByValue}
          selectedSearchBy={searchBy}
          onHandleSearch={(e) => setSearchValue(e.target.value)}
          setSearchByValue={(value: string) =>
            setSearchBy(value as UserSearchByType)
          }
          searchPlaceHolder={'Search member'}
          prependSearchBy={true}
        />
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USER}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddOutlinedIcon />}
            sx={{ borderRadius: '8px' }}
            onClick={() => handleCreateUser(selectedCompany?.id ?? '')}
          >
            Add User
          </Button>
        </AccessControlWrapper>
      </Stack>
    </Stack>
  )
}
