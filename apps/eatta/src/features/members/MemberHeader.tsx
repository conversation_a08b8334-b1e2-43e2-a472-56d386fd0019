import { useState, FC, useEffect, useMemo } from 'react'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { Stack, IconButton, Typography, Divider, Button } from '@mui/material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import { UserFilters, PageFilters } from '@/store/interfaces'
import { ApprovalRequestFilters } from '@/store/interfaces/makerChecker'
import { SearchByValueConfig } from '@/components/PageFilters'
import { resetOnboardingState } from '@/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'
import { AutoCompleteOnFilter } from '@/components/AutoCompleteOnFilter'
import {
  COMPANY_TYPES_COMBINED,
  Company,
  CompanyFilters,
} from '@/store/interfaces'
import { getCompanies } from '@/store/actions'

type MemberSearchByType =
  | keyof Omit<UserFilters, keyof PageFilters>
  | keyof Omit<ApprovalRequestFilters, keyof PageFilters>

interface MemberHeaderProps {
  filters: UserFilters
  onSearchChange: (
    key: keyof UserFilters | keyof ApprovalRequestFilters,
    value: string
  ) => void
  onCompanyCodeChange?: (
    companyCode: string | null,
    companyType?: string | null
  ) => void
  searchByValues:
    | SearchByValueConfig<UserFilters>[]
    | SearchByValueConfig<ApprovalRequestFilters>[]
  initialSearchBy: MemberSearchByType
  showCompanyFilter?: boolean
}

const MemberHeader: FC<MemberHeaderProps> = ({
  filters,
  onSearchChange,
  onCompanyCodeChange,
  searchByValues,
  initialSearchBy,
  showCompanyFilter = true,
}) => {
  const [searchValue, setSearchValue] = useState('')
  const [searchBy, setSearchBy] = useState<MemberSearchByType>(initialSearchBy)

  const searchByLabel = useMemo(() => {
    const map: Record<string, string> = {}
    searchByValues.forEach((value) => {
      map[String(value.filterKey)] =
        value.filterLabel || String(value.filterKey)
    })
    return map
  }, [searchByValues])

  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { companiesResponse, isLoading } = useAppSelector(
    (state) => state.companies
  )

  const [selectedCompanyType, setSelectedCompanyType] = useState<string>('')
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null)
  const [companySearchValue, setCompanySearchValue] = useState('')

  const debouncedSearchValue = useDebounce(searchValue, 400)
  const debouncedCompanySearch = useDebounce(companySearchValue, 400)

  useEffect(() => {
    onSearchChange(searchBy, debouncedSearchValue)
  }, [debouncedSearchValue, searchBy])

  useEffect(() => {
    if (selectedCompanyType) {
      const filters: CompanyFilters = {
        page: 1,
        size: 10,
        type: selectedCompanyType,
        name: debouncedCompanySearch || undefined,
      }
      getCompanies(dispatch, filters)
    }
  }, [selectedCompanyType, debouncedCompanySearch, dispatch])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(value)
  }

  const handleSearchByChange = (value: string) => {
    const newSearchBy = value as MemberSearchByType
    setSearchBy(newSearchBy)
    setSearchValue('')
    onSearchChange(newSearchBy, '')
  }

  // Update search by when initialSearchBy changes (tab change)
  useEffect(() => {
    setSearchBy(initialSearchBy)
  }, [initialSearchBy])
  const handleCreateUser = () => {
    dispatch(resetOnboardingState())
    router.push(`/backoffice/members/create`)
  }

  const handleCompanyTypeChange = (companyType: string) => {
    setSelectedCompanyType(companyType)
    setSelectedCompany(null)
    setCompanySearchValue('')
    // Clear company selection from filters when type changes
    onCompanyCodeChange?.(null, null)
  }

  const handleCompanySelect = (company: Company | null) => {
    setSelectedCompany(company)
    onCompanyCodeChange?.(company?.code || null, selectedCompanyType || null)
  }

  const handleCompanySearchChange = (searchValue: string) => {
    setCompanySearchValue(searchValue)
  }

  return (
    <Stack>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <IconButton
            sx={{
              borderRadius: '8px',
              border: '1px solid #D0D5DD',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              display: 'none',
            }}
            onClick={() => router.back()}
          >
            <ArrowBackIosNewOutlinedIcon />
          </IconButton>
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            Members
          </Typography>
        </Stack>
      </Stack>
      <Divider />
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          justifyContent: 'space-between',
        }}
        direction={{ xs: 'column', md: 'row' }}
        spacing={2}
        paddingInline={3}
        paddingBlock={2}
        alignItems={{ xs: 'stretch', md: 'center' }}
        justifyContent="flex-start"
      >
        <CustomFilterUserBox
          searchValue={searchValue}
          searchByValues={searchByValues.map(
            (item) => item.filterKey as string
          )}
          searchByLabel={searchByLabel}
          selectedSearchBy={searchBy}
          onHandleSearch={handleSearch}
          setSearchByValue={handleSearchByChange}
          searchPlaceHolder={`Search ${searchByLabel?.[searchBy] || searchBy || 'member'}`}
          prependSearchBy={true}
        />

        {showCompanyFilter && (
          <AutoCompleteOnFilter<Company>
            filterTypes={COMPANY_TYPES_COMBINED}
            options={companiesResponse.data || []}
            loading={isLoading}
            selectedFilterType={selectedCompanyType}
            selectedOption={selectedCompany}
            getOptionLabel={(company) => `${company.name} (${company.code})`}
            onFilterTypeChange={handleCompanyTypeChange}
            onOptionSelect={handleCompanySelect}
            onInputChange={handleCompanySearchChange}
            placeholder="Search by company type"
            noOptionsText={
              selectedCompanyType
                ? `No ${selectedCompanyType} companies found`
                : 'No companies found'
            }
            filterPlaceholder="Organization Type"
          />
        )}

        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USER}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddOutlinedIcon />}
            sx={{
              borderRadius: '8px',
              whiteSpace: 'nowrap',
              minWidth: 'fit-content',
            }}
            onClick={handleCreateUser}
          >
            Add Member
          </Button>
        </AccessControlWrapper>
      </Stack>
    </Stack>
  )
}

export default MemberHeader
