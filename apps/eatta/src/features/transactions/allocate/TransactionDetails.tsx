/**
 * <AUTHOR> on 09/06/2025
 */
import { Stack, Typography } from '@mui/material'
import { Transaction } from '@/store/interfaces/transactions'
import { FC } from 'react'
import { formatCurrency, formatDate } from '@dtbx/store/utils'

interface TransactionDetailsProps {
  transaction: Transaction
}

export const TransactionDetails: FC<TransactionDetailsProps> = ({
  transaction,
}) => {
  const {
    buyerName,
    amount,
    coreReference,
    dateCreated,
    sourceName,
    sourceAddress,
  } = transaction
  return (
    <Stack
      spacing={3}
      padding={3}
      sx={{
        border: '1px solid #D0D5DD',
        borderRadius: '6px',
      }}
    >
      <Typography
        variant="subtitle1"
        sx={{ color: '#101828', fontWeight: 600 }}
      >
        Transaction
      </Typography>

      <Stack
        direction="row"
        spacing={2}
        justifyContent="space-between"
        alignItems="center"
        flexWrap="wrap"
        useFlexGap
      >
        <Stack spacing={2}>
          <Typography
            variant="body2"
            sx={{
              color: '#475467',
              fontWeight: 600,
              fontSize: '0.875rem',
            }}
          >
            Date Received
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: '#101828',
              fontWeight: 600,
              fontSize: '1.125rem',
            }}
          >
            {formatDate(dateCreated ?? '')}
          </Typography>
        </Stack>

        <Stack spacing={2}>
          <Typography
            variant="body2"
            sx={{
              color: '#475467',
              fontWeight: 600,
              fontSize: '0.875rem',
            }}
          >
            Amount Received
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: '#101828',
              fontWeight: 600,
              fontSize: '1.125rem',
            }}
          >
            {formatCurrency(amount, 'USD', 'en-US')}
          </Typography>
        </Stack>

        <Stack spacing={2}>
          <Typography
            variant="body2"
            sx={{
              color: '#475467',
              fontWeight: 600,
            }}
          >
            Transaction Ref
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: '#101828',
              fontWeight: 600,
              fontSize: '1.125rem',
            }}
          >
            {coreReference}
          </Typography>
        </Stack>

        <Stack spacing={2}>
          <Typography
            variant="body2"
            sx={{
              color: '#475467',
              fontWeight: 600,
            }}
          >
            Buyer Name
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: '#101828',
              fontWeight: 600,
              fontSize: '1.125rem',
            }}
          >
            {buyerName ?? 'N/A'}
          </Typography>
        </Stack>
        <Stack spacing={2}>
          <Typography
            variant="body2"
            sx={{
              color: '#475467',
              fontWeight: 600,
            }}
          >
            Source Name
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: '#101828',
              fontWeight: 600,
              fontSize: '1.125rem',
            }}
          >
            {sourceName ?? 'N/A'}
          </Typography>
        </Stack>
        <Stack spacing={2}>
          <Typography
            variant="body2"
            sx={{
              color: '#475467',
              fontWeight: 600,
            }}
          >
            Source Address
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: '#101828',
              fontWeight: 600,
              fontSize: '1.125rem',
            }}
          >
            {sourceAddress ?? 'N/A'}
          </Typography>
        </Stack>
      </Stack>
    </Stack>
  )
}
