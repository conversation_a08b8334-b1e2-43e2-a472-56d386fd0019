import { vi, expect, describe, it, beforeEach, Mock } from 'vitest'
import * as apiFunctions from '@/store/actions'
import { setNotification } from '@dtbx/store/reducers'
import { secureapi2, downloadBlob } from '@dtbx/store/utils'
import {
  setLoadingReports,
  setReportPaginationData,
  setIFTReportList,
  setRTGSReportList,
  setPesalinkReportList,
  setC2BReportList,
  setCustomerReportList,
  setLoadingExportReport,
  setLoadingReportsHistory,
  setReportHistoryList,
  setReportHistoryPaginationData,
  setSearchingReportsHistory,
  setLoadingSaveReport,
} from '@/store/reducers'
import {
  CUSTOMER_REPORT_TYPES,
  CustomerReportTypes,
  TRANSACTION_REPORT_TYPES,
  TransactionReportTypes,
} from '@/store/interfaces'

const mockSecureApi2 = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  patch: vi.fn(),
}

vi.mock('@dtbx/store/utils', () => ({
  secureapi2: mockSecureApi2,
}))
beforeEach(() => {
  vi.clearAllMocks()
})
describe('getReports', () => {
  it('should dispatch setIFTReportList when reportType is ift', async () => {
    const dispatch = vi.fn()
    const mockResponse = {
      data: {
        data: [{ id: 1, amount: 100 }],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReports(dispatch, 'ift')

    expect(secureapi2.get).toHaveBeenCalledWith('payment/payments/ift')
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(true))
    expect(dispatch).toHaveBeenCalledWith(
      setReportPaginationData({
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      })
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(false))
    expect(dispatch).toHaveBeenCalledWith(
      setIFTReportList([{ id: 1, amount: 100 }])
    )
  })
  it('should dispatch setRTGSReportList when reportType is rtgs', async () => {
    const dispatch = vi.fn()
    const mockResponse = {
      data: {
        data: [{ id: 2, amount: 200 }],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReports(dispatch, 'rtgs')

    expect(secureapi2.get).toHaveBeenCalledWith('payment/payments/rtgs')
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(true))
    expect(dispatch).toHaveBeenCalledWith(
      setReportPaginationData({
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      })
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(false))
    expect(dispatch).toHaveBeenCalledWith(
      setRTGSReportList([{ id: 2, amount: 200 }])
    )
  })
  it('should dispatch setPesalinkReportList when reportType is pesalink', async () => {
    const dispatch = vi.fn()
    const mockResponse = {
      data: {
        data: [{ id: 3, amount: 300 }],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReports(dispatch, 'pesalink')

    expect(secureapi2.get).toHaveBeenCalledWith('payment/payments/pesalink')
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(true))
    expect(dispatch).toHaveBeenCalledWith(
      setReportPaginationData({
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      })
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(false))
    expect(dispatch).toHaveBeenCalledWith(
      setPesalinkReportList([{ id: 3, amount: 300 }])
    )
  })
  it('should dispatch setC2BReportList when reportType is mpesa/c2b', async () => {
    const dispatch = vi.fn()
    const mockResponse = {
      data: {
        data: [{ id: 4, amount: 400 }],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReports(dispatch, 'mpesa/c2b')

    expect(secureapi2.get).toHaveBeenCalledWith('payment/payments/mpesa/c2b')
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(true))
    expect(dispatch).toHaveBeenCalledWith(
      setReportPaginationData({
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      })
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(false))
    expect(dispatch).toHaveBeenCalledWith(
      setC2BReportList([{ id: 4, amount: 400 }])
    )
  })
  it('should dispatch error notification when API call fails', async () => {
    const dispatch = vi.fn()
    const error = new Error('Network error')
    vi.spyOn(secureapi2, 'get').mockRejectedValue(error)

    await apiFunctions.getReports(dispatch, 'ift')

    expect(secureapi2.get).toHaveBeenCalledWith('payment/payments/ift')
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(true))
    expect(dispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'Network error',
        type: 'error',
      })
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(false))
  })
  it('should not dispatch any report list when reportType is unknown', async () => {
    const dispatch = vi.fn()
    const mockResponse = {
      data: {
        data: [{ id: 5, amount: 500 }],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReports(dispatch, 'unknown' as TransactionReportTypes)

    expect(secureapi2.get).toHaveBeenCalledWith('payment/payments/unknown')
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(true))
    expect(dispatch).toHaveBeenCalledWith(
      setReportPaginationData({
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      })
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(false))
    expect(dispatch).not.toHaveBeenCalledWith(
      expect.objectContaining({
        type: expect.stringMatching(
          /setIFTReportList|setRTGSReportList|setPesalinkReportList|setC2BReportList|setMpesaAccountTopUpReportList|setPayBillReportList|setBuyAirtimeReportList/
        ),
      })
    )
  })
  it('should handle empty response data', async () => {
    const dispatch = vi.fn()
    const mockResponse = {
      data: {
        data: [],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 0,
        totalNumberOfPages: 0,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReports(dispatch, 'ift')

    expect(secureapi2.get).toHaveBeenCalledWith('payment/payments/ift')
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(true))
    expect(dispatch).toHaveBeenCalledWith(
      setReportPaginationData({
        pageNumber: 1,
        pageSize: 10,
        totalElements: 0,
        totalNumberOfPages: 0,
      })
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReports(false))
    expect(dispatch).toHaveBeenCalledWith(setIFTReportList([]))
  })
})

describe('getCustomerReports', () => {
  it('should fetch customer reports and dispatch data successfully', async () => {
    const dispatch = vi.fn()
    const eventType = 'ACCOUNT_OPENING' as CustomerReportTypes
    const params = '?startDate=2023-01-01&endDate=2023-01-31'
    const mockResponse = {
      data: {
        data: [{ id: 1, name: 'Test Customer' }],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 100,
        totalNumberOfPages: 10,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getCustomerReports(dispatch, eventType, params)

    expect(secureapi2.get).toHaveBeenCalledWith(
      '/dbp/customers/event-details?startDate=2023-01-01&endDate=2023-01-31&eventType=ACCOUNT_OPENING'
    )
    expect(dispatch).toHaveBeenCalledWith(
      setCustomerReportList(mockResponse.data.data)
    )
  })
  it('should set loading state to true at start and false at end', async () => {
    const dispatch = vi.fn()
    const eventType = 'ACCOUNT_OPENING' as CustomerReportTypes
    const params = '?page=1'
    const mockResponse = {
      data: {
        data: [],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 0,
        totalNumberOfPages: 0,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getCustomerReports(dispatch, eventType, params)

    expect(dispatch).toHaveBeenNthCalledWith(1, setLoadingReports(true))
    expect(dispatch).toHaveBeenLastCalledWith(setLoadingReports(false))
  })
  it('should correctly construct query parameters with eventType', async () => {
    const dispatch = vi.fn()
    const eventType = 'ACCOUNT_CLOSURE' as CustomerReportTypes
    const params = '?startDate=2023-01-01&endDate=2023-01-31&page=1'
    const mockResponse = {
      data: {
        data: [],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 0,
        totalNumberOfPages: 0,
      },
    }
    const getSpy = vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getCustomerReports(dispatch, eventType, params)

    expect(getSpy).toHaveBeenCalledWith(
      '/dbp/customers/event-details?startDate=2023-01-01&endDate=2023-01-31&page=1&eventType=ACCOUNT_CLOSURE'
    )
  })
  it('should extract pagination data from response and dispatch it', async () => {
    const dispatch = vi.fn()
    const eventType = 'ACCOUNT_OPENING' as CustomerReportTypes
    const params = '?page=1'
    const paginationData = {
      pageNumber: 2,
      pageSize: 15,
      totalElements: 45,
      totalNumberOfPages: 3,
    }
    const mockResponse = {
      data: {
        data: [],
        ...paginationData,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getCustomerReports(dispatch, eventType, params)

    expect(dispatch).toHaveBeenCalledWith(
      setReportPaginationData(paginationData)
    )
  })
  it('should dispatch error notification when API call fails', async () => {
    const dispatch = vi.fn()
    const eventType = 'ACCOUNT_OPENING' as CustomerReportTypes
    const params = '?page=1'
    const error = new Error('Network error')
    vi.spyOn(secureapi2, 'get').mockRejectedValue(error)

    await apiFunctions.getCustomerReports(dispatch, eventType, params)

    expect(dispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'Network error',
        type: 'error',
      })
    )
  })
  it('should set loading state to false even when API call fails', async () => {
    const dispatch = vi.fn()
    const eventType = 'ACCOUNT_OPENING' as CustomerReportTypes
    const params = '?page=1'
    const error = new Error('API error')
    vi.spyOn(secureapi2, 'get').mockRejectedValue(error)

    await apiFunctions.getCustomerReports(dispatch, eventType, params)

    expect(dispatch).toHaveBeenNthCalledWith(1, setLoadingReports(true))
    expect(dispatch).toHaveBeenLastCalledWith(setLoadingReports(false))
  })
  it('should handle empty response data correctly', async () => {
    const dispatch = vi.fn()
    const eventType = 'ACCOUNT_OPENING' as CustomerReportTypes
    const params = '?page=1'
    const mockResponse = {
      data: {
        data: [],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 0,
        totalNumberOfPages: 0,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getCustomerReports(dispatch, eventType, params)

    expect(dispatch).toHaveBeenCalledWith(setCustomerReportList([]))
    expect(dispatch).not.toHaveBeenCalledWith(
      setNotification(expect.objectContaining({ type: 'error' }))
    )
  })
})
describe('exportReportByType', () => {
  it('should successfully export a transaction report and download Excel file', async () => {
    const mockDispatch = vi.fn()
    const mockReportType = TRANSACTION_REPORT_TYPES[0]
    const mockParams = '?startDate=2023-01-01&endDate=2023-01-31'
    const mockResponse = { data: new Uint8Array([1, 2, 3]) }

    vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)
    global.Blob = vi
      .fn()
      .mockImplementation((content, options) => ({ content, options }))
    vi.spyOn(global, 'Blob')
    vi.mock('@dtbx/store/utils', () => ({
      downloadBlob: vi.fn(),
      secureapi2: { get: vi.fn() },
    }))

    await apiFunctions.exportReportByType(
      mockDispatch,
      mockReportType,
      mockParams
    )

    expect(secureapi2.get).toHaveBeenCalledWith(
      `/reports/${mockReportType}/export-to-excel${mockParams}`,
      { responseType: 'blob' }
    )
    expect(Blob).toHaveBeenCalledWith([mockResponse.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    expect(downloadBlob).toHaveBeenCalled()
  })
  it('should successfully export a customer report and download Excel file', async () => {
    const mockDispatch = vi.fn()
    const mockReportType = CUSTOMER_REPORT_TYPES[0]
    const mockResponse = { data: new Uint8Array([1, 2, 3]) }

    vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)
    global.Blob = vi
      .fn()
      .mockImplementation((content, options) => ({ content, options }))
    vi.spyOn(global, 'Blob')
    vi.mock('@dtbx/store/utils', () => ({
      downloadBlob: vi.fn(),
      secureapi2: { get: vi.fn() },
    }))

    await apiFunctions.exportReportByType(mockDispatch, mockReportType)

    expect(secureapi2.get).toHaveBeenCalledWith(
      `/reports/customers-events/export-to-excel?eventType=${mockReportType}`,
      { responseType: 'blob' }
    )
    expect(Blob).toHaveBeenCalledWith([mockResponse.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    expect(downloadBlob).toHaveBeenCalled()
  })
  it('should dispatch loading state at start and end of successful export', async () => {
    const mockDispatch = vi.fn()
    const mockReportType = TRANSACTION_REPORT_TYPES[0]
    const mockResponse = { data: new Uint8Array([1, 2, 3]) }

    vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)
    vi.mock('@dtbx/store/utils', () => ({
      downloadBlob: vi.fn(),
      secureapi2: { get: vi.fn() },
    }))

    await apiFunctions.exportReportByType(mockDispatch, mockReportType)

    expect(mockDispatch).toHaveBeenNthCalledWith(
      1,
      setLoadingExportReport(true)
    )
    expect(mockDispatch).toHaveBeenLastCalledWith(setLoadingExportReport(false))
    expect(mockDispatch).toHaveBeenCalledTimes(3) // Loading start, notification, loading end
  })
  it('should dispatch success notification with correct report type message', async () => {
    const mockDispatch = vi.fn()
    const mockReportType = TRANSACTION_REPORT_TYPES[0]
    const mockResponse = { data: new Uint8Array([1, 2, 3]) }

    vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)
    vi.mock('@dtbx/store/utils', () => ({
      downloadBlob: vi.fn(),
      secureapi2: { get: vi.fn() },
    }))

    await apiFunctions.exportReportByType(mockDispatch, mockReportType)

    expect(mockDispatch).toHaveBeenNthCalledWith(
      2,
      setNotification({
        message: ` ${mockReportType} report generated successfully`,
        type: 'success',
      })
    )
  })
  it('should handle API errors by dispatching error notification', async () => {
    const mockDispatch = vi.fn()
    const mockReportType = TRANSACTION_REPORT_TYPES[0]
    const mockError = new Error('API error occurred')

    vi.spyOn(secureapi2, 'get').mockRejectedValueOnce(mockError)

    await apiFunctions.exportReportByType(mockDispatch, mockReportType)

    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({
        message: mockError.message,
        type: 'error',
      })
    )
  })
  it('should handle undefined params parameter correctly', async () => {
    const mockDispatch = vi.fn()
    const mockReportType = TRANSACTION_REPORT_TYPES[0]
    const mockResponse = { data: new Uint8Array([1, 2, 3]) }

    vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)
    vi.mock('@dtbx/store/utils', () => ({
      downloadBlob: vi.fn(),
      secureapi2: { get: vi.fn() },
    }))

    await apiFunctions.exportReportByType(
      mockDispatch,
      mockReportType,
      undefined
    )

    expect(secureapi2.get).toHaveBeenCalledWith(
      `/reports/${mockReportType}/export-to-excel`,
      { responseType: 'blob' }
    )
    expect(downloadBlob).toHaveBeenCalled()
  })
  it('should handle empty response data correctly', async () => {
    const mockDispatch = vi.fn()
    const mockReportType = TRANSACTION_REPORT_TYPES[0]
    const mockResponse = { data: new Uint8Array([]) }

    vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)
    global.Blob = vi.fn().mockImplementation((content, options) => ({
      content,
      options,
    }))
    vi.spyOn(global, 'Blob')
    vi.mock('@dtbx/store/utils', () => ({
      downloadBlob: vi.fn(),
      secureapi2: { get: vi.fn() },
    }))

    await apiFunctions.exportReportByType(mockDispatch, mockReportType)

    expect(Blob).toHaveBeenCalledWith([mockResponse.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    expect(downloadBlob).toHaveBeenCalled()
    expect(mockDispatch).toHaveBeenCalledWith(
      setNotification({
        message: ` ${mockReportType} report generated successfully`,
        type: 'success',
      })
    )
  })
  it('should reset loading state even when error occurs', async () => {
    const mockDispatch = vi.fn()
    const mockReportType = TRANSACTION_REPORT_TYPES[0]
    const mockError = new Error('API error occurred')

    vi.spyOn(secureapi2, 'get').mockRejectedValueOnce(mockError)

    await apiFunctions.exportReportByType(mockDispatch, mockReportType)

    expect(mockDispatch).toHaveBeenNthCalledWith(
      1,
      setLoadingExportReport(true)
    )
    expect(mockDispatch).toHaveBeenLastCalledWith(setLoadingExportReport(false))
    expect(mockDispatch).toHaveBeenCalledTimes(3) // Loading start, error notification, loading end
  })
})
// REPORT HISTORY TESTS
describe('getReportsHistory', () => {
  it('should fetch report history with default parameters', async () => {
    const dispatch = vi.fn()
    const mockResponse = {
      data: {
        data: [{ id: 1, name: 'Report 1' }],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 20,
        totalNumberOfPages: 2,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReportsHistory(dispatch)

    expect(secureapi2.get).toHaveBeenCalledWith(
      '/reports/report-filters?page=1&pageSize=10'
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReportsHistory(true))
    expect(dispatch).toHaveBeenCalledWith(
      setReportHistoryPaginationData({
        pageNumber: 1,
        pageSize: 10,
        totalElements: 20,
        totalNumberOfPages: 2,
      })
    )
    expect(dispatch).toHaveBeenCalledWith(
      setReportHistoryList([{ id: 1, name: 'Report 1' }])
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReportsHistory(false))
  })
  it('should set loading state when not searching', async () => {
    const dispatch = vi.fn()
    const mockResponse = {
      data: {
        data: [],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 0,
        totalNumberOfPages: 0,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReportsHistory(dispatch)

    expect(dispatch).toHaveBeenNthCalledWith(1, setLoadingReportsHistory(true))
    expect(dispatch).not.toHaveBeenCalledWith(setSearchingReportsHistory(true))
    expect(dispatch).toHaveBeenCalledWith(setLoadingReportsHistory(false))
    expect(dispatch).not.toHaveBeenCalledWith(setSearchingReportsHistory(false))
  })
  it('should set searching state when reportName is provided', async () => {
    const dispatch = vi.fn()
    const reportName = 'Test Report'
    const mockResponse = {
      data: {
        data: [],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 0,
        totalNumberOfPages: 0,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReportsHistory(dispatch, reportName)

    expect(dispatch).toHaveBeenNthCalledWith(
      1,
      setSearchingReportsHistory(true)
    )
    expect(dispatch).not.toHaveBeenCalledWith(setLoadingReportsHistory(true))
    expect(dispatch).toHaveBeenCalledWith(setSearchingReportsHistory(false))
    expect(dispatch).not.toHaveBeenCalledWith(setLoadingReportsHistory(false))
  })
  it('should construct API URL with correct pagination parameters', async () => {
    const dispatch = vi.fn()
    const page = 3
    const size = 25
    const mockResponse = {
      data: {
        data: [],
        pageNumber: 3,
        pageSize: 25,
        totalElements: 100,
        totalNumberOfPages: 4,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReportsHistory(dispatch, undefined, page, size)

    expect(secureapi2.get).toHaveBeenCalledWith(
      '/reports/report-filters?page=3&pageSize=25'
    )
    expect(dispatch).toHaveBeenCalledWith(
      setReportHistoryPaginationData({
        pageNumber: 3,
        pageSize: 25,
        totalElements: 100,
        totalNumberOfPages: 4,
      })
    )
  })
  it('should handle empty reportName parameter', async () => {
    const dispatch = vi.fn()
    const reportName = ''
    const mockResponse = {
      data: {
        data: [],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 0,
        totalNumberOfPages: 0,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReportsHistory(dispatch, reportName)

    expect(secureapi2.get).toHaveBeenCalledWith(
      '/reports/report-filters?page=1&pageSize=10'
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReportsHistory(true))
    expect(dispatch).not.toHaveBeenCalledWith(setSearchingReportsHistory(true))
  })
  it('should dispatch error notification when API call fails', async () => {
    const dispatch = vi.fn()
    const error = new Error('Network error')
    vi.spyOn(secureapi2, 'get').mockRejectedValue(error)

    await apiFunctions.getReportsHistory(dispatch)

    expect(dispatch).toHaveBeenCalledWith(setLoadingReportsHistory(true))
    expect(dispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'Network error',
        type: 'error',
      })
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingReportsHistory(false))
  })
  it('should clear loading states even when API call fails', async () => {
    const dispatch = vi.fn()
    const reportName = 'Test Report'
    const error = new Error('API error')
    vi.spyOn(secureapi2, 'get').mockRejectedValue(error)

    await apiFunctions.getReportsHistory(dispatch, reportName)

    expect(dispatch).toHaveBeenNthCalledWith(
      1,
      setSearchingReportsHistory(true)
    )
    expect(dispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'API error',
        type: 'error',
      })
    )
    expect(dispatch).toHaveBeenLastCalledWith(setSearchingReportsHistory(false))
  })
  it('should handle different page and size parameters with reportName', async () => {
    const dispatch = vi.fn()
    const reportName = 'Monthly Report'
    const page = 2
    const size = 15
    const mockResponse = {
      data: {
        data: [{ id: 2, name: 'Monthly Report' }],
        pageNumber: 2,
        pageSize: 15,
        totalElements: 30,
        totalNumberOfPages: 2,
      },
    }
    vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getReportsHistory(dispatch, reportName, page, size)

    expect(secureapi2.get).toHaveBeenCalledWith(
      '/reports/report-filters?page=2&pageSize=15&reportName=Monthly Report'
    )
    expect(dispatch).toHaveBeenCalledWith(setSearchingReportsHistory(true))
    expect(dispatch).toHaveBeenCalledWith(
      setReportHistoryPaginationData({
        pageNumber: 2,
        pageSize: 15,
        totalElements: 30,
        totalNumberOfPages: 2,
      })
    )
    expect(dispatch).toHaveBeenCalledWith(
      setReportHistoryList([{ id: 2, name: 'Monthly Report' }])
    )
    expect(dispatch).toHaveBeenCalledWith(setSearchingReportsHistory(false))
  })
})
describe.skip('postReportFilters', () => {
  it('should post report filters and dispatch success notification when API call succeeds', async () => {
    const dispatch = vi.fn()
    const payload = {
      reportName: 'TestReport',
      reportFilters: [{ filter1: 'value1' }],
    }
    const mockResponse = {
      data: {
        data: [{ id: 1, name: 'Saved Filter' }],
      },
    }
    // vi.spyOn(secureapi2, 'post').mockResolvedValue(mockResponse)
    // mockSecureApi2.post.mockResolvedValue(mockResponse)
    ;(secureapi2.post as Mock).mockResolvedValue(mockResponse)

    await apiFunctions.postReportFilters(dispatch, payload)

    expect(mockSecureApi2.post).toHaveBeenCalledWith(
      '/reports/report-filters',
      payload
    )
    expect(secureapi2).toBe(mockSecureApi2)
    console.log('Secure api 2', secureapi2)
    console.log('Mock Secure api 2', mockSecureApi2)
    expect(dispatch).toHaveBeenCalledWith(
      setReportHistoryList(mockResponse.data.data)
    )
    expect(dispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'Report filters saved successfully',
        type: 'success',
      })
    )
  })
  it('should dispatch setLoadingSaveReport(true) at the beginning of execution', async () => {
    const dispatch = vi.fn()
    const payload = {
      reportName: 'TestReport',
      reportFilters: [{ filter1: 'value1' }],
    }

    // mockSecureApi2.post.mockResolvedValue({
    //   data: { data: [] },
    // })
    vi.spyOn(secureapi2, 'get').mockResolvedValue({ data: { data: [] } })

    await apiFunctions.postReportFilters(dispatch, payload)

    expect(dispatch).toHaveBeenNthCalledWith(1, setLoadingSaveReport(true))
  })
  it('should dispatch setReportHistoryList with correct response data', async () => {
    const dispatch = vi.fn()
    const payload = {
      reportName: 'TestReport',
      reportFilters: [{ filter1: 'value1' }],
    }
    const historyData = [
      { id: 1, name: 'Filter1' },
      { id: 2, name: 'Filter2' },
    ]
    const mockResponse = {
      data: {
        data: historyData,
      },
    }
    mockSecureApi2.post.mockResolvedValue(mockResponse)

    await apiFunctions.postReportFilters(dispatch, payload)

    expect(dispatch).toHaveBeenCalledWith(setReportHistoryList(historyData))
  })
  it('should dispatch setLoadingSaveReport(false) after successful API call', async () => {
    const dispatch = vi.fn()
    const payload = {
      reportName: 'TestReport',
      reportFilters: [{ filter1: 'value1' }],
    }
    mockSecureApi2.post.mockResolvedValue({
      data: { data: [] },
    })

    await apiFunctions.postReportFilters(dispatch, payload)

    const lastCall = dispatch.mock.calls.length
    expect(dispatch).toHaveBeenNthCalledWith(
      lastCall,
      setLoadingSaveReport(false)
    )
  })
  it('should dispatch error notification when API call fails', async () => {
    const dispatch = vi.fn()
    const payload = {
      reportName: 'TestReport',
      reportFilters: [{ filter1: 'value1' }],
    }
    const errorMessage = 'API request failed'
    mockSecureApi2.post.mockRejectedValue(new Error(errorMessage))

    await apiFunctions.postReportFilters(dispatch, payload)

    expect(dispatch).toHaveBeenCalledWith(
      setNotification({
        message: errorMessage,
        type: 'error',
      })
    )
  })

  it('should dispatch setLoadingSaveReport(false) even when API call fails', async () => {
    const dispatch = vi.fn()
    const payload = {
      reportName: 'TestReport',
      reportFilters: [{ filter1: 'value1' }],
    }
    mockSecureApi2.post.mockRejectedValue(new Error('API error'))

    await apiFunctions.postReportFilters(dispatch, payload)

    const lastCall = dispatch.mock.calls.length
    expect(dispatch).toHaveBeenNthCalledWith(
      lastCall,
      setLoadingSaveReport(false)
    )
  })

  it('should handle empty reportFilters array in payload', async () => {
    const dispatch = vi.fn()
    const payload = {
      reportName: 'TestReport',
      reportFilters: [],
    }
    mockSecureApi2.post.mockResolvedValue({
      data: { data: [] },
    })

    await apiFunctions.postReportFilters(dispatch, payload)

    expect(mockSecureApi2.post).toHaveBeenCalledWith(
      '/reports/report-filters',
      payload
    )
    expect(dispatch).toHaveBeenCalledWith(setReportHistoryList([]))
    expect(dispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'Report filters saved successfully',
        type: 'success',
      })
    )
  })
  it('should handle malformed response data from API', async () => {
    const dispatch = vi.fn()
    const payload = {
      reportName: 'TestReport',
      reportFilters: [{ filter1: 'value1' }],
    }
    const malformedResponse = { data: null }
    mockSecureApi2.post.mockResolvedValue(malformedResponse)

    await apiFunctions.postReportFilters(dispatch, payload)

    expect(dispatch).toHaveBeenCalledWith(setReportHistoryList(undefined))
    expect(dispatch).toHaveBeenCalledWith(
      setNotification({
        message: 'Report filters saved successfully',
        type: 'success',
      })
    )
    expect(dispatch).toHaveBeenCalledWith(setLoadingSaveReport(false))
  })
})
describe.skip('isTransactionReportType', () => {})
describe.skip('isCustomerReportType', () => {})
