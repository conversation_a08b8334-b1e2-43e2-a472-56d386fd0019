'use client'

import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Stack,
  TextField,
  Typography,
  Link,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import React from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import Grid from '@mui/material/Grid2'
import {
  ChevronLeftIcon,
  EmailIcon,
  PhoneIcon,
} from '@dtbx/ui/components/SvgIcons'
import { COMPANY_TYPES_COMBINED } from '@/store/interfaces'
import { FileUpload } from '@/components/FileUpload'
import { handleCustomerSupport } from '@/store/actions'
import { ALPHA_NUMERIC_REGEX } from '@/utils/validators'

const validationSchema = Yup.object({
  membershipType: Yup.string().required('Membership type is required'),
  organizationCode: Yup.string()
    .required('Organization Code is required')
    .matches(ALPHA_NUMERIC_REGEX, 'Only alphanumeric characters are allowed'),
  email: Yup.string().trim().email('Enter valid email').nullable(),
  phone: Yup.string()
    .trim()
    .matches(/^[0-9+\-\s]{13}$/, 'Invalid phone number format')
    .nullable(),
  issueDescription: Yup.string().required('Please describe your issue'),
  file: Yup.mixed<File>().optional(),
}).test(
  'email-or-phone-required',
  'Please provide at least an email or a phone number',
  function (value) {
    const hasEmail = !!value.email?.trim()
    const hasPhone = !!value.phone?.trim()
    return hasEmail || hasPhone
  }
)

export default function CustomerSupportPage() {
  const dispatch = useAppDispatch()

  const { isLoadingLogin } = useAppSelector((store) => store.auth)
  const [fileUploadKey, setFileUploadKey] = React.useState(Date.now())
  const formik = useFormik({
    initialValues: {
      membershipType: '',
      organizationCode: '',
      email: '',
      phone: '',
      issueDescription: '',
      file: File,
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      const file = values.file instanceof File ? values.file : null

      await handleCustomerSupport(
        dispatch,
        {
          membershipType: values.membershipType,
          organizationCode: values.organizationCode,
          email: values.email,
          // phone:values.phone,
          issueDescription: values.issueDescription,
          file: file as File,
        },
        {},
        handleSubmitSuccess
      )
    },
  })

  const handleSubmitSuccess = () => {
    formik.resetForm()
    setFileUploadKey(Date.now())
  }

  return (
    <FormikProvider value={formik}>
      <Form onSubmit={formik.handleSubmit}>
        <Grid container spacing={2}>
          <Grid
            size={{ xs: 12, sm: 12, md: 12, lg: 8 }}
            sx={{
              border: '1px solid #ccc',
              borderRadius: '8px',
              padding: 2,
            }}
          >
            <Link
              href="/auth"
              rel="noopener noreferrer"
              sx={{ textDecoration: 'none', display: 'inline-flex' }}
            >
              <Typography
                variant="body2"
                fontWeight={600}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  gap: 1,
                  marginBottom: 1,
                  color: '#D92D20',
                }}
              >
                <ChevronLeftIcon fontSize="small" />
                Back to login
              </Typography>
            </Link>

            <Stack spacing={3}>
              <Typography variant="h5" fontWeight="600">
                Send us a message:
              </Typography>

              <Box>
                <Typography variant="body2" fontWeight={500}>
                  Type of Membership
                </Typography>
                <Autocomplete
                  disablePortal
                  size="small"
                  id="membershipType"
                  options={COMPANY_TYPES_COMBINED}
                  {...formik.getFieldProps('membershipType')}
                  onChange={(_, value) => {
                    formik.setFieldValue('membershipType', value)
                  }}
                  renderInput={(params) => (
                    <TextField
                      hiddenLabel
                      placeholder="Select type"
                      {...params}
                      error={Boolean(
                        formik.touched.membershipType &&
                          formik.errors.membershipType
                      )}
                      helperText={
                        formik.touched.membershipType &&
                        formik.errors.membershipType
                          ? formik.errors.membershipType
                          : ''
                      }
                    />
                  )}
                />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={500}>
                  EATTA Organization Code:
                </Typography>
                <TextField
                  fullWidth
                  size="small"
                  {...formik.getFieldProps('organizationCode')}
                  error={Boolean(
                    formik.touched.organizationCode &&
                      formik.errors.organizationCode
                  )}
                  helperText={
                    formik.touched.organizationCode &&
                    formik.errors.organizationCode
                  }
                />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={500} mb={1}>
                  Enter your phone number or email
                </Typography>

                <Grid container spacing={2}>
                  <Grid size={{ xs: 12, sm: 6, md: 6, lg: 6 }}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Email"
                      placeholder="e.g. <EMAIL>"
                      {...formik.getFieldProps('email')}
                      error={Boolean(
                        formik.touched.email && formik.errors.email
                      )}
                      helperText={formik.touched.email && formik.errors.email}
                    />
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6, md: 6, lg: 6 }}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Phone Number"
                      placeholder="e.g. +254712345678"
                      {...formik.getFieldProps('phone')}
                      error={Boolean(
                        formik.touched.phone && formik.errors.phone
                      )}
                      helperText={formik.touched.phone && formik.errors.phone}
                    />
                  </Grid>
                </Grid>

                <Typography
                  variant="caption"
                  color="text.secondary"
                  mt={1}
                  display="block"
                >
                  Provide at least one so we can contact you.
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={500}>
                  Describe your issue
                </Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  size="small"
                  placeholder="Enter description..."
                  {...formik.getFieldProps('issueDescription')}
                  error={Boolean(
                    formik.touched.issueDescription &&
                      formik.errors.issueDescription
                  )}
                  helperText={
                    formik.touched.issueDescription &&
                    formik.errors.issueDescription
                  }
                />
              </Box>

              <Box>
                <FileUpload
                  key={fileUploadKey}
                  progress={0}
                  description="PDF or PNG, JPG image allowed."
                  mimeTypes={['application/pdf', 'image/png', 'image/jpeg']}
                  required={false}
                  onFileChange={(file) => formik.setFieldValue('file', file)}
                />
              </Box>

              <Button
                variant="contained"
                type="submit"
                disabled={!formik.isValid || isLoadingLogin}
                endIcon={
                  isLoadingLogin ? (
                    <CircularProgress size={20} thickness={3} />
                  ) : undefined
                }
              >
                Submit
              </Button>
            </Stack>
          </Grid>

          <Grid size={{ xs: 12, sm: 12, md: 12, lg: 4 }}>
            <Stack spacing={2}>
              <Typography variant="h6" color="primary" fontWeight={600}>
                Or Talk to Us Directly
              </Typography>

              <EmailIcon />
              <Stack direction="row" alignItems="flex-start" spacing={2}>
                <Box>
                  <Typography
                    variant="body1"
                    color="text.secondary"
                    fontWeight={500}
                  >
                    EMAIL
                  </Typography>
                  <Typography variant="body1" color="primary">
                    <EMAIL>
                  </Typography>
                </Box>
              </Stack>

              <PhoneIcon />
              <Stack direction="row" alignItems="flex-start" spacing={2}>
                <Box>
                  <Typography
                    variant="body1"
                    color="text.secondary"
                    fontWeight={500}
                  >
                    PHONE NUMBER
                  </Typography>
                  <Typography variant="body1" color="primary">
                    0719 031 888 / 0732 121 888
                  </Typography>
                </Box>
              </Stack>
            </Stack>
          </Grid>
        </Grid>
      </Form>
    </FormikProvider>
  )
}
