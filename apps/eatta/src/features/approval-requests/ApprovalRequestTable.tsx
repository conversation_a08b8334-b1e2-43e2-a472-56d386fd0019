'use client'

import React, { useEffect, useState } from 'react'
import {
  Chip,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { ChipProps, StatusChip } from '@dtbx/ui/components/Chip'
import { RequestMoreMenu } from './MoreMenu'
import { getApprovalRequests } from '@/store/actions/ApprovalRequests'
import { EATTA_MODULES } from '@/utils/constants'
import { useAppDispatch } from '@/store'
import {
  ApprovalRequestFilters,
  ApprovalRequestStatus,
} from '@/store/interfaces/makerChecker'
import { useAppSelector } from '@/store'
import TableSkeleton from '@/components/TableSkeleton'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { sentenceCase } from 'tiny-case'
import { formatTimestamp } from '@dtbx/store/utils'
import { useResetPageOnFilterChange } from '@/hooks/useResetPageOnFilterChange'

export const RequestChip = styled(Chip)<ChipProps>(() => ({
  padding: '2px 8px',
  borderRadius: '16px',
  background: '#F3F5F5',
  height: '24px',
  width: 'auto',
  minWidth: '0',
}))

interface ApprovalRequestsTableProps {
  status: ApprovalRequestStatus
  filters: ApprovalRequestFilters
}
const ApprovalRequestsTable: React.FC<ApprovalRequestsTableProps> = ({
  status,
  filters,
}) => {
  const dispatch = useAppDispatch()
  const [order, setOrder] = useState<'asc' | 'desc'>('desc')
  const [orderBy, setOrderBy] = useState<string>('dateCreated')
  const { approvalRequests, isLoadingApprovals } = useAppSelector(
    (state) => state.approvals
  )
  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }

  const resetPageRef = useResetPageOnFilterChange(
    filters,
    paginationOptions,
    setPaginationOptions
  )

  useEffect(() => {
    if (resetPageRef.current) {
      resetPageRef.current = false
      return
    }
    const fetchApprovals = async () => {
      await getApprovalRequests(dispatch, {
        ...filters,
        status: status,
        page: paginationOptions.page,
        size: paginationOptions.size,
        channel: 'EATTA',
      })
    }
    fetchApprovals()
  }, [dispatch, filters, paginationOptions.page, status])

  const APPROVALS_HEADER = [
    { id: 'requestType', label: 'Request Type', alignRight: false },
    { id: 'maker', label: 'Maker', alignRight: false },
    { id: 'makerTimestamp', label: 'Maker Timestamp', alignRight: false },
  ]

  if (status === 'APPROVED') {
    APPROVALS_HEADER.push({
      id: 'status',
      label: 'Status',
      alignRight: false,
    })
  } else {
    APPROVALS_HEADER.push({
      id: 'action',
      label: 'Action',
      alignRight: false,
    })
  }

  return isLoadingApprovals ? (
    <TableSkeleton rowCount={15} columnCount={4} />
  ) : approvalRequests.data.length === 0 ? (
    <EmptyPage title="No requests found" bgUrl={'/eatta/combo.svg'} />
  ) : (
    <TableContainer
      component={Paper}
      sx={{
        boxShadow: 'none',
        '& .MuiTableCell-root': {
          paddingInline: '1.5rem',
          paddingBlock: '0.5rem',
          textAlign: 'left',
        },
      }}
    >
      <Table
        sx={{ minWidth: 650 }}
        aria-label="approval requests table"
        size="small"
      >
        <CustomTableHeader
          order={order}
          orderBy={orderBy}
          headLabel={APPROVALS_HEADER}
          showCheckbox={false}
          rowCount={approvalRequests.data.length}
          numSelected={0}
        />
        <TableBody>
          {approvalRequests.data.map((row, index) => (
            <TableRow hover key={index} tabIndex={-1}>
              <TableCell>
                <RequestChip
                  label={sentenceCase(row.makerCheckerType.type)}
                  sx={{ width: 'auto' }}
                />
              </TableCell>
              <TableCell>{row.maker}</TableCell>
              <TableCell>{formatTimestamp(row.dateCreated)}</TableCell>
              {status === 'APPROVED' && (
                <TableCell>
                  <StatusChip status="success" label={row.status} />
                </TableCell>
              )}
              {status !== 'APPROVED' && (
                <TableCell>
                  <RequestMoreMenu request={row} />
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell
              sx={{ paddingInline: 0 }}
              align="center"
              height={40}
              colSpan={12}
            >
              {approvalRequests.totalNumberOfPages > 0 && (
                <CustomPagination
                  options={{
                    ...paginationOptions,
                    totalPages: approvalRequests.totalNumberOfPages,
                  }}
                  handlePagination={handlePagination}
                />
              )}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  )
}

export default ApprovalRequestsTable
