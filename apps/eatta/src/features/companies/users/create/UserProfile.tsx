import React, { FC, useEffect, useMemo, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  FormControl,
  FormHelperText,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import { FileUpload } from '@/components/FileUpload'
import {
  COMPANY_USER_ROLES,
  CompanyUser,
  CompanyUserRole,
  UserProfileRequest,
} from '@/store/interfaces'
import { CreateUserSteps } from '@/features/companies/users/create/CreateUserPage'
import { matchIsValidTel, MuiTelInput, MuiTelInputInfo } from 'mui-tel-input'
import { createUser, uploadOnboardingDocuments } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { ACCESS_CONTROLS, COUNTRY_CODES } from '@/utils/constants'

const validationSchema = Yup.object().shape({
  isCompanyAdmin: Yup.boolean().required('Role is required'),
  phoneNumber: Yup.object({
    nationalNumber: Yup.string().required('Phone number is required'),
    countryCode: Yup.string().required('Phone code is required'),
    numberValue: Yup.string()
      .required('Phone number is required')
      .test('isValidPhone', 'Phone number is invalid', (value) =>
        matchIsValidTel(value)
      ),
  }).required('Phone number is required'),
  email: Yup.string()
    .required('Email should not be empty')
    .email('Enter valid email'),
  file: Yup.mixed().required('File is required'),
})

interface UserProfileProps {
  setStep: (step: CreateUserSteps) => void
}

const UserProfile: FC<UserProfileProps> = ({ setStep }) => {
  const dispatch = useAppDispatch()
  const { onboardingUser, isCreatingUser } = useAppSelector(
    (state) => state.companies
  )

  const user: CompanyUser | null = useMemo(
    () => (onboardingUser?.entity ? JSON.parse(onboardingUser.entity) : null),
    [onboardingUser]
  )

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_USER),
    []
  )

  const [documentUrl, setDocumentUrl] = useState(
    user?.accountCreationDocumentUrl
  )

  useEffect(() => {
    setDocumentUrl(user?.accountCreationDocumentUrl)
  }, [user])

  const formik = useFormik({
    initialValues: {
      isCompanyAdmin:
        user?.isCompanyAdmin !== undefined && user?.isCompanyAdmin !== null
          ? user?.isCompanyAdmin
          : false,
      email: user?.email ?? '',
      phoneNumber: {
        nationalNumber: user?.phoneNumber ?? '',
        countryCode: user?.phoneNumberCountryId ?? '',
        numberValue:
          user?.phoneNumber && user?.phoneNumberCountryId
            ? `${COUNTRY_CODES[user?.phoneNumberCountryId]}${user?.phoneNumber}`
            : '',
      },
      file: undefined,
      stepName: 'PROFILE',
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      try {
        const { phoneNumber, file, ...otherValues } = values
        const payload = {
          ...otherValues,
          phoneNumber: phoneNumber.nationalNumber,
          phoneNumberCountryId: phoneNumber.countryCode,
          approvalId: onboardingUser?.id,
          accountCreationDocumentUrl: documentUrl,
        } as UserProfileRequest

        //upload file
        if (!documentUrl && file) {
          const url = await uploadOnboardingDocuments(
            dispatch,
            file as File,
            {}
          )
          payload.accountCreationDocumentUrl = url
          setDocumentUrl(url)
        }

        await createUser(isSuper, dispatch, payload, () =>
          setStep(CreateUserSteps.SUBMISSION)
        )
      } catch (error) {
        console.error('Error during submission:', error)
      }
    },
  })
  const [role, setRole] = React.useState<CompanyUserRole | null>(
    user?.isCompanyAdmin !== undefined && user?.isCompanyAdmin !== null
      ? user.isCompanyAdmin
        ? 'Contact Person'
        : 'Team Member'
      : null
  )

  const handlePhoneChange = (_value: string, info: MuiTelInputInfo) => {
    formik.setFieldTouched('phoneNumber', true, false)
    formik.setFieldValue('phoneNumber', {
      nationalNumber: info.nationalNumber,
      countryCode: info.countryCode,
      numberValue: info.numberValue,
    })
  }

  return (
    <Stack spacing={2}>
      <Stack
        spacing={3}
        sx={{
          padding: 3,
          backgroundColor: '#FFFFFF',
          borderRadius: '0.5rem',
        }}
      >
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack spacing={2}>
              <Stack spacing={1}>
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  Role{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <Autocomplete
                  disablePortal
                  size="small"
                  id="bankCode"
                  options={COMPANY_USER_ROLES}
                  value={role}
                  onChange={(_, value) => {
                    setRole(value)
                    formik.setFieldValue(
                      'isCompanyAdmin',
                      value === 'Contact Person'
                    )
                  }}
                  getOptionLabel={(option) => option}
                  renderInput={(params) => (
                    <TextField
                      hiddenLabel
                      {...params}
                      placeholder="Select role"
                      error={Boolean(
                        formik.touched.isCompanyAdmin &&
                          formik.errors.isCompanyAdmin
                      )}
                      helperText={
                        formik.touched.isCompanyAdmin &&
                        formik.errors.isCompanyAdmin
                          ? formik.errors.isCompanyAdmin
                          : ''
                      }
                    />
                  )}
                />
              </Stack>
              <Stack spacing={1} sx={{ width: '100%' }}>
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  Email{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type="email"
                  placeholder="Email"
                  sx={{ marginBlock: '0' }}
                  margin="normal"
                  fullWidth
                  {...formik.getFieldProps('email')}
                  error={formik.touched.email && Boolean(formik.errors.email)}
                  helperText={formik.touched.email && formik.errors.email}
                />
              </Stack>

              <FormControl
                fullWidth
                error={Boolean(
                  formik.touched.phoneNumber && formik.errors.phoneNumber
                )}
                sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}
              >
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Phone number
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <MuiTelInput
                  hiddenLabel
                  size="small"
                  value={formik.values.phoneNumber.numberValue}
                  name="phoneNumber"
                  defaultCountry="KE"
                  onlyCountries={['KE', 'UG', 'TZ', 'BI']}
                  onChange={handlePhoneChange}
                  slotProps={{
                    htmlInput: {
                      maxLength: 15,
                    },
                  }}
                />
                {formik.touched.phoneNumber && formik.errors.phoneNumber && (
                  <FormHelperText error>
                    {formik.errors.phoneNumber.numberValue}
                  </FormHelperText>
                )}
              </FormControl>

              <Stack spacing={1} sx={{ width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Attach account creation request form :{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                  <Typography>
                    Usually a signed document provided by EATTA to authorize
                    creation of the user's account.
                  </Typography>
                  <FileUpload
                    fileUrl={user?.accountCreationDocumentUrl}
                    description="Only PDF file allowed."
                    mimeTypes={['application/pdf']}
                    required={true}
                    disabled={
                      isCreatingUser ||
                      Boolean(user?.accountCreationDocumentUrl)
                    }
                    onFileChange={(file) => formik.setFieldValue('file', file)}
                  />
                </Typography>
              </Stack>
              <Box mt={2}>
                <AccessControlWrapper
                  rights={ACCESS_CONTROLS.CREATE_USER}
                  makerId={onboardingUser?.maker}
                  isMake={true}
                >
                  <Button
                    fullWidth
                    variant="contained"
                    type="submit"
                    disabled={!formik.isValid || isCreatingUser}
                    endIcon={
                      isCreatingUser ? (
                        <CircularProgress size={20} thickness={3.0} />
                      ) : undefined
                    }
                  >
                    Next
                  </Button>
                </AccessControlWrapper>
              </Box>
            </Stack>
          </Form>
        </FormikProvider>
      </Stack>
    </Stack>
  )
}

export default UserProfile
