import { Divider, Stack, Typography } from '@mui/material'
import React, { FC, useEffect, useMemo, useState } from 'react'
import { PageFilters } from '@/store/interfaces'
import { useAppDispatch } from '@/store'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import { ApprovalRequestFilters } from '@/store/interfaces/makerChecker'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { SearchByValueConfig } from '@/components/PageFilters'

export interface ApprovalPageHeaderProps {
  filters: ApprovalRequestFilters
  onSearchChange: (key: keyof ApprovalRequestFilters, value: string) => void
}

// type ApprovalSearchByType = keyof Omit<
//   ApprovalRequestFilters,
//   keyof PageFilters
// >

type ApprovalSearchByType = keyof Partial<ApprovalRequestFilters>

const searchByValues: SearchByValueConfig<ApprovalRequestFilters>[] = [
  {
    filterLabel: 'Maker Firstname',
    filterKey: 'makerFirstName',
    type: 'string',
  },
  {
    filterLabel: 'Maker Lastname',
    filterKey: 'makerLastName',
    type: 'string',
  },
]

export const ApprovalRequestHeader: FC<ApprovalPageHeaderProps> = ({
  filters,
  onSearchChange,
}) => {
  const [searchBy, setSearchBy] =
    useState<ApprovalSearchByType>('makerFirstName')
  const searchByValue = ['makerFirstName', 'makerLastName']
  const [searchValue, setSearchValue] = useState('')
  const debouncedSearchValue = useDebounce(searchValue, 500)

  useEffect(() => {
    onSearchChange(searchBy, debouncedSearchValue)
  }, [debouncedSearchValue, searchBy])

  const searchByLabel = useMemo(() => {
    const map: Record<string, string> = {}
    searchByValues.forEach((value) => {
      map[String(value.filterKey)] =
        value.filterLabel || String(value.filterKey)
    })
    return map
  }, [searchByValues])

  return (
    <Stack>
      <Stack paddingInline={3} paddingBlock={2}>
        <Typography
          variant="h5"
          sx={{
            fontWeight: 600,
            color: '#000A12',
          }}
        >
          Approval Requests
        </Typography>
      </Stack>

      <Divider />

      <Stack
        paddingInline={3}
        paddingBlock={2}
        spacing={1}
        gap={3}
        flexWrap="wrap"
        sx={{
          display: 'flex',
          flexDirection: 'row',

          alignItems: 'center',
        }}
      >
        <CustomFilterUserBox
          searchValue={searchValue}
          searchByValues={searchByValue}
          selectedSearchBy={searchBy}
          searchByLabel={searchByLabel}
          onHandleSearch={(e: any) => setSearchValue(e.target.value)}
          setSearchByValue={(value: string) =>
            setSearchBy(value as ApprovalSearchByType)
          }
          searchPlaceHolder={`Search ${searchByLabel?.[searchBy] || searchBy || ''}`}
          prependSearchBy={true}
        />
      </Stack>
    </Stack>
  )
}
