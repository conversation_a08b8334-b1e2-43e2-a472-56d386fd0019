'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { CircularProgress, Divider, Stack } from '@mui/material'
import { MainPageHeader } from '@/components/MainPageHeader'
import { PageFilters } from '@/components/PageFilters'
import {
  CATALOGUE_STATUS_OPTIONS,
  CatalogueFilters,
  INVOICE_ENTRY_STATUS,
} from '@/store/interfaces'
import { SaleTitle } from '@/components/SaleTitle'
import { PostAuctionList } from '@/features/post-auction/PostAuctionList'
import { useAppDispatch, useAppSelector } from '@/store'
import { downloadCatalogues, getPostAuctionCatalogues } from '@/store/actions'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { UploadCatalogue } from '@/components/UploadCatalogue'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { AccessWrapper } from '@/components/AccessHelper'
import { CustomButton } from '@/components/CustomButton'
import { UploadIcon } from '@dtbx/ui/icons'
import useOnScreen from '@/hooks/useOnScreen'
import AuctionSkeleton from '../../components/AuctionSkeleton'
import { setPostAuctionFilters } from '@/store/reducers'

export const PostAuctionPage = () => {
  const dispatch = useAppDispatch()
  const isBackOffice = checkIfBackOffice()
  const router = useCustomRouter()
  const { decodedToken } = useAppSelector((state) => state.auth)
  const isProducer = decodedToken.clientType === 'Producer'

  const {
    postAuctionCataloguesResponse,
    isLoadingCatalogues,
    postAuctionFilters,
  } = useAppSelector((state) => state.catalogues)

  const [openUploadCatalogue, setOpenUploadCatalogue] = useState(false)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const isProductionEnv = process.env.NEXT_PUBLIC_ENVIRONMENT === 'prod'

  const handleSearch = useCallback(
    (newFilters: CatalogueFilters) => {
      const updatedFilters = {
        ...newFilters,
        [isProducer ? 'invoiceEntryStatus' : 'invoiceStatus']:
          newFilters.status,
        page: 1,
      }
      dispatch(setPostAuctionFilters(updatedFilters))
    },
    [isProducer, dispatch]
  )

  const handleExport = useCallback(() => {
    downloadCatalogues(dispatch, isBackOffice, postAuctionFilters)
  }, [dispatch, isBackOffice, postAuctionFilters])

  const fetchPostAuction = async (filter: CatalogueFilters) => {
    await getPostAuctionCatalogues(dispatch, isBackOffice, filter)
    setIsLoadingMore(false)
  }

  const loadMore = async () => {
    if (isLoadingMore) return
    setIsLoadingMore(true)
    const updatedFilters = {
      ...postAuctionFilters,
      page: postAuctionFilters.page + 1,
    }
    dispatch(setPostAuctionFilters(updatedFilters))

    // Check if we've reached the last page
    if (
      postAuctionFilters.page >=
      postAuctionCataloguesResponse.totalNumberOfPages
    ) {
      setHasMore(false)
    }
  }

  const { measureRef, isIntersecting, observer } = useOnScreen()

  useEffect(() => {
    if (hasMore && isIntersecting) {
      loadMore()
      observer?.disconnect()
    }
  }, [isIntersecting, hasMore, loadMore])

  const handleInvoiceUpload = () => {
    router.push('/invoices/upload')
  }

  const handleOnCloseUpload = (success: boolean) => {
    setOpenUploadCatalogue(false)
    //Refresh data on success
    if (success) {
      setTimeout(() => {
        dispatch(
          setPostAuctionFilters({
            ...postAuctionFilters,
            page: 1,
          })
        )
      }, 2000)
    }
  }

  useEffect(() => {
    fetchPostAuction({ ...postAuctionFilters, ascending: false })
  }, [postAuctionFilters])

  return (
    <Stack sx={{ height: '100%' }}>
      {!isBackOffice && (
        <>
          <MainPageHeader>
            <AccessWrapper clientTypes={['Broker']} backofficeAccess={false}>
              <Stack direction="row" spacing={2}>
                <CustomButton
                  variant="contained"
                  onClick={handleInvoiceUpload}
                  label="Upload New Invoice"
                  startIcon={<UploadIcon stroke="#FFFFFF" />}
                  sx={{
                    backgroundColor: 'primary.main',
                    border: 'none',
                    color: '#FFFFFF',
                    '&:hover': {
                      border: 'none',
                      color: '#FFFFFF',
                    },
                  }}
                />

                {/* Hidden for production environment */}

                {!isProductionEnv && (
                  <CustomButton
                    variant="contained"
                    onClick={() => setOpenUploadCatalogue(true)}
                    label="Upload New Catalogue"
                    startIcon={<UploadIcon stroke="#FFFFFF" />}
                    sx={{
                      backgroundColor: 'primary.main',
                      border: 'none',
                      color: '#FFFFFF',
                      '&:hover': {
                        border: 'none',
                        color: '#FFFFFF',
                      },
                    }}
                  />
                )}
              </Stack>
            </AccessWrapper>
          </MainPageHeader>
          <Divider />
        </>
      )}

      <PageFilters
        onSearch={handleSearch}
        filters={postAuctionFilters}
        onExport={handleExport}
        statuses={
          isProducer
            ? INVOICE_ENTRY_STATUS.filter((status) => status !== 'SETTLED')
            : CATALOGUE_STATUS_OPTIONS
        }
        searchByValues={[
          { filterLabel: 'Lot Number', filterKey: 'lotNo', type: 'numeric' },
          { filterLabel: 'Producer', filterKey: 'producer', type: 'string' },
          { filterLabel: 'Broker', filterKey: 'broker', type: 'string' },
          { filterLabel: 'Buyer', filterKey: 'buyer', type: 'string' },
          { filterLabel: 'Buyer Name', filterKey: 'buyerName', type: 'string' },
          {
            filterLabel: 'Invoice Number',
            filterKey: 'invoiceNo',
            type: 'string',
          },
          { filterLabel: 'Factory', filterKey: 'factory', type: 'string' },
        ]}
      />

      <SaleTitle
        saleDate={`${postAuctionFilters.year}/${postAuctionFilters.saleDate}`}
        lotsCount={postAuctionCataloguesResponse.totalElements}
      />

      <Stack
        sx={{
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {isLoadingCatalogues && !isLoadingMore ? (
          <AuctionSkeleton />
        ) : postAuctionCataloguesResponse.data.length === 0 ? (
          <EmptyPage
            title="There are no records to show"
            message="There are no records to show"
            bgUrl={isBackOffice ? '/eatta/combo.svg' : '/combo.svg'}
          />
        ) : (
          <Stack
            spacing={3}
            sx={{
              height: '100%',
              overflowY: 'auto',
              paddingRight: 2,
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#888',
                borderRadius: '4px',
              },
            }}
          >
            <PostAuctionList
              postAuctionData={postAuctionCataloguesResponse.data}
            />
            <div
              ref={(node) => {
                if (node) measureRef(node)
              }}
              style={{
                height: '60px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '60px',
              }}
            >
              {isLoadingMore && <CircularProgress size={40} />}
            </div>
          </Stack>
        )}
      </Stack>

      <UploadCatalogue
        auctionType="POST_AUCTION"
        open={openUploadCatalogue}
        onClose={handleOnCloseUpload}
      />
    </Stack>
  )
}
