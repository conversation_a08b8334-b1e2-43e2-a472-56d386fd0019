import {
  Box,
  Dialog,
  Divider,
  Icon<PERSON>utton,
  Stack,
  Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { useAppSelector } from '@/store'

export interface TeaDetailsProps {
  open: boolean
  onClose: () => void
}

const TeaDetails: React.FC<TeaDetailsProps> = ({ open, onClose }) => {
  const { selectedAuctionEntry } = useAppSelector((state) => state.invoices)

  const handleClose = () => {
    onClose()
  }

  const DELIVERY_ORDER_ITEMS = [
    {
      label: 'Garden Invoice',
      value: selectedAuctionEntry?.invoiceNo || '',
    },
    {
      label: 'Warrant #',
      value: selectedAuctionEntry?.type || '_',
    },
    {
      label: 'Pkgs',
      value: selectedAuctionEntry?.type || '_',
    },
    {
      label: 'Pkg Type',
      value: selectedAuctionEntry?.type || '_',
    },
    {
      label: 'Grade',
      value: selectedAuctionEntry?.grade || '_',
    },
    {
      label: 'Net Kgs',
      value: selectedAuctionEntry?.netWeight || '_',
    },
    {
      label: 'Pkg Wt',
      value: selectedAuctionEntry?.netWeight || '_',
    },
  ]

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={handleClose}>
      <Stack sx={{ width: '100%' }}>
        <Stack
          sx={{
            borderRadius: 2,
            gap: '0.5rem',
            padding: '1rem',
          }}
        >
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{
              mb: '1rem',
            }}
          >
            <Box display="flex" alignItems="flex-start">
              <Typography
                sx={{ fontSize: '1.125rem', fontWeight: 700, color: '#101828' }}
              >
                Lot Number: {selectedAuctionEntry?.lotNo || ''}
              </Typography>
              <Typography
                sx={{
                  fontSize: '1.125rem',
                  fontWeight: 700,
                  color: '#101828',
                  ml: 2,
                }}
              >
                {selectedAuctionEntry?.buyer || '_'}
              </Typography>
            </Box>
            <IconButton
              aria-label="close"
              onClick={handleClose}
              size="small"
              sx={{
                width: '36px',
                height: '36px',
                backgroundColor: ' #F1F5F9',
                border: '1px solid #CBD5E1)',
                borderRadius: '50%',
              }}
            >
              <CloseIcon />
            </IconButton>
          </Stack>
          {DELIVERY_ORDER_ITEMS.map(({ label, value }, index) => (
            <Stack key={index} spacing={2}>
              <Stack
                sx={{
                  flexDirection: 'row',
                  gap: '2rem',
                  justifyContent: 'space-between',
                }}
              >
                <Typography>{label}</Typography>
                <Typography sx={{ color: '#344054', fontWeight: 600 }}>
                  {value}
                </Typography>
              </Stack>
              {index < DELIVERY_ORDER_ITEMS.length - 1 && <Divider />}
            </Stack>
          ))}
        </Stack>
      </Stack>
    </Dialog>
  )
}

export default TeaDetails
