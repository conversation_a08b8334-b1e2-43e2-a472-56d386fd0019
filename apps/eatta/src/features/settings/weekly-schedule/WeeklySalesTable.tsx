'use client'
import React, { useEffect, useMemo, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  TextField,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { useAppDispatch, useAppSelector } from '@/store'
import { getSalesSchedule } from '@/store/actions/AuctionSchedule'
import { AuctionSchedule, Order } from '@/store/interfaces'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { UploadWeeklyScheduleDialog } from './UploadWeeklySchedule'
import { sortData } from '@/utils/sortTableData'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import dayjs from 'dayjs'
import { DateRangePicker } from '@dtbx/ui/components/DropDownMenus'
import TableSkeleton from '@/components/TableSkeleton'
import { CalendarIcon } from '@mui/x-date-pickers'
import { generateYears } from '@/utils/numberFormatter'

const WeeklySalesTable: React.FC = () => {
  const dispatch = useAppDispatch()
  const [showUploadSchedule, setShowUploadSchedule] = useState(false)
  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<string>('dateCreated')
  const [dateFilter, setDateFilters] = useState<{
    fromDate?: string
    toDate?: string
  }>({})
  const currentYear = new Date().getFullYear()
  const { scheduleResponse, isLoadingSchedules } = useAppSelector(
    (state) => state.salesSchedule
  )
  const [filters, setFilters] = useState<{
    year?: number
  }>({
    //Always flter by current year by default
    year: new Date().getFullYear(),
  })
  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof AuctionSchedule)
  }

  const sortKey = orderBy as keyof AuctionSchedule

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
    totalPages: scheduleResponse.totalNumberOfPages,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    await getSalesSchedule(dispatch, { ...filters, ...newOptions })
  }
  /*************************end pagination handlers**************************/
  const handleClose = () => {
    setShowUploadSchedule(false)
  }
  useEffect(() => {
    getSalesSchedule(dispatch, {
      ...filters,
      ...dateFilter,
      page: 1,
      size: 10,
    })
  }, [filters, dateFilter])
  const formik = useFormik({
    initialValues: {
      year: currentYear,
      month: '',
    },
    validationSchema: Yup.object({
      year: Yup.string().required('Year is required'),
      month: Yup.string().required('Month is required'),
    }),
    onSubmit: async () => {
      formik.resetForm()
    },
  })
  const salesScheduleSorted = sortData(
    [...scheduleResponse.data],
    sortKey,
    order
  )
  const viewingYear = useMemo(() => generateYears(), [])
  return (
    <Stack>
      <Stack
        sx={{
          width: '100%',
        }}
      >
        <Stack
          direction={{ xs: 'column', md: 'row' }}
          spacing={{ xs: 2, md: 1 }}
          paddingInline={{ xs: 1, md: 2 }}
          paddingBlock={1}
          sx={{
            backgroundColor: '#FFFFFF',
            alignItems: { sm: 'flex-start', md: 'center' },
            justifyContent: 'space-between',
          }}
        >
          <Typography sx={{ fontSize: '20px', fontWeight: 'bolder' }}>
            Schedule of Weekly Sales
          </Typography>

          <Stack flex={1}>
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={{ xs: 2, sm: 4 }}
              justifyContent={{ xs: 'flex-start', sm: 'flex-end' }}
              alignItems={{ xs: 'stretch', sm: 'center' }}
              marginTop={1}
            >
              <Stack spacing={1} sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                <Autocomplete
                  disablePortal
                  disableClearable
                  size="small"
                  id="year"
                  options={viewingYear}
                  sx={{ width: '100%' }}
                  {...formik.getFieldProps('year')}
                  onChange={(_, value) => {
                    formik.setFieldValue('year', value)
                    setFilters((prev) => ({
                      ...prev,
                      year: value,
                    }))
                  }}
                  renderInput={(params) => (
                    <TextField
                      hiddenLabel
                      placeholder="Year"
                      {...params}
                      error={Boolean(formik.touched.year && formik.errors.year)}
                      helperText={
                        formik.touched.year && formik.errors.year
                          ? formik.errors.year
                          : ''
                      }
                    />
                  )}
                />
              </Stack>

              <Stack spacing={1} sx={{ width: { xs: '100%', sm: 'auto' } }}>
                <DateRangePicker
                  onApplyDateRange={(newDate: {
                    start: dayjs.Dayjs
                    end: dayjs.Dayjs
                  }) => {
                    setDateFilters({
                      fromDate: newDate.start.format('YYYY-MM-DD'),
                      toDate: newDate.end.format('YYYY-MM-DD'),
                    })
                  }}
                  buttonText={
                    dateFilter.fromDate && dateFilter.toDate
                      ? `${dateFilter.fromDate} - ${dateFilter.toDate}`
                      : 'Filter by date'
                  }
                  buttonIcon={<CalendarIcon />}
                />
              </Stack>

              <Button
                variant="contained"
                color="primary"
                startIcon={<AddOutlinedIcon />}
                sx={{
                  borderRadius: '0.5rem',
                  px: 1.5,
                  width: { xs: '100%', sm: 'auto' },
                  whiteSpace: 'nowrap',
                }}
                onClick={() => setShowUploadSchedule(true)}
              >
                Upload New Schedule
              </Button>
            </Stack>
          </Stack>
        </Stack>
      </Stack>

      {isLoadingSchedules ? (
        <TableSkeleton rowCount={10} columnCount={5} />
      ) : (
        <>
          {/* Table or Empty Page */}
          {scheduleResponse.data.length === 0 ? (
            <EmptyPage
              title="No configurations found"
              message="Please create a new configuration to get started"
            />
          ) : (
            <Box>
              <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
                <Table
                  sx={{ minWidth: 650 }}
                  aria-label="designations table"
                  size="small"
                >
                  <CustomTableHeader
                    order={order}
                    orderBy={orderBy}
                    headLabel={[
                      {
                        id: 'saleNumber',
                        label: 'Sale Number',
                        alignRight: false,
                      },
                      { id: 'dayOne', label: 'Day 1', alignRight: false },
                      {
                        id: 'dayTwo',
                        label: 'Day 2',
                        alignRight: false,
                      },
                      {
                        id: 'promptDate',
                        label: 'Prompt Date',
                        alignRight: false,
                      },
                      {
                        id: 'catalogueClosing',
                        label: 'Catalogue Closing',
                        alignRight: false,
                      },
                    ]}
                    showCheckbox={false}
                    rowCount={2}
                    numSelected={2}
                    onRequestSort={handleRequestSort}
                  />
                  <TableBody>
                    {salesScheduleSorted.map((row, index) => (
                      <TableRow hover key={index} tabIndex={-1}>
                        <TableCell>
                          <Typography variant="body1">
                            {row.saleCode}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body1">
                            {row.dayOneDate}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {row.dayOne}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body1">
                            {row.dayTwoDate}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {row.dayTwo}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body1">
                            {row.promptDate}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {row.promptDay}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body1">
                            {row.closingDate}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {row.closingDay}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                  <TableFooter>
                    <TableRow>
                      <TableCell
                        sx={{ paddingInline: 0 }}
                        align="center"
                        height={40}
                        colSpan={7}
                      >
                        {scheduleResponse.data.length > 0 && (
                          <CustomPagination
                            options={{
                              ...paginationOptions,
                              totalPages: scheduleResponse.totalNumberOfPages,
                            }}
                            handlePagination={handlePagination}
                          />
                        )}
                      </TableCell>
                    </TableRow>
                  </TableFooter>
                </Table>
              </TableContainer>
            </Box>
          )}
        </>
      )}

      <UploadWeeklyScheduleDialog
        years={viewingYear}
        open={showUploadSchedule}
        handleClose={handleClose}
      />
    </Stack>
  )
}

export default WeeklySalesTable
