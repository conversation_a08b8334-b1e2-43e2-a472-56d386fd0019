'use client'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Button, CircularProgress, Divider, Stack } from '@mui/material'
import { MainPageHeader } from '@/components/MainPageHeader'
import { AccessWrapper } from '@/components/AccessHelper'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import {
  DEFAULT_FILTER_CONFIG,
  PageFilters,
  SearchByValueConfig,
} from '@/components/PageFilters'
import { InvoicesFilters, InvoiceTabType } from '@/store/interfaces'
import { InvoiceTabs } from '@/features/invoices/InvoiceTabs'
import { CHIP_COLORS } from '@/utils/statusChips'
import { ClientType } from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { dispatchInvoices } from '@/store/actions/invoices'
import { TabPanel } from '@dtbx/ui/components/Tabs'
import { InvoiceTable } from '@/features/invoices/InvoiceTable'
import {
  resetBrokerInvoicesResponse,
  resetInvoicesStore,
  setSelectedInvoices,
} from '@/store/reducers'
import { EslipTable } from '@/features/invoices/EslipTable'
import { CheckoutCart } from '@/components/CheckoutCart'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { getAuctionWeek } from '@dtbx/store/utils'

const BROKER_TABS: InvoiceTabType[] = [
  {
    title: 'Forward Invoices',
    status: CHIP_COLORS.success,
    invoiceStatus: 'READY',
    canSelect: true,
  },
  {
    title: 'Flagged with Errors',
    status: CHIP_COLORS.error,
    invoiceStatus: 'INVALID',
    canSelect: false,
  },
  {
    title: 'Sent Invoices',
    status: CHIP_COLORS.success,
    invoiceStatus: 'UNPAID',
    canSelect: false,
  },
  {
    title: 'Paid Invoices',
    status: CHIP_COLORS.success,
    invoiceStatus: 'PAID',
    canSelect: false,
  },
]

const TABS: InvoiceTabType[] = [
  {
    title: 'Received Invoices',
    status: CHIP_COLORS.success,
    invoiceStatus: 'UNPAID',
    canSelect: true,
  },
  {
    title: 'Partially Paid Invoices',
    status: CHIP_COLORS.success,
    invoiceStatus: 'PARTIAL',
    canSelect: true,
  },
  {
    title: 'Paid Invoices',
    status: CHIP_COLORS.success,
    invoiceStatus: 'PAID',
    canSelect: false,
  },
  {
    title: 'E-Slips',
    status: CHIP_COLORS.success,
    canSelect: false,
  },
]

const getTabs = (clientType?: ClientType): InvoiceTabType[] => {
  if (clientType === 'Broker') return BROKER_TABS

  if (clientType === 'Buyer') return TABS

  return []
}

const invoiceSearchByValue: SearchByValueConfig<InvoicesFilters>[] = [
  { filterLabel: 'Invoice Number', filterKey: 'invoiceNo', type: 'string' },
  { filterLabel: 'Buyer', filterKey: 'buyer', type: 'string' },
]

const eslipSearchByValue: SearchByValueConfig<InvoicesFilters>[] = [
  { filterLabel: 'Invoice Number', filterKey: 'invoiceNumber', type: 'string' },
  { filterLabel: 'Total', filterKey: 'total', type: 'numeric' },
  { filterLabel: 'Paid Amount', filterKey: 'paidAmount', type: 'numeric' },
  { filterLabel: 'Balance', filterKey: 'balance', type: 'numeric' },
]

export const InvoicePage: React.FC = () => {
  const dispatch = useAppDispatch()
  const { decodedToken } = useAppSelector((state) => state.auth)
  const { selectedInvoices, isDispatchingInvoices } = useAppSelector(
    (state) => state.invoices
  )

  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState<InvoiceTabType[]>(
    getTabs(decodedToken.clientType)
  )
  const [selectedTab, setSelectedTab] = useState(initialTab)
  const week = useMemo(() => getAuctionWeek().toString(), [])
  const [filters, setFilters] = useState<InvoicesFilters>({
    saleDate: searchParams.get('saleDate') ?? week,
    year: +(searchParams.get('year') ?? new Date().getFullYear()),
    status: tabs[selectedTab].invoiceStatus,
    page: 1,
    size: 10,
  })

  const searchByValues = useMemo(() => {
    const tab = tabs[selectedTab]
    return tab.title === 'E-Slips' ? eslipSearchByValue : invoiceSearchByValue
  }, [selectedTab])

  const handleTabSelected = (index: number) => {
    dispatch(resetBrokerInvoicesResponse)
    setSelectedTab(index)
    handleSearch({ ...filters, status: tabs[index].invoiceStatus })
  }

  const handleSearch = useCallback((newFilters: InvoicesFilters) => {
    setFilters({
      ...newFilters,
      page: 1,
    })
  }, [])

  const handleExport = useCallback(() => {
    console.log('Exporting Data')
  }, [])

  const handleDispatchInvoices = async () => {
    await dispatchInvoices(
      dispatch,
      selectedInvoices.map((invoice) => invoice.id),
      handleDispatchSuccess
    )
  }

  const handleDispatchSuccess = () => {
    //Clear selected invoice
    dispatch(setSelectedInvoices([]))
    //Navigate to dispatch tab
    handleTabSelected(2)
  }

  const handleCountChange = (count: number, tab: InvoiceTabType) => {
    setTabs((prevTabs) =>
      prevTabs.map((t) =>
        t.title === tab.title ? { ...t, itemCounts: count } : t
      )
    )
  }

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    params.set('year', filters.year.toString())
    params.set('saleDate', filters.saleDate)
    replace(`${pathname}?${params.toString()}`)
  }

  useEffect(() => {
    // Update filters when URL parameters change
    const saleDate = searchParams.get('saleDate')
    const year = searchParams.get('year')

    if (saleDate || year) {
      setFilters((prev) => ({
        ...prev,
        saleDate: saleDate ?? prev.saleDate,
        year: year ? +year : prev.year,
      }))
    }
  }, [searchParams])

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab, filters])

  //Clear selected invoice and reset store when sale date changes
  useEffect(() => {
    dispatch(resetInvoicesStore())
    dispatch(resetBrokerInvoicesResponse())
    
    return () => {
      dispatch(resetInvoicesStore())
      dispatch(resetBrokerInvoicesResponse())
    }
  }, [filters.saleDate])

  return (
    <Stack sx={{ height: '100%' }}>
      <MainPageHeader>
        <AccessWrapper clientTypes={['Broker']} backofficeAccess={false}>
          <Button
            variant="contained"
            disabled={isDispatchingInvoices || selectedInvoices.length === 0}
            onClick={handleDispatchInvoices}
            endIcon={
              isDispatchingInvoices ? (
                <CircularProgress size={20} thickness={3.0} />
              ) : (
                <ChevronRightIcon stroke="#98A2B3" />
              )
            }
          >
            Send Invoices
            {selectedInvoices.length > 0 && `(${selectedInvoices.length})`}
          </Button>
        </AccessWrapper>
        <CheckoutCart checkoutType="INVOICE" />
      </MainPageHeader>
      <Divider />

      <PageFilters
        title={`View Invoices for sale of ${filters.year}/${filters.saleDate}`}
        filterConfig={{
          ...DEFAULT_FILTER_CONFIG,
          showStatus: false,
        }}
        onSearch={handleSearch}
        filters={filters}
        onExport={handleExport}
        searchByValues={searchByValues}
      />

      <Divider />

      <InvoiceTabs
        tabs={tabs}
        selectedTab={selectedTab}
        onTabSelected={handleTabSelected}
      />

      <Stack sx={{ overflow: 'auto' }}>
        {tabs.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            {tab.title === 'E-Slips' ? (
              <EslipTable
                filters={filters}
                onLotCountChange={(count) => handleCountChange(count, tab)}
              />
            ) : (
              <InvoiceTable
                canSelect={tab.canSelect}
                filters={filters}
                onLotCountChange={(count) => handleCountChange(count, tab)}
              />
            )}
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}
