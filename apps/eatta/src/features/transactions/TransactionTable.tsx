/**
 * <AUTHOR> on 01/07/2025
 */
import {
  PaymentFilters,
  Transaction,
  TransactionStatus,
} from '@/store/interfaces/transactions'
import { useAppDispatch, useAppSelector } from '@/store'
import React, { useEffect, useState } from 'react'
import { Order } from '@/store/interfaces'
import { sortData } from '@/utils/sortTableData'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { useResetPageOnFilterChange } from '@/hooks/useResetPageOnFilterChange'
import { getTransactions } from '@/store/actions/transactions'
import TableSkeleton from '@/components/TableSkeleton'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import {
  Button,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import { formatCurrency, formatDate } from '@dtbx/store/utils'
import { ArrowForwardIos } from '@mui/icons-material'
import { TransactionDetailsModal } from '@/features/transactions/TransactionDetailsModal'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { setSelectedTransaction } from '@/store/reducers'

export interface SettlementTableProps {
  status: TransactionStatus
  filters: PaymentFilters
  canSelect: boolean
  onTransactionCountChange?: (count: number) => void
  onSetCurrentTab: (tabIndex: number) => void
}

export const TransactionTable = ({
  status,
  filters,
  onTransactionCountChange,
  onSetCurrentTab,
}: SettlementTableProps) => {
  const dispatch = useAppDispatch()

  const { transactions, isLoading } = useAppSelector(
    (state) => state.transactions
  )

  const [openDetailsModal, setOpenDetailsModal] = useState<boolean>(false)

  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<keyof Transaction>('dateCreated')

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof Transaction)
  }

  const sortKey = orderBy as keyof Transaction
  const transactionsSorted = sortData([...transactions.data], sortKey, order)

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }

  const resetPageRef = useResetPageOnFilterChange(
    filters,
    paginationOptions,
    setPaginationOptions
  )

  const handleViewDetails = (transaction: Transaction) => {
    dispatch(setSelectedTransaction(transaction))
    setOpenDetailsModal(true)
  }

  const handleTransactionModalClose = (tabIndex: number | undefined) => {
    setOpenDetailsModal(false)
    if (tabIndex !== undefined) onSetCurrentTab(tabIndex)
  }

  useEffect(() => {
    if (resetPageRef.current) {
      resetPageRef.current = false
      return
    }
    const fetchTransactions = async () => {
      await getTransactions(dispatch, {
        ...filters,
        status,
        page: paginationOptions.page,
        size: paginationOptions.size,
        ascending: false,
      })
    }
    fetchTransactions()
  }, [dispatch, filters, paginationOptions.page])

  useEffect(() => {
    onTransactionCountChange &&
      onTransactionCountChange(transactions.totalElements)
  }, [transactions.totalElements])

  return isLoading ? (
    <TableSkeleton rowCount={15} columnCount={6} />
  ) : transactions.data.length === 0 ? (
    <EmptyPage
      title="No transactions found"
      message="Transactions will appear here once they are processed."
      bgUrl={'/eatta/combo.svg'}
    />
  ) : (
    <>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
          '& .MuiTableCell-root': {
            paddingInline: '1.5rem',
            paddingBlock: '0.5rem',
            textAlign: 'left',
          },
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="transactions table"
          size="small"
        >
          <CustomTableHeader
            order={'asc'}
            orderBy={'id'}
            headLabel={[
              {
                id: 'organizationName',
                label: 'Organization',
                alignRight: false,
              },
              {
                id: 'organizationType',
                label: 'Type of Organization',
                alignRight: false,
              },
              {
                id: 'destinationAccount',
                label: 'Destination Account',
                alignRight: false,
              },
              {
                id: 'branch',
                label: 'Branch',
                alignRight: false,
              },
              {
                id: 'amount',
                label: 'Amount to Disburse',
                alignRight: false,
              },
              { id: 'dateCreated', label: 'Date Paid', alignRight: false },
              { id: 'channel', label: 'Channel', alignRight: false },
              { label: '' },
            ]}
            showCheckbox={false}
            rowCount={transactions.data.length}
            numSelected={0}
            onRequestSort={handleRequestSort}
          />
          <TableBody>
            {transactionsSorted.map((row) => {
              const {
                id,
                destinationAccount,
                channel,
                amount,
                dateCreated,
                organizationName,
                organizationType,
              } = row
              return (
                <TableRow hover key={id} tabIndex={-1} role="checkbox">
                  <TableCell>{organizationName}</TableCell>
                  <TableCell>
                    <StatusChip
                      status={
                        organizationType === 'Broker' ||
                        organizationType === 'Buyer'
                          ? 'success'
                          : 'error'
                      }
                      label={organizationType}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {destinationAccount.accountNumber}
                    </Typography>
                    <Typography variant="body2">
                      {' '}
                      {destinationAccount.bank}
                    </Typography>
                  </TableCell>
                  <TableCell>{destinationAccount.branch ?? 'N/A'}</TableCell>
                  <TableCell>
                    {formatCurrency(amount, 'USD', 'en-US')}
                  </TableCell>
                  <TableCell>{formatDate(dateCreated ?? '')}</TableCell>
                  <TableCell>{channel ?? 'N/A'}</TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleViewDetails(row)}>
                      <ArrowForwardIos />
                    </IconButton>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell
                align="center"
                colSpan={12}
                sx={{ paddingInline: 0, height: 40 }}
              >
                {transactions.totalNumberOfPages > 0 && (
                  <CustomPagination
                    options={{
                      ...paginationOptions,
                      totalPages: transactions.totalNumberOfPages,
                    }}
                    handlePagination={handlePagination}
                  />
                )}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
      {openDetailsModal && (
        <TransactionDetailsModal
          open={openDetailsModal}
          onClose={handleTransactionModalClose}
        />
      )}
    </>
  )
}
