'use client'
import { DEFAULT_FILTER_CONFIG, PageFilters } from '@/components/PageFilters'
import { Divider, Stack } from '@mui/material'
import {
  TransactionFilters,
  TransactionStatus,
} from '@/store/interfaces/transactions'
import { TransactionsTable } from './TransactionsTable'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { getAuctionWeek } from '@dtbx/store/utils'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { TabPanel } from '@dtbx/ui/components/Tabs'
import { TransactionApprovalsTable } from '@/features/transactions/TransactionApprovalsTable'
import { CustomTabs, TabType } from '@/components/CustomTabs'
import { useAppSelector } from '@/store'

const TABS: TabType<TransactionStatus>[] = [
  {
    title: 'Allocated',
    status: 'ALLOCATED',
    canSelect: true,
  },
  {
    title: 'Unallocated',
    status: 'UNALLOCATED',
    canSelect: false,
  },
  {
    title: 'Pending Approval',
    status: 'PENDING',
    canSelect: false,
  },
]

export const TransactionsPage = () => {
  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState<TabType<TransactionStatus>[]>(TABS)
  const [selectedTab, setSelectedTab] = useState(initialTab)

  const { isLoading } = useAppSelector((state) => state.transactions)

  const week = useMemo(() => getAuctionWeek().toString(), [])

  const [filters, setFilters] = useState<TransactionFilters>({
    saleDate: searchParams.get('saleDate') ?? week,
    year: +(searchParams.get('year') ?? new Date().getFullYear()),
    page: 1,
    size: 10,
  })

  const handleSearch = useCallback((newFilters: TransactionFilters) => {
    setFilters({
      ...newFilters,
      page: 1,
    })
  }, [])

  const handleCountChange = (
    count: number,
    tab: TabType<TransactionStatus>
  ) => {
    setTabs((prevTabs) =>
      prevTabs.map((t) =>
        t.title === tab.title ? { ...t, itemCounts: count } : t
      )
    )
  }

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    params.set('year', filters.year.toString())
    params.set('saleDate', filters.saleDate)
    replace(`${pathname}?${params.toString()}`)
  }

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab, filters])

  return (
    <Stack sx={{ height: '100%' }}>
      <PageFilters
        title="Money In"
        subtitle={
          'A record of all tea E-slip payments received into the EATTA DTB escrow account number ***********'
        }
        filterConfig={{
          ...DEFAULT_FILTER_CONFIG,
          showStatus: false,
          showExport: false,
          showDateFilter: true,
        }}
        onSearch={handleSearch}
        filters={filters}
        searchByValues={[
          {
            filterLabel: 'Transaction Ref',
            filterKey: 'transactionRef',
            type: 'string',
          },
          {
            filterLabel: 'Eslip Number',
            filterKey: 'eslipNumber',
            type: 'string',
          },
          {
            filterLabel: 'Buyer Name',
            filterKey: 'buyer',
            type: 'string',
          },
          {
            filterLabel: 'Amount',
            filterKey: 'amount',
            type: 'numeric',
          },
        ]}
        onExport={() => console.log('Export clicked')}
      />
      <Divider />
      <CustomTabs
        tabs={tabs}
        selectedTab={selectedTab}
        onTabSelected={setSelectedTab}
        isLoading={isLoading}
      />
      <Stack sx={{ overflow: 'auto' }}>
        {tabs.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            {tab.title === 'Pending Approval' ? (
              <TransactionApprovalsTable
                status={tab.status}
                filters={filters}
                onApprovalCountChange={(count) => handleCountChange(count, tab)}
                canSelect={false}
                onSetCurrentTab={setSelectedTab}
              />
            ) : (
              <TransactionsTable
                status={tab.status}
                filters={filters}
                canSelect={tabs[selectedTab].canSelect}
                onTransactionCountChange={(count) =>
                  handleCountChange(count, tabs[selectedTab])
                }
                onSetCurrentTab={setSelectedTab}
              />
            )}
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}
