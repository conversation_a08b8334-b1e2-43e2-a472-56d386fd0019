import React, { FC, useEffect, useMemo } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import {
  CompanyUser,
  EAST_AFRICAN_COUNTRIES,
  UserIdentityRequest,
} from '@/store/interfaces'
import { createUser } from '@/store/actions'
import { useAppDispatch } from '@dtbx/store'
import { useAppSelector } from '@/store'
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight'
import { CreateMemberSteps } from '@/features/members/create/CreateMemberPage'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { ID_REGEX } from '@/utils/validators'

const validationSchema = Yup.object().shape({
  citizenship: Yup.string().required('Nationality is required'),
  nationalId: Yup.string()
    .matches(ID_REGEX, 'National ID must be up to 8 digits only')
    .required('ID Number is required')
    .min(8, 'National ID must be up to 8 digits only')
    .max(8, 'National ID must be up to 8 digits only'),
  firstName: Yup.string().required('First name is required'),
  middleName: Yup.string().optional().nullable(),
  lastName: Yup.string().required('Last name is required'),
})

type IdentityVerificationProps = {
  setStep: (step: CreateMemberSteps) => void
}

const MemberIdentityVerification: FC<IdentityVerificationProps> = ({
  setStep,
}) => {
  const dispatch = useAppDispatch()
  const { isCreatingUser, selectedCompany, onboardingUser, companiesResponse } =
    useAppSelector((state) => state.companies)
  const user: CompanyUser | null = useMemo(
    () => (onboardingUser?.entity ? JSON.parse(onboardingUser.entity) : null),
    [onboardingUser]
  )

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_USER),
    []
  )
  const formik = useFormik<UserIdentityRequest>({
    initialValues: {
      organizationCode: selectedCompany?.code ?? '',
      citizenship: user?.citizenship ?? '',
      firstName: user?.firstName ?? '',
      middleName: user?.middleName ?? '',
      lastName: user?.lastName ?? '',
      nationalId: user?.nationalId ?? '',
      stepName: 'VERIFICATION',
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      const payload: UserIdentityRequest = {
        ...values,
        approvalId: onboardingUser?.id,
      }
      await createUser(isSuper, dispatch, payload, () =>
        setStep(CreateMemberSteps.PROFILE)
      )
    },
  })

  useEffect(() => {
    formik.setFieldValue('organizationCode', selectedCompany?.code ?? '')
  }, [selectedCompany])

  useEffect(() => {
    console.log('companiesResponse >', companiesResponse)
  }, [])

  return (
    <Stack spacing={2}>
      <Stack
        spacing={3}
        sx={{
          padding: 3,
          backgroundColor: '#FFFFFF',
          borderRadius: '0.5rem',
        }}
      >
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack spacing={2}>
              <Stack spacing={1}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Company{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <Autocomplete
                  disablePortal
                  size="small"
                  id="citizenship"
                  options={companiesResponse.data}
                  value={formik.values.citizenship}
                  onChange={(_, value) => {
                    formik.setFieldValue('citizenship', value)
                  }}
                  onBlur={() => formik.setFieldTouched('citizenship', true)}
                  getOptionLabel={(option) => option}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Select Nationality"
                      error={
                        formik.touched.citizenship &&
                        Boolean(formik.errors.citizenship)
                      }
                      helperText={
                        formik.touched.citizenship && formik.errors.citizenship
                          ? formik.errors.citizenship
                          : ''
                      }
                    />
                  )}
                />
              </Stack>
              <Stack spacing={1}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Nationality{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <Autocomplete
                  disablePortal
                  size="small"
                  id="citizenship"
                  options={EAST_AFRICAN_COUNTRIES}
                  value={formik.values.citizenship}
                  onChange={(_, value) => {
                    formik.setFieldValue('citizenship', value)
                  }}
                  onBlur={() => formik.setFieldTouched('citizenship', true)}
                  getOptionLabel={(option) => option}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Select Nationality"
                      error={
                        formik.touched.citizenship &&
                        Boolean(formik.errors.citizenship)
                      }
                      helperText={
                        formik.touched.citizenship && formik.errors.citizenship
                          ? formik.errors.citizenship
                          : ''
                      }
                    />
                  )}
                />
              </Stack>
              <Stack spacing={1} sx={{ width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  ID Number{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type="text"
                  placeholder="Enter ID Number"
                  sx={{ marginBlock: '0' }}
                  margin="normal"
                  fullWidth
                  {...formik.getFieldProps('nationalId')}
                  error={
                    formik.touched.nationalId &&
                    Boolean(formik.errors.nationalId)
                  }
                  helperText={
                    formik.touched.nationalId && formik.errors.nationalId
                  }
                />
              </Stack>

              <Stack spacing={1} sx={{ width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  FIrst Name{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type="text"
                  placeholder="Enter first name"
                  sx={{ marginBlock: '0' }}
                  margin="normal"
                  fullWidth
                  {...formik.getFieldProps('firstName')}
                  error={
                    formik.touched.firstName && Boolean(formik.errors.firstName)
                  }
                  helperText={
                    formik.touched.firstName && formik.errors.firstName
                  }
                />
              </Stack>

              <Stack spacing={1} sx={{ width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Middle Name(s)
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type="text"
                  placeholder="Enter middle name(s)"
                  sx={{ marginBlock: '0' }}
                  margin="normal"
                  fullWidth
                  {...formik.getFieldProps('middleName')}
                  error={
                    formik.touched.middleName &&
                    Boolean(formik.errors.middleName)
                  }
                  helperText={
                    formik.touched.middleName && formik.errors.middleName
                  }
                />
              </Stack>

              <Stack spacing={1} sx={{ width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Last Name{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type="text"
                  placeholder="Enter last name"
                  sx={{ marginBlock: '0' }}
                  margin="normal"
                  fullWidth
                  {...formik.getFieldProps('lastName')}
                  error={
                    formik.touched.lastName && Boolean(formik.errors.lastName)
                  }
                  helperText={formik.touched.lastName && formik.errors.lastName}
                />
              </Stack>

              <Box mt={2}>
                <AccessControlWrapper
                  rights={ACCESS_CONTROLS.CREATE_USER}
                  makerId={onboardingUser?.maker}
                  isMake={true}
                >
                  <Button
                    fullWidth
                    variant="contained"
                    type="submit"
                    disabled={!formik.isValid || isCreatingUser}
                    endIcon={
                      isCreatingUser ? (
                        <CircularProgress size={20} thickness={3.0} />
                      ) : (
                        <KeyboardArrowRightIcon />
                      )
                    }
                  >
                    Next
                  </Button>
                </AccessControlWrapper>
              </Box>
            </Stack>
          </Form>
        </FormikProvider>
      </Stack>
    </Stack>
  )
}

export default MemberIdentityVerification
