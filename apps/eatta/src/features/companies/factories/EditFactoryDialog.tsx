import React, { useEffect, useMemo } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import * as Yup from 'yup'
import { Form, FormikProvider, useFormik } from 'formik'
import {
  Bank,
  BankBranch,
  CHANNELS,
  Company,
  CURRENCIES,
  Currency,
  PaymentChannel,
} from '@/store/interfaces'
import { useAppSelector } from '@/store'
import { NUMERIC_REGEX, ORG_NAME_REGEX } from '@/utils/validators'
import CloseIcon from '@mui/icons-material/Close'
import { useAppDispatch } from '@dtbx/store'
import { fetchBanks, getFactories, updateCompany } from '@/store/actions'
import { AddFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { EditFactoryDetails, FactoryResponse } from '@/store/interfaces/factory'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { HasAccessToRights } from '@dtbx/store/utils'

const initialSchema = Yup.object({
  name: Yup.string()
    .required('Factory Name is required')
    .matches(
      ORG_NAME_REGEX,
      'Only letters, numbers, spaces, and brackets are allowed. No leading or trailing spaces.'
    )
    .test(
      'alpha',
      'Name must contain at least three letters.',
      (value) => (value.match(/[a-zA-Z]/g) || []).length >= 3
    ),
  bankName: Yup.string().required('Bank is required'),
  accountCurrency: Yup.string().required('Currency is required'),
  accountNumber: Yup.string()
    .required('Account number should not be empty')
    .matches(NUMERIC_REGEX, 'Account number should be a number')
    .min(5, 'Account number should be at least 5 digits')
    .max(16, 'Account number should have max of 16 digits'),
  channel: Yup.string().required('channel is required'),
  swiftCode: Yup.string().required('Swift Code is required'),
})

export const EditFactoryForm = ({
  onEditSuccess,
  onCancel,
  factory,
}: {
  onEditSuccess: () => void
  onCancel: () => void
  company: Company | null
  factory: FactoryResponse | null
}) => {
  const { banks, bankBranches, isEditingUser, selectedCompany } =
    useAppSelector((state) => state.companies)

  const dispatch = useAppDispatch()
  const [bank, setBank] = React.useState<Bank | null>(null)
  const [currency, setCurrency] = React.useState<Currency | null>(null)
  const [branch, setBranch] = React.useState<BankBranch | null>(null)
  const [validationSchema, updateValidationSchema] =
    React.useState(initialSchema)

  useEffect(() => {
    fetchBanks(dispatch)
  }, [])

  useEffect(() => {
    const schema = initialSchema.shape({
      channel: Yup.string<PaymentChannel>().required(
        'Channel should not be empty'
      ),
    })

    if (bank?.bicCode !== 'DTKEKENA') {
      schema.shape({
        bankBranchName: Yup.string().required(
          'Branch name should not be empty'
        ),
        bankBranchCode: Yup.string().required(
          'Branch code should not be empty'
        ),
      })
    }

    updateValidationSchema(schema)
  }, [bank?.bicCode])

  const isBackoffice = checkIfBackOffice()
  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_UPDATE_ORGANIZATION),
    []
  )
  useEffect(() => {
    if (factory && bankBranches.length > 0) {
      const matchedBranch = bankBranches.find(
        (b) => b.code === factory.bankBranchCode
      )
      setBranch(matchedBranch ?? null)
    }
  }, [factory, bankBranches])
  const formik = useFormik({
    initialValues: {
      name: factory?.factoryName ?? '',
      bankBranchName: factory?.bankBranchName ?? '',
      bankCode: factory?.bankCode ?? '',
      bankName: factory?.bankName ?? '',
      bankBranchCode: factory?.bankBranchCode ?? '',
      accountNumber: factory?.accountNumber ?? '',
      accountCurrency: factory?.accountCurrency ?? '',
      channel: factory?.channel ?? '',
      swiftCode: factory?.swiftCode ?? '',
      makerStep:'SUBMISSION'
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      if (!factory || !factory.id) {
        console.error('Factory or factory ID is missing')
        return
      }
      const editFactory: EditFactoryDetails = { ...values }
      await updateCompany(isSuper, dispatch, factory.id, editFactory, () =>
        onEditSuccess()
      )

      await getFactories(dispatch, isBackoffice, {
        page: 1,
        size: 10,
        parentOrganizationId: selectedCompany?.id || '',
      })
    },
  })

  useEffect(() => {
    if (factory) {
      const selectedBank = banks.find((b) => b.bicCode === factory.swiftCode)
      setBank(selectedBank || null)

      const selectedCurrency = CURRENCIES.find(
        (c) => c.code === factory.accountCurrency
      )
      setCurrency(selectedCurrency || null)
    }
  }, [factory, banks])

  const handleBankChange = (value: Bank | null) => {
    setBank(value)
    formik.setFieldValue('bankCode', value?.bankCode ?? '')
    formik.setFieldValue('bankName', value?.bankName ?? '')

    formik.setFieldValue('swiftCode', value?.bicCode ?? '')
    formik.setFieldValue('channel', value?.bicCode === 'DTKEKENA' ? 'IFT' : '')
  }

  return (
    <FormikProvider value={formik}>
      <Form onSubmit={formik.handleSubmit}>
        <Stack spacing={2} paddingBlock={2}>
          <Stack spacing={1}>
            <Typography fontWeight={600}>Name of factory:</Typography>
            <TextField
              hiddenLabel
              size="small"
              type="text"
              sx={{ marginBlock: '0' }}
              margin={'normal'}
              {...formik.getFieldProps('name')}
              fullWidth
              onKeyDown={(e) => e.stopPropagation()}
              error={Boolean(formik.touched.name && formik.errors.name)}
              helperText={formik.touched.name && formik.errors.name}
            />
          </Stack>
          <Stack spacing={1}>
            <Typography fontWeight={600}>Enter payment details:</Typography>
            <Typography>
              Please provide a bank account through which the factory will
              receive funds a sale. The can be paid either through SWIFT or
              RTGS.You can always update this later.
            </Typography>
            <Stack spacing={1}>
              <Typography variant="body1" fontWeight={600}>
                Select Bank
              </Typography>
              <Autocomplete
                disablePortal
                size="small"
                id="bankCode"
                options={banks}
                value={bank}
                onChange={(_, value) => handleBankChange(value)}
                getOptionLabel={(option) => option.bankName}
                isOptionEqualToValue={(option, value) =>
                  option.bankCode === value.bankCode
                }
                renderInput={(params) => (
                  <TextField
                    hiddenLabel
                    {...params}
                    placeholder="Select bank"
                    error={Boolean(
                      formik.touched.bankCode && formik.errors.bankCode
                    )}
                    helperText={
                      formik.touched.bankCode && formik.errors.bankCode
                        ? formik.errors.bankCode
                        : ''
                    }
                  />
                )}
              />
            </Stack>
          </Stack>
          {formik.values.channel === 'IFT' && (
            <Stack direction="row" spacing={2}>
              <Stack width="100%">
                <Typography fontWeight={600}>Branch Name</Typography>
                <Autocomplete
                  fullWidth
                  disablePortal
                  size="small"
                  id="bankBranchName"
                  options={bankBranches}
                  value={branch}
                  onChange={(_, value) => {
                    setBranch(value)
                    formik.setFieldValue('', value?.name ?? '')
                    formik.setFieldValue('bankBranchCode', value?.code ?? '')
                  }}
                  getOptionLabel={(option) => option.name}
                  isOptionEqualToValue={(option, value) =>
                    option.code === value.code
                  }
                  renderInput={(params) => (
                    <TextField
                      hiddenLabel
                      {...params}
                      placeholder="Select branch"
                      error={Boolean(
                        formik.touched.bankBranchName &&
                          formik.errors.bankBranchName
                      )}
                      helperText={
                        formik.touched.bankBranchName &&
                        formik.errors.bankBranchName
                          ? formik.errors.bankBranchName
                          : ''
                      }
                    />
                  )}
                />
              </Stack>
              <Stack width="100%">
                <Typography fontWeight={600}>Branch Code</Typography>
                <TextField
                  disabled
                  hiddenLabel
                  size="small"
                  type="text"
                  placeholder="001"
                  sx={{ marginBlock: '0' }}
                  margin={'normal'}
                  {...formik.getFieldProps('bankBranchCode')}
                  fullWidth
                  error={Boolean(
                    formik.touched.bankBranchCode &&
                      formik.errors.bankBranchCode
                  )}
                  helperText={
                    formik.touched.bankBranchCode &&
                    formik.errors.bankBranchCode
                  }
                />
              </Stack>
            </Stack>
          )}

          <Stack direction="row" spacing={2}>
            <Stack width="100%">
              <Typography fontWeight={600}>
                Account Number{' '}
                <Box component="span" sx={{ color: 'primary.main' }}>
                  *
                </Box>
              </Typography>
              <TextField
                hiddenLabel
                size="small"
                type="text"
                sx={{ marginBlock: '0' }}
                margin={'normal'}
                {...formik.getFieldProps('accountNumber')}
                fullWidth
                onKeyDown={(e) => e.stopPropagation()}
                error={Boolean(
                  formik.touched.accountNumber && formik.errors.accountNumber
                )}
                helperText={
                  formik.touched.accountNumber && formik.errors.accountNumber
                }
              />
            </Stack>
            <Stack sx={{ width: '100%' }}>
              <Typography variant="body1" fontWeight={600}>
                Currency
              </Typography>
              <Autocomplete
                fullWidth
                disablePortal
                size="small"
                id="accountCurrency"
                options={CURRENCIES}
                value={currency}
                onChange={(_, value) => {
                  setCurrency(value)
                  formik.setFieldValue('accountCurrency', value?.code ?? '')
                }}
                getOptionLabel={(option) => option.name}
                isOptionEqualToValue={(option, value) =>
                  option.code === value.code
                }
                renderInput={(params) => (
                  <TextField
                    hiddenLabel
                    {...params}
                    placeholder="Select currency"
                    error={Boolean(
                      formik.touched.accountCurrency &&
                        formik.errors.accountCurrency
                    )}
                    helperText={
                      formik.touched.accountCurrency &&
                      formik.errors.accountCurrency
                        ? formik.errors.accountCurrency
                        : ''
                    }
                  />
                )}
              />
            </Stack>
          </Stack>
          <Stack sx={{ width: '100%' }}>
            <Typography variant="body1" fontWeight={600}>
              Channel
            </Typography>
            <Autocomplete
              fullWidth
              disablePortal
              size="small"
              id="channel"
              options={CHANNELS.filter((c) =>
                bank?.bicCode === 'DTKEKENA' ? c === 'IFT' : c !== 'IFT'
              )}
              {...formik.getFieldProps('channel')}
              onChange={(_, value: PaymentChannel) => {
                formik.setFieldValue('channel', value)
              }}
              renderInput={(params) => (
                <TextField
                  hiddenLabel
                  {...params}
                  placeholder="Select channel"
                  error={Boolean(
                    formik.touched.channel && formik.errors.channel
                  )}
                  helperText={
                    formik.touched.channel && formik.errors.channel
                      ? formik.errors.channel
                      : ''
                  }
                />
              )}
            />
          </Stack>
          <Stack>
            <Typography fontWeight={600}>SWIFT Code</Typography>
            <TextField
              hiddenLabel
              size="small"
              type="text"
              disabled // ← this makes it read-only
              sx={{ marginBlock: '0' }}
              margin={'normal'}
              {...formik.getFieldProps('swiftCode')}
              fullWidth
              onKeyDown={(e) => e.stopPropagation()}
              error={Boolean(
                formik.touched.swiftCode && formik.errors.swiftCode
              )}
              helperText={formik.touched.swiftCode && formik.errors.swiftCode}
            />
          </Stack>

          <Stack
            direction="row"
            spacing={2}
            sx={{
              mt: 3,
              width: '100%',
            }}
          >
            <Button
              sx={{ width: '40%' }}
              disabled={isEditingUser}
              variant="outlined"
              onClick={onCancel}
            >
              Cancel
            </Button>

            <Button
              fullWidth
              variant="contained"
              type="submit"
              disabled={!formik.isValid || isEditingUser}
              endIcon={
                isEditingUser ? (
                  <CircularProgress size={20} thickness={3.0} />
                ) : undefined
              }
            >
              Update
            </Button>
          </Stack>
        </Stack>
      </Form>
    </FormikProvider>
  )
}

type EditFactoryDialogProps = {
  organizationName: string
  open: boolean
  handleClose: () => void
  company: Company
  factory: FactoryResponse
}

export const EditFactoryDialog = ({
  open,
  handleClose,
  factory,
}: EditFactoryDialogProps) => {
  const { selectedCompany, selectedFactory } = useAppSelector(
    (state) => state.companies
  )
  return (
    <Dialog maxWidth={'sm'} open={open} onClose={handleClose}>
      <DialogTitle fontWeight={600}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            spacing={1}
          >
            <AddFactoryIcon height={32} width={32} />
            <Typography variant="subtitle1" fontWeight="bold">
              Edit Mark - {selectedFactory?.factoryName || ''}
            </Typography>
          </Stack>
          <IconButton
            sx={{
              height: '2rem',
              width: '2rem',
              border: '1px solid #D0D5DD',
              borderRadius: '0.5rem',
            }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          Sarma is registered under {selectedCompany?.name || ''}
        </DialogContentText>

        <EditFactoryForm
          onEditSuccess={handleClose}
          onCancel={handleClose}
          company={selectedCompany}
          factory={selectedFactory}
        />
      </DialogContent>
    </Dialog>
  )
}
