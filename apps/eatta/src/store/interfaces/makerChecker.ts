//Maker-checker

export type ApprovalRequestStatus =
  | 'STAGING'
  | 'PENDING'
  | 'APPROVED'
  | 'REJECTED'
export interface ApprovalRequestFilters {
  channel: string
  status?: string
  module: string
  makerFirstName?: string
  makerLastName?: string
  createDateFrom?: string
  createDateTo?: string
  requestType?: string
  size: number
  page: number
  organizationCode?: string
}

export interface IApprovalRequest {
  checker?: string
  checkerComments?: string
  id: string
  maker: string
  dateCreated: string
  dateModified: string
  makerCheckerType: {
    channel: string
    checkerPermissions: string[]
    description?: string
    makerPermissions: string[]
    module: string
    name: string
    overridePermissions: string[]
    type: string
  }
  entityId: string
  entity: string
  diff: IDiffValues[]
  makerComments?: string
  status: ApprovalRequestStatus
}
export interface IDiffValues {
  field: string
  name?: string
  oldValue: IDiffValues[] | string
  newValue: IDiffValues[] | string
}

export interface SelectedApprovalRequests {
  firstName: string
  lastName: string
  createdBy: string
  modifiedBy: string
  buyerCommission: number
  commissionType: string
  producerCommission: number
  penalty: number
  centralBankRate: number
  withHoldingTax: number
  comments: string
}

//RequestTypeId
export interface IMakerCheckerTypeModule {
  dateCreated: string
  dateModified: string
  createdBy: string
  updatedBy: string
  approvalRequest: string
}

export interface EattaModule extends IMakerCheckerTypeModule {
  id: string
  moduleName: string
  channelId: string
}

export interface IMakerCheckerTypePermission extends IMakerCheckerTypeModule {
  id: string
  name: string
  permissionId: string
  visible: boolean
  module: EattaModule
  systemIdentifier: string
}

export interface IMakerCheckerType {
  name: string
  id: string
  description: string
  makerPermissions: IMakerCheckerTypePermission[]
  checkerPermissions: IMakerCheckerTypePermission[]
  overridePermissions: IMakerCheckerTypePermission[]
}

export type IMakerCheckerTypeList = IMakerCheckerType[]

export interface MakerCheckerActivity {
  action: string
  actionedBy: string
  email?: string
  actionedDate: string
  comment?: string
}
