'use client'

import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Stack,
  TextField,
  Typography,
  Link,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import React from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import Grid from '@mui/material/Grid2'
import {
  ChevronLeftIcon,
  EmailIcon,
  PhoneIcon,
} from '@dtbx/ui/components/SvgIcons'
import { COMPANY_TYPES_COMBINED } from '@/store/interfaces'
import { FileUpload } from '@/components/FileUpload'
import { handleCustomerSupport } from '@/store/actions'

const validationSchema = Yup.object({
  membershipType: Yup.string().required('Membership type is required'),
  organizationCode: Yup.string().required('Organization Code is required'),
  email: Yup.string()
  .required('Phone number or email is required')
  .test('is-phone-or-email', 'Must be a valid email or phone number', function (value) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const phoneRegex = /^[0-9+\-\s]{7,15}$/
    return emailRegex.test(value || '') || phoneRegex.test(value || '')
  }),
  issueDescription: Yup.string().required('Please describe your issue'),
  file: Yup.mixed<File>().optional(),
})

export default function CustomerSupportPage() {
  const dispatch = useAppDispatch()

  const { isLoadingLogin } = useAppSelector((store) => store.auth)

  const formik = useFormik({
    initialValues: {
      membershipType: '',
      organizationCode: '',
      email: '',
      issueDescription: '',
      file: File,
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      try {
        const file = values.file instanceof File ? values.file : null

        await handleCustomerSupport(
          dispatch,
          {
            membershipType: values.membershipType,
            organizationCode: values.organizationCode,
            email: values.email,
            issueDescription: values.issueDescription,
            file: file as File,
          },
          {}
        )
        formik.resetForm()
      } catch (error) {
        console.error('error', error)
      }
    },
  })

  return (
    <FormikProvider value={formik}>
      <Form onSubmit={formik.handleSubmit}>
        <Grid container spacing={2}>
          <Grid
            size={{ xs: 12, sm: 12, md: 12, lg: 8 }}
            sx={{
              border: '1px solid #ccc',
              borderRadius: '8px',
              padding: 2,
            }}
          >
            <Link
              href="/auth"
              rel="noopener noreferrer"
              sx={{ textDecoration: 'none', display: 'inline-flex' }}
            >
              <Typography
                variant="body2"
                fontWeight={600}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  gap: 1,
                  marginBottom: 1,
                  color: '#D92D20',
                }}
              >
                <ChevronLeftIcon fontSize="small" />
                Back to login
              </Typography>
            </Link>

            <Stack spacing={3}>
              <Typography variant="h5" fontWeight="600">
                Send us a message:
              </Typography>

              <Box>
                <Typography variant="body2" fontWeight={500}>
                  Type of Membership
                </Typography>
                <Autocomplete
                  disablePortal
                  size="small"
                  id="membershipType"
                  options={COMPANY_TYPES_COMBINED}
                  {...formik.getFieldProps('membershipType')}
                  onChange={(_, value) => {
                    formik.setFieldValue('membershipType', value)
                  }}
                  renderInput={(params) => (
                    <TextField
                      hiddenLabel
                      placeholder="Select type"
                      {...params}
                      error={Boolean(
                        formik.touched.membershipType &&
                          formik.errors.membershipType
                      )}
                      helperText={
                        formik.touched.membershipType &&
                        formik.errors.membershipType
                          ? formik.errors.membershipType
                          : ''
                      }
                    />
                  )}
                />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={500}>
                  EATTA Organization Code:
                </Typography>
                <TextField
                  fullWidth
                  size="small"
                  {...formik.getFieldProps('organizationCode')}
                  error={Boolean(
                    formik.touched.organizationCode &&
                      formik.errors.organizationCode
                  )}
                  helperText={
                    formik.touched.organizationCode &&
                    formik.errors.organizationCode
                  }
                />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={500}>
                  Enter your phone number or email
                </Typography>
                <TextField
                  fullWidth
                  size="small"
                  {...formik.getFieldProps('email')}
                  error={Boolean(formik.touched.email && formik.errors.email)}
                  helperText={formik.touched.email && formik.errors.email}
                />
                <Typography variant="caption" color="text.secondary">
                  This helps us get in touch with you faster.
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={500}>
                  Describe your issue
                </Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  size="small"
                  placeholder="Enter description..."
                  {...formik.getFieldProps('issueDescription')}
                  error={Boolean(
                    formik.touched.issueDescription &&
                      formik.errors.issueDescription
                  )}
                  helperText={
                    formik.touched.issueDescription &&
                    formik.errors.issueDescription
                  }
                />
              </Box>

              <Box>
                <FileUpload
                  progress={0}
                  description="PDF or PNG, JPG image allowed."
                  mimeTypes={['application/pdf', 'image/png', 'image/jpeg']}
                  required={false}
                  onFileChange={(file) => formik.setFieldValue('file', file)}
                />
              </Box>

              <Button
                variant="contained"
                type="submit"
                disabled={!formik.isValid}
                endIcon={
                  isLoadingLogin ? (
                    <CircularProgress size={20} thickness={3} />
                  ) : undefined
                }
              >
                Submit
              </Button>
            </Stack>
          </Grid>

          <Grid size={{ xs: 12, sm: 12, md: 12, lg: 4 }}>
            <Stack spacing={2}>
              <Typography variant="h6" color="primary" fontWeight={600}>
                Or Talk to Us Directly
              </Typography>

              <EmailIcon />
              <Stack direction="row" alignItems="flex-start" spacing={2}>
                <Box>
                  <Typography
                    variant="body1"
                    color="text.secondary"
                    fontWeight={500}
                  >
                    EMAIL
                  </Typography>
                  <Typography variant="body1" color="primary">
                    <EMAIL>
                  </Typography>
                </Box>
              </Stack>

              <PhoneIcon />
              <Stack direction="row" alignItems="flex-start" spacing={2}>
                <Box>
                  <Typography
                    variant="body1"
                    color="text.secondary"
                    fontWeight={500}
                  >
                    PHONE NUMBER
                  </Typography>
                  <Typography variant="body1" color="primary">
                    0719 031 888 / 0732 121 888
                  </Typography>
                </Box>
              </Stack>
            </Stack>
          </Grid>
        </Grid>
      </Form>
    </FormikProvider>
  )
}
