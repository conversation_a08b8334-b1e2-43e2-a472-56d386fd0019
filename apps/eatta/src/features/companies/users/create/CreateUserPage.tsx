'use client'
import { Box, Divider, Stack, Typography } from '@mui/material'
import { BackButton } from '@dtbx/ui/components/Button'
import { useRouter } from 'next/navigation'
import React, { FC, useEffect, useMemo, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import Grid from '@mui/material/Grid2'
import Complete from './Complete'
import UserProfile from './UserProfile'
import UserDetailsVerification from './UserDetailsVerification'
import { CustomStepper } from '@/components/CustomStepper'
import IdentityVerification from './IdentityVerification'
import { getOrganizationById } from '@/store/actions'
import { CompanyUser, UserSetUpStage } from '@/store/interfaces'
import { ApprovalRequestStatus } from '@/store/interfaces/makerChecker'
import { safeJsonParse } from '@/utils/objectUtil'
import { checkIsMaker, HasAccessToRights } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'

type Step = {
  title: string
  description?: string
}
const STEPS: Step[] = [
  {
    title: 'Identity Details',
    description: 'Names and identification details',
  },
  {
    title: 'User Profile Set Up',
    description: 'Contact information and roles.',
  },
  {
    title: 'Confirm and Submit',
    description:
      'Confirm user details and submit profile for checker verification.',
  },
  {
    title: 'Checker Verification',
    description: 'User added successfully.Received activation email.',
  },
  {
    title: 'Complete',
    description:
      'User is successfully onboarded and receives an activation email.',
  },
]

export enum CreateUserSteps {
  VERIFICATION = 1,
  PROFILE,
  SUBMISSION,
  CHECKER,
  COMPLETE,
}

interface CreateCompanyPageProps {
  organizationId: string
}

const CreateCompanyPage: FC<CreateCompanyPageProps> = ({ organizationId }) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const [currentStep, setCurrentStep] = useState<CreateUserSteps>(1)

  const { selectedCompany, onboardingUser, onboardedUser } = useAppSelector(
    (state) => state.companies
  )
  const user: CompanyUser | null = useMemo(
    () => safeJsonParse(onboardingUser?.entity),
    [onboardingUser]
  )

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_USER),
    []
  )

  const hideCheckerStep = useMemo(() => {
    if (!onboardingUser) return isSuper
    return isSuper && checkIsMaker(onboardingUser?.maker)
  }, [isSuper, onboardingUser])

  const steps: Step[] = useMemo(() => {
    let steps: Step[] = STEPS
    if (hideCheckerStep) {
      steps = steps.filter((_step, index) => index !== 3)
    }
    return steps
  }, [isSuper])

  const handleSetNextStep = (
    step: UserSetUpStage,
    status: ApprovalRequestStatus
  ) => {
    //Maker
    if (status === 'STAGING' && !onboardedUser) {
      switch (step) {
        case 'VERIFICATION':
          setCurrentStep(CreateUserSteps.PROFILE)
          break
        case 'PROFILE':
          setCurrentStep(CreateUserSteps.SUBMISSION)
          break
      }
      return
    }
    //Checker
    if (onboardedUser && !isSuper) {
      setCurrentStep(CreateUserSteps.COMPLETE)
    } else {
      setCurrentStep(CreateUserSteps.CHECKER)
    }
  }

  useEffect(() => {
    const step = user?.stepName
    const status = onboardingUser?.status
    if (step && status) {
      handleSetNextStep(step, status)
    }
  }, [onboardingUser, user])

  useEffect(() => {
    const getOrganization = async () => {
      await getOrganizationById(dispatch, organizationId)
    }
    getOrganization()
  }, [organizationId])

  return (
    <Stack
      sx={{
        backgroundColor: '#F2F4F7',
        height: '100%',
      }}
    >
      <Stack
        sx={{ backgroundColor: '#FFFFFF' }}
        direction="row"
        paddingInline={3}
        paddingBlock={2}
        spacing={2}
        alignItems="flex-start"
      >
        <BackButton onClick={() => router.back()} />
        <Stack>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            Add User
          </Typography>
          <Typography variant="body1" sx={{ color: '#475467' }}>
            Once added, they will be able to log in as members of{' '}
            {selectedCompany?.name}
          </Typography>
        </Stack>
      </Stack>

      <Divider />

      <Box sx={{ flexGrow: 1, height: '100%', overflow: 'hidden' }}>
        <Grid
          container
          spacing={2}
          sx={{ height: '100%', overflowY: { sm: 'auto', md: 'hidden' } }}
        >
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 4, lg: 3 }}
            sx={{ height: { md: '100%' }, overflowY: 'auto' }}
          >
            <CustomStepper
              steps={steps}
              currentStep={currentStep}
              setStep={(step: CreateUserSteps) => {
                // Prevent going back to previous steps when in CHECKER step
                if (currentStep === CreateUserSteps.CHECKER && step < currentStep) {
                  return
                }
                setCurrentStep(step)
              }}
            />
          </Grid>
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 8, lg: 9 }}
            sx={{ height: { md: '100%' }, overflowY: { md: 'auto' } }}
          >
            <Box width={{ sm: '100%', md: '80%', lg: '60%' }}>
              {(() => {
                switch (currentStep) {
                  case CreateUserSteps.VERIFICATION:
                    return <IdentityVerification setStep={setCurrentStep} />
                  case CreateUserSteps.PROFILE:
                    return <UserProfile setStep={setCurrentStep} />
                  case CreateUserSteps.SUBMISSION:
                    return (
                      <UserDetailsVerification
                        actionType={'SUBMISSION'}
                        setStep={setCurrentStep}
                      />
                    )
                  case CreateUserSteps.CHECKER:
                    if (hideCheckerStep) {
                      return <Complete />
                    }
                    return (
                      <UserDetailsVerification
                        actionType={'CHECKER'}
                        setStep={setCurrentStep}
                      />
                    )
                  case CreateUserSteps.COMPLETE:
                    return <Complete />
                }
              })()}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Stack>
  )
}

export default CreateCompanyPage
