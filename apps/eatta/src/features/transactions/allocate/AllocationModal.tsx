/**
 * <AUTHOR> on 05/06/2025
 */
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Stack,
  Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { EslipDetails } from '@/features/transactions/allocate/EslipDetails'
import { InvoiceEslip } from '@/store/interfaces'
import React, { FC, useMemo, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { TransactionDetails } from '@/features/transactions/allocate/TransactionDetails'
import {
  AllocateTransactionRequest,
  Transaction,
} from '@/store/interfaces/transactions'
import {
  allocateTransaction,
  approveTransactionRequest,
} from '@/store/actions/transactions'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { CommentDialog } from '@/components/CommentDialog'
import { ArrowDownward } from '@mui/icons-material'
import MakerCheckerActivities from '@/components/MakerCheckerActivities'

interface AllocationModalProps {
  actionType: 'MAKER' | 'CHECKER'
  open: boolean
  onClose: (tabIndex?: number) => void
  onEslipSelected?: (eslip: InvoiceEslip) => void
}

export const AllocationModal: FC<AllocationModalProps> = ({
  actionType,
  open,
  onClose,
}) => {
  const dispatch = useAppDispatch()
  const {
    allocateEslip,
    selectedTransaction,
    isAllocatingTransaction,
    approvalRequest,
  } = useAppSelector((state) => state.transactions)
  const [openCommentDialog, setOpenCommentsDialog] = useState(false)
  const [allocateRequest, setAllocateRequest] =
    useState<AllocateTransactionRequest>({
      comments: '',
      invoiceId: '',
      invoicePaymentId: '',
    })

  const isMaker = actionType == 'MAKER'

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_ALLOCATE_PAYMENT),
    []
  )

  const handleAllocation = async (
    transaction: Transaction,
    eslip: InvoiceEslip
  ) => {
    setAllocateRequest({
      comments: '',
      invoiceId: eslip.id,
      invoicePaymentId: transaction.id,
    })
    setOpenCommentsDialog(true)
  }

  const handleOnSubmitCommentsDialog = async (comments: string) => {
    setOpenCommentsDialog(false)

    //Maker
    if (isMaker) {
      await allocateTransaction(
        isSuper,
        dispatch,
        { ...allocateRequest, comments },
        () => onClose(isSuper ? 0 : 1)
      )
    }
    //Approval
    if (!approvalRequest) return
    await approveTransactionRequest(
      dispatch,
      approvalRequest.id,
      comments,
      () => onClose(0)
    )
  }

  if (!allocateEslip || !selectedTransaction) {
    return
  }

  return (
    <Dialog fullWidth maxWidth="md" open={open}>
      <DialogTitle fontWeight={600}>
        <Stack spacing={1}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography variant="h5" fontWeight="bold">
              {isMaker ? 'Confirm' : 'Approve'} Allocation.
            </Typography>
            <IconButton
              sx={{
                height: '2rem',
                width: '2rem',
                border: '1px solid #D0D5DD',
                borderRadius: '0.5rem',
              }}
              onClick={() => onClose()}
            >
              <CloseIcon />
            </IconButton>
          </Stack>

          <Typography variant="subtitle2">
            {isMaker
              ? 'This request will be submitted for approval once you confirm.'
              : 'This request is awaiting approval'}
          </Typography>
        </Stack>
      </DialogTitle>
      <Divider />
      <DialogContent>
        <Stack spacing={2} useFlexGap>
          <TransactionDetails transaction={selectedTransaction} />

          <Stack alignItems="center" justifyContent="center">
            <Stack
              alignItems="center"
              justifyContent="center"
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '50%',
                width: '2.25rem',
                height: '2.25rem',
              }}
            >
              <ArrowDownward />
            </Stack>
          </Stack>

          <EslipDetails eslip={allocateEslip} />
          {!isMaker && (
            <MakerCheckerActivities
              activities={[
                {
                  action: 'E-Slip allocated by',
                  actionedBy: approvalRequest?.maker ?? '',
                  actionedDate: approvalRequest?.dateCreated ?? '',
                  comment: approvalRequest?.makerComments ?? '',
                },
              ]}
            />
          )}

          <AccessControlWrapper
            rights={
              isMaker
                ? ACCESS_CONTROLS.ALLOCATE_PAYMENT
                : ACCESS_CONTROLS.ACCEPT_ALLOCATE_PAYMENT
            }
            makerId={approvalRequest?.maker}
            isMake={isMaker}
          >
            <Button
              variant="contained"
              disabled={isAllocatingTransaction}
              onClick={() =>
                handleAllocation(selectedTransaction, allocateEslip)
              }
              endIcon={
                isAllocatingTransaction ? (
                  <CircularProgress size={20} thickness={3.0} />
                ) : undefined
              }
            >
              {isMaker ? 'Looks Good' : 'Approve'}
            </Button>
          </AccessControlWrapper>

          {openCommentDialog && (
            <CommentDialog
              open={openCommentDialog}
              onClose={() => setOpenCommentsDialog(false)}
              onSubmit={handleOnSubmitCommentsDialog}
            />
          )}
        </Stack>
      </DialogContent>
    </Dialog>
  )
}
