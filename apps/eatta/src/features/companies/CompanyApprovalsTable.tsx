import React, { useEffect, useState } from 'react'
import {
  Button,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import {
  Company,
  CompanyFilters,
  Order,
  OrganizationStatus,
} from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { getCompanyApprovalRequests } from '@/store/actions'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import AddIcon from '@mui/icons-material/Add'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { AccessControlWrapper, formatTimestamp } from '@dtbx/store/utils'
import { sortData } from '@/utils/sortTableData'
import TableSkeleton from '@/components/TableSkeleton'
import { ArrowForwardIos } from '@mui/icons-material'
import { IApprovalRequest } from '@/store/interfaces/makerChecker'
import { resetOnboardingState, setOnboardingCompany } from '@/store/reducers'
import { ACCESS_CONTROLS, EATTA_MODULES } from '@/utils/constants'
import { APPROVAL_STATUS_MAP } from '@/utils/statusChips'
import { safeJsonParse } from '@/utils/objectUtil'

export const NEXT_STAGE_MAP: Record<string, string> = {
  PROFILE: 'Payment Details',
  PAYMENT: 'Maker Submit',
  SUBMISSION: 'Checker Verification',
  VERIFICATION: 'Complete',
}

interface ApprovalTableProps {
  status: OrganizationStatus
  filters: CompanyFilters
  onApprovalCountChange?: (count: number) => void
}

const CompanyApprovalsTable: React.FC<ApprovalTableProps> = ({
  status,
  filters,
  onApprovalCountChange,
}) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { companyApprovalRequests, isLoading } = useAppSelector(
    (state) => state.companies
  )

  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<keyof IApprovalRequest>('dateCreated')

  const isPendingApproval = status === 'PENDING'

  const handleCreateCompany = () => {
    router.push('/backoffice/companies/create')
  }

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof IApprovalRequest)
  }

  const sortKey = orderBy as keyof IApprovalRequest
  const companiesDataSorted = sortData(
    [...companyApprovalRequests.data],
    sortKey,
    order
  )

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }

  const handleNextStage = (approvalRequest: IApprovalRequest) => {
    dispatch(resetOnboardingState())
    dispatch(setOnboardingCompany(approvalRequest))
    const path = isPendingApproval
      ? '/backoffice/companies/approve'
      : '/backoffice/companies/create'
    router.push(path)
  }

  useEffect(() => {
    const fetchApprovals = async () => {
      await getCompanyApprovalRequests(dispatch, {
        status: status,
        module: EATTA_MODULES.organizations,
        page: paginationOptions.page,
        size: paginationOptions.size,
        channel: 'EATTA',
      })
    }
    fetchApprovals()
  }, [dispatch, filters, paginationOptions.page, status])

  useEffect(() => {
    onApprovalCountChange &&
      onApprovalCountChange(companyApprovalRequests.totalElements)
  }, [companyApprovalRequests.totalElements])

  return isLoading ? (
    <TableSkeleton rowCount={15} columnCount={6} />
  ) : companyApprovalRequests.data.length === 0 ? (
    <EmptyPage
      title="No companies found"
      message="Please create a new company to get started"
      bgUrl={'/eatta/combo.svg'}
      action={
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_ORGANIZATION}>
          <Button
            variant="contained"
            type="submit"
            startIcon={<AddIcon />}
            onClick={handleCreateCompany}
          >
            New Company
          </Button>
        </AccessControlWrapper>
      }
    />
  ) : (
    <TableContainer
      component={Paper}
      sx={{
        boxShadow: 'none',
        '& .MuiTableCell-root': {
          paddingInline: '1.5rem',
          paddingBlock: '0.5rem',
          textAlign: 'left',
        },
      }}
    >
      <Table sx={{ minWidth: 650 }} aria-label="company table" size="small">
        <CustomTableHeader
          order={order}
          orderBy={orderBy}
          headLabel={[
            {
              id: 'code',
              label: 'EATTA Member Code',
              alignRight: false,
            },
            { id: 'name', label: 'Company Name', alignRight: false },
            { id: 'type', label: 'Company Type', alignRight: false },
            { id: 'maker', label: 'Maker', alignRight: false },
            {
              id: 'dateCreated',
              label: 'Date Created',
              alignRight: false,
            },
            {
              id: 'stepName',
              label: 'Stage',
              alignRight: false,
            },
            {
              id: 'status',
              label: 'Status',
              alignRight: false,
            },
            { id: '', label: '', alignRight: false },
          ]}
          showCheckbox={false}
          rowCount={companyApprovalRequests.data.length}
          numSelected={0}
          onRequestSort={handleRequestSort}
        />
        <TableBody>
          {companiesDataSorted.map((approvalRequest: IApprovalRequest) => {
            const { id, entity, maker, dateCreated, status } = approvalRequest
            const company = safeJsonParse<Company>(entity)
            if (!company) return null
            const { name, type, code, stepName } = company

            return (
              <TableRow hover key={id} tabIndex={-1} role="checkbox">
                <TableCell component="th" scope="row" id={code}>
                  {code ?? 'N/A'}
                </TableCell>
                <TableCell>{name}</TableCell>
                <TableCell>
                  <StatusChip
                    status={
                      type === 'Broker' || type === 'Buyer'
                        ? 'success'
                        : 'error'
                    }
                    label={type}
                  />
                </TableCell>
                <TableCell>{maker}</TableCell>
                <TableCell>{formatTimestamp(dateCreated)}</TableCell>
                <TableCell>
                  <StatusChip label={NEXT_STAGE_MAP[stepName]} />
                </TableCell>
                <TableCell>
                  <StatusChip label={APPROVAL_STATUS_MAP[status]} />
                </TableCell>
                <TableCell>
                  <AccessControlWrapper
                    rights={
                      isPendingApproval
                        ? ACCESS_CONTROLS.ACCEPT_CREATE_ORGANIZATION
                        : ACCESS_CONTROLS.CREATE_ORGANIZATION
                    }
                    isMake={!isPendingApproval}
                    makerId={maker}
                  >
                    <IconButton
                      onClick={() => handleNextStage(approvalRequest)}
                    >
                      <ArrowForwardIos />
                    </IconButton>
                  </AccessControlWrapper>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell
              sx={{ paddingInline: 0 }}
              align="center"
              height={40}
              colSpan={12}
            >
              {companyApprovalRequests.totalNumberOfPages > 0 && (
                <CustomPagination
                  options={{
                    ...paginationOptions,
                    totalPages: companyApprovalRequests.totalNumberOfPages,
                  }}
                  handlePagination={handlePagination}
                />
              )}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  )
}

export default CompanyApprovalsTable
