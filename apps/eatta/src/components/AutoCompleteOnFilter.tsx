import React from 'react'
import { Autocomplete, Stack, TextField, Select, MenuItem } from '@mui/material'
import { KeyboardArrowDownRounded } from '@mui/icons-material'

export const COMPANY_TYPES = [
  'Broker',
  'Producer',
  'Partner',
  'Buyer',
  'Warehouse',
  'Factory',
]

export const AutoCompleteOnFilter = () => {
  return (
    <Stack spacing={2} direction="row" width="50%">
      <FilterBox
        filterValues={COMPANY_TYPES}
        selectedSearchBy={COMPANY_TYPES[0]}
      />
      <Autocomplete
        sx={{ width: '70%' }}
        disablePortal
        size="small"
        id="type"
        options={COMPANY_TYPES}
        renderInput={(params) => (
          <TextField hiddenLabel placeholder="Select type" {...params} />
        )}
      />
    </Stack>
  )
}

export const FilterBox = ({
  filterValues,
  selectedSearchBy,
  onHandleSearch,
}: {
  selectedSearchBy: string
  filterValues: string[]
  onHandleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
}) => {
  return (
    <Stack direction={'row'}>
      <Select
        size="medium"
        onChange={() => {}}
        IconComponent={(iconProps) => (
          <KeyboardArrowDownRounded
            {...iconProps}
            sx={{
              color: '#667085',
              marginLeft: '0.5rem',
              ...(iconProps?.sx || {}),
            }}
          />
        )}
        value={selectedSearchBy}
        sx={{
          width: '100%',
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '2px !important',
          },
          background: '#FFFFFF',
          '& fieldset': {
            border: `1px solid #D0D5DD !important`,
          },
          borderRadius: '8px',
          color: '#667085',
        }}
      >
        {filterValues.map((value) => (
          <MenuItem key={value} value={value}>
            {`Search for ${value}`}
          </MenuItem>
        ))}
      </Select>
    </Stack>
  )
}
