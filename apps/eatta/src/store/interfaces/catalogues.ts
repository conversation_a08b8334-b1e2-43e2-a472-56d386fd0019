import { PageFilters } from '@/store/interfaces/filters'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { BrokerInvoice, DisbursementType, InvoiceEntryStatus } from './invoices'

export const CATALOGUE_STATUS_OPTIONS = [
  'NEW',
  'PARTIAL',
  'PAID',
  'BATCHED',
  'FAILED',
  'INVOICED',
] as const

export type InvoiceStatus = (typeof CATALOGUE_STATUS_OPTIONS)[number]

export type AuctionType = 'PRE_AUCTION' | 'POST_AUCTION'

export interface CatalogueFilters extends PageFilters {
  year: number
  saleDate: string
  saleCode?: string
  producer?: string
  amount?: number
  lotNo?: string
  broker?: string
  invoiceNo?: string
  status?: InvoiceStatus | InvoiceEntryStatus
  buyerName?: string
  totalPrice?: number
  dateCreated?: string
  invoiceStatus?: string
  buyer?: string
  buyerCode?: string
  factory?: string
  disbursementType?: DisbursementType
  mark?: string
  wareHouse?: string
  manufacturedDate?: string
  fromSaleDate?: string
  toSaleDate?: string
  auctionType?: AuctionType
  groupedBy?: string[]
}

export interface AuctionResponse extends PaginatedResponse<AuctionGroup> {}

export interface AuctionGroup {
  producer: string
  warehouse: string
  buyer: string
  broker: string
  factories: FactoryGroup[]
}

export interface FactoryGroup {
  name: string
  catalogues: Catalogue[]
}
export interface CataloguePayload {
  saleDate: string
  file: File
  auctionType: AuctionType
}

export interface Catalogue {
  producer: string
  factory: string
  id: string
  saleDate: string
  broker: string
  buyer: string
  buyerName: string
  netWeight: number
  lotNo: number
  invoiceNo: string
  pkgs: number
  kgs: number
  status: string
  wareHouse: string
  type: string
  grade: string
  value: number
  comment: string
  manufacturedDate: string
  invoiceStatus: InvoiceStatus
  salePrice: number
  totalValue: number
  gardenInvoice: string
  brokerCommissionFromProducer: number
  brokerProducerWithholdingTax: number
  warehouseCharges: number
  penalties: number
  datePaid: string
  totalWeight: number
  pricePerKg: number
  invoiceEntryStatus: InvoiceEntryStatus
  lateDays: number | null
}

export interface SheetConfig {
  sheetName: string
  columnRange: string
}

export interface AccountSalesEntry {
  brokerInvoiceEntries: BrokerInvoiceEntry[]
  mainGrades: GradeSummary
  allGrades: GradeSummary
  secondaryGrades: GradeSummary
}
export interface GradeSummary {
  totalNetSoldWeight: number
  totalGrossValue: number
  totalBrokerCommissionFromProducer: number
  totalWareHouseCharges: number
  totalNetProceedsDue: number
  totalWithholdingTax: number
}
export interface BrokerInvoiceEntry {
  id: string
  invoiceNumber: string
  gardenInvoice: string | null
  buyer: string
  pkgs: number
  type: string
  lotNo: number
  auctionDate: string
  buyerCode: string
  buyerName: string | null
  broker: string
  factory: string
  grade: string | null
  totalWeight: number
  saleDate: string
  category: string | null
  pricePerKg: number
  netWeight: number
  unitWeight: number
  totalValue: number
  warehouseCharges: number
  brokerShare: number
  withholdingTax: number
  netAmount: number
  netPayable: number
  warehouse: string | null
  netProceedsDue: number
  bags: number | null
  datePaid: string | null
  teaReleaseDocumentDate: string | null
  status: string
  brokerCommissionFromProducer: number
  producerShare: number
  errors: string | null
  auctionEntry: AuctionEntry
  brokerInvoice: BrokerInvoice
}

export interface AuctionEntry {
  lotNo: number
  buyer: string
  buyerCode: string
  saleDate: string
  broker: string
  warehouseCompany: string | null
  grade: string
  invoiceNo: string
  netWeight: number
  unitWeight: number
  totalPrice: number | null
  type: string
}
