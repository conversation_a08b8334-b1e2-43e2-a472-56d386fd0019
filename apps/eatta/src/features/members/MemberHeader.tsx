import { useState, FC, useEffect } from 'react'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { Stack, IconButton, Typography, Divider, Button } from '@mui/material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import { UserFilters, PageFilters } from '@/store/interfaces'
import { resetOnboardingState } from '@/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'
import { AutoCompleteOnFilter } from '@/components/AutoCompleteOnFilter'
import {
  COMPANY_TYPES_COMBINED,
  Company,
  CompanyFilters,
} from '@/store/interfaces'
import { getCompanies } from '@/store/actions'

interface MemberHeaderProps {
  filters: UserFilters
  onSearchChange: (key: keyof UserFilters, value: string) => void
  onCompanyCodeChange?: (companyCode: string | null) => void
}

const MemberHeader: FC<MemberHeaderProps> = ({
  filters,
  onSearchChange,
  onCompanyCodeChange,
}) => {
  type UserSearchByType = keyof Omit<UserFilters, keyof PageFilters>
  const [searchValue, setSearchValue] = useState('')
  const [searchBy, setSearchBy] = useState<UserSearchByType>('email')
  const searchByValue = Object.keys(filters).filter(
    (key) => typeof filters[key as keyof UserFilters] === 'string'
  )

  const searchByLabels: Record<string, string> = {
    name: 'Name',
    email: 'Email',
    phoneNumber: 'Phone Number',
    username: 'Username',
    status: 'Status',
  }

  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { companiesResponse, isLoading } = useAppSelector(
    (state) => state.companies
  )

  // Company filter states
  const [selectedCompanyType, setSelectedCompanyType] = useState<string>(
    COMPANY_TYPES_COMBINED[0]
  )
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null)
  const [companySearchValue, setCompanySearchValue] = useState('')

  const debouncedSearchValue = useDebounce(searchValue, 400)
  const debouncedCompanySearch = useDebounce(companySearchValue, 400)

  useEffect(() => {
    onSearchChange(searchBy, debouncedSearchValue)
  }, [debouncedSearchValue, searchBy])

  // Fetch companies when company type or search changes
  useEffect(() => {
    if (selectedCompanyType) {
      const filters: CompanyFilters = {
        page: 1,
        size: 10,
        type: selectedCompanyType,
        name: debouncedCompanySearch || undefined,
      }
      getCompanies(dispatch, filters)
    }
  }, [selectedCompanyType, debouncedCompanySearch, dispatch])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(value)
  }

  const handleSearchByChange = (value: string) => {
    const newSearchBy = value as UserSearchByType
    setSearchBy(newSearchBy)
    setSearchValue('')
    onSearchChange(searchBy, '')
  }
  const handleCreateUser = () => {
    dispatch(resetOnboardingState())
    router.push(`/backoffice/members/create`)
  }

  const handleCompanyTypeChange = (companyType: string) => {
    setSelectedCompanyType(companyType)
    setSelectedCompany(null)
    setCompanySearchValue('')
  }

  const handleCompanySelect = (company: Company | null) => {
    setSelectedCompany(company)
    // Pass the company code to parent component to handle member filtering
    onCompanyCodeChange?.(company?.code || null)
    console.log('Selected company:', company)
  }

  const handleCompanySearchChange = (searchValue: string) => {
    setCompanySearchValue(searchValue)
  }

  return (
    <Stack>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <IconButton
            sx={{
              borderRadius: '8px',
              border: '1px solid #D0D5DD',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              display: 'none',
            }}
            onClick={() => router.back()}
          >
            <ArrowBackIosNewOutlinedIcon />
          </IconButton>
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            Members
          </Typography>
        </Stack>
      </Stack>
      <Divider />
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          justifyContent: 'space-between',
        }}
        direction="row"
        spacing={2}
        paddingInline={3}
        paddingBlock={2}
        alignItems="center"
        justifyContent="flex-start"
      >
        <CustomFilterUserBox
          searchValue={searchValue}
          searchByValues={searchByValue}
          searchByLabel={searchByLabels}
          selectedSearchBy={searchBy}
          onHandleSearch={handleSearch}
          setSearchByValue={handleSearchByChange}
          searchPlaceHolder={`Search ${searchBy || 'member'}`}
          prependSearchBy={true}
        />
        <AutoCompleteOnFilter<Company>
          filterTypes={COMPANY_TYPES_COMBINED}
          options={companiesResponse.data || []}
          loading={isLoading}
          selectedFilterType={selectedCompanyType}
          selectedOption={selectedCompany}
          getOptionLabel={(company) => `${company.name} (${company.code})`}
          onFilterTypeChange={handleCompanyTypeChange}
          onOptionSelect={handleCompanySelect}
          onInputChange={handleCompanySearchChange}
          placeholder="Search by company type"
          noOptionsText={`No ${selectedCompanyType} companies found`}
        />
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USER}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddOutlinedIcon />}
            sx={{
              borderRadius: '8px',
              whiteSpace: 'nowrap',
              minWidth: 'fit-content',
            }}
            onClick={handleCreateUser}
          >
            Add Member
          </Button>
        </AccessControlWrapper>
      </Stack>
    </Stack>
  )
}

export default MemberHeader
