import {useState, FC} from 'react'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { Stack, IconButton, Typography, Divider, Button } from '@mui/material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import router from 'next/router'
import { UserFilters, PageFilters } from '@/store/interfaces'

interface MemberHeaderProps {
  filters: UserFilters
  onSearchChange: (key: keyof UserFilters, value: string) => void
}

const MemberHeader: FC<MemberHeaderProps> = ({ filters, onSearchChange }) => {
  type UserSearchByType = keyof Omit<UserFilters, keyof PageFilters>
  const [searchValue, setSearchValue] = useState('')
  const [searchBy, setSearchBy] = useState<UserSearchByType>('email')
  const searchByValue = Object.keys(filters).filter(
    (key) => typeof filters[key as keyof UserFilters] === 'string'
  )

  return (
    <Stack>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <IconButton
            sx={{
              borderRadius: '8px',
              border: '1px solid #D0D5DD',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            onClick={() => router.back()}
          >
            <ArrowBackIosNewOutlinedIcon />
          </IconButton>
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            {/* {selectedCompany?.name} */} Members
          </Typography>
        </Stack>
      </Stack>
      <Divider />
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          justifyContent: 'space-between',
        }}
        direction="row"
        spacing={2}
        paddingInline={3}
        paddingBlock={2}
        alignItems="center"
        justifyContent="flex-start"
      >
        <CustomFilterUserBox
          searchValue={searchValue}
          searchByValues={searchByValue}
          selectedSearchBy={searchBy}
          onHandleSearch={(e) => setSearchValue(e.target.value)}
          setSearchByValue={(value: string) =>
            setSearchBy(value as UserSearchByType)
          }
          searchPlaceHolder={'Search member'}
          prependSearchBy={true}
        />
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USER}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddOutlinedIcon />}
            sx={{ borderRadius: '8px' }}
            // onClick={() => handleCreateUser(selectedCompany?.id ?? '')}
          >
            Add Member
          </Button>
        </AccessControlWrapper>
      </Stack>
    </Stack>
  )
}

export default MemberHeader
