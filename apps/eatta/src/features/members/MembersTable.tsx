import {
  Company,
  CompanyFilters,
  CompanyUser,
  Order,
  PartialCompanyUser,
} from '@/store/interfaces'
import React, { useEffect, useState, useRef } from 'react'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch, useAppSelector } from '@/store'
import { sortData } from '@/utils/sortTableData'

import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import TableSkeleton from '@/components/TableSkeleton'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import {
  Button,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { AccessControlWrapper, formatDate } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { resetOnboardingState } from '@/store/reducers'
import { EditFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { EditUserDialog } from '../companies/users/EditUserDialog'

interface MemberTableProps {
  filters: CompanyFilters
  onMembersCountChange?: (count: number) => void
  onPaginationChange?: (page: number, size: number) => void
}

const MembersTable: React.FC<MemberTableProps> = ({
  filters,
  onMembersCountChange,
  onPaginationChange,
}) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { memberResponse, isLoading } = useAppSelector(
    (state) => state.companies
  )

  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<keyof Company>('dateCreated')
  const [UserData, setUserData] = useState<PartialCompanyUser>({
    id: '',
    email: '',
    name: '',
    phoneNumber: '',
    nationalId: '',
  })
  const [showEditUser, setShowEditUser] = useState(false)
  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof Company)
  }

  const sortKey = orderBy as keyof CompanyUser
  const companiesDataSorted = sortData([...memberResponse.data], sortKey, order)
  const handleEdituser = (row: CompanyUser) => {
    setUserData(row)
    setShowEditUser(true)
  }

  const handleClose = () => {
    setShowEditUser(false)
  }
  const paginationOptions = {
    page: filters.page || 1,
    size: filters.size || 10,
  }

  const handlePagination = async (newOptions: PaginationOptions) => {
    onPaginationChange?.(newOptions.page, newOptions.size)
  }

  const handleCreateUser = () => {
    dispatch(resetOnboardingState())
    router.push(`/backoffice/members/create`)
  }

  const resetPageRef = useRef(false)

  useEffect(() => {
    if (resetPageRef.current) {
      resetPageRef.current = false
      return
    }
  }, [resetPageRef])

  useEffect(() => {
    onMembersCountChange && onMembersCountChange(memberResponse.totalElements)
  }, [memberResponse])

  return isLoading ? (
    <TableSkeleton rowCount={15} columnCount={6} />
  ) : memberResponse.data.length === 0 ? (
    <EmptyPage
      title="No members found"
      message="Please create a new member to get started"
      bgUrl={'/eatta/combo.svg'}
      action={
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USER}>
          <Button
            variant="contained"
            type="submit"
            startIcon={<AddIcon />}
            onClick={handleCreateUser}
          >
            New member
          </Button>
        </AccessControlWrapper>
      }
    />
  ) : (
    <>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
          '& .MuiTableCell-root': {
            paddingInline: '1.5rem',
            paddingBlock: '0.5rem',
            textAlign: 'left',
          },
        }}
      >
        <Table sx={{ minWidth: 650 }} aria-label="company table" size="small">
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={[
              {
                id: 'nationalId',
                label: 'User ID',
                alignRight: false,
              },
              { id: 'name', label: 'Name/Contact', alignRight: false },
              { id: 'companyName', label: 'Company Name', alignRight: false },
              { id: 'companyType', label: 'Company Type', alignRight: false },
              { id: 'phone', label: 'Phone number', alignRight: false },
              {
                id: 'dateCreated',
                label: 'Date Created',
                alignRight: false,
              },
              {
                id: 'status',
                label: 'Status',
                alignRight: false,
              },
              {
                id: 'lastLogin',
                label: 'Last Login',
                alignRight: false,
              },
              { id: '', label: '', alignRight: false },
            ]}
            showCheckbox={false}
            rowCount={memberResponse.data.length}
            numSelected={0}
            onRequestSort={handleRequestSort}
          />
          <TableBody>
            {companiesDataSorted.map((row) => {
              const {
                id,
                firstName,
                lastName,
                email,
                companyName,
                companyType,
                phoneNumber,
                status,
                lastLogin,
                nationalId,
                dateCreated,
              } = row
              return (
                <TableRow hover key={id} tabIndex={-1} role="checkbox">
                  <TableCell component="th" scope="row" id={id}>
                    {nationalId ?? '-'}
                  </TableCell>
                  <TableCell>
                    <Stack>
                      <Typography variant="label1">
                        {firstName || '' + ' ' + lastName || ''}
                      </Typography>
                      <Typography variant="body2">{email}</Typography>
                    </Stack>
                  </TableCell>
                  <TableCell>{companyName}</TableCell>
                  <TableCell>
                    <StatusChip
                      status={
                        companyType === 'Broker' || companyType === 'Buyer'
                          ? 'success'
                          : 'error'
                      }
                      label={companyType}
                    />
                  </TableCell>
                  <TableCell>{phoneNumber}</TableCell>
                  <TableCell>
                    {dateCreated ? formatDate(dateCreated) : '-'}
                  </TableCell>
                  <TableCell>
                    <StatusChip
                      status={status === 'ACTIVE' ? 'success' : 'warn'}
                      label={status}
                    />
                  </TableCell>
                  <TableCell>
                    {lastLogin ? formatDate(lastLogin) : '-'}
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEdituser(row)}>
                      <EditFactoryIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell
                sx={{ paddingInline: 0 }}
                align="center"
                height={40}
                colSpan={12}
              >
                {memberResponse.totalNumberOfPages > 0 && (
                  <CustomPagination
                    options={{
                      ...paginationOptions,
                      totalPages: memberResponse.totalNumberOfPages,
                    }}
                    handlePagination={handlePagination}
                  />
                )}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
      <EditUserDialog
        open={showEditUser}
        UserData={UserData}
        handleClose={handleClose}
      />
    </>
  )
}

export default MembersTable

/*
EATTA USERS
https://dev.dtbafrica.com/api/backoffice-bff/eatta-service/users/TEST699?page=1&size=10&ascending=false
*/
/*
MEMBERS FETCH
https://dev.dtbafrica.com/api/backoffice-bff/eatta-service/users/search/all?page=1&size=10&companyCode=TEST699
*/
