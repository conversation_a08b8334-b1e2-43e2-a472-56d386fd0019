import { DECIMAL_NUMERIC_REGEX } from '@/utils/validators'
import {
  Divider,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Typography,
  Box,
} from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { useAppSelector } from '@/store'
import { KeyboardArrowDownRounded } from '@mui/icons-material'
import { AuctionPicker } from '@/components/AuctionPicker'
import { AuctionSchedule } from '@/store/interfaces'
import { ExportButton } from '@/components/ExportButton'
import { BrokerSelector } from './BrokerSelector'
import { MarkSelector } from './MarkSelector'
import { useDebounce } from '@dtbx/ui/hooks'
import { DisbursementSelector } from './disbursmentSelector'
import { DisbursementType } from '@/store/interfaces/invoices'
import { generateYears } from '@/utils/numberFormatter'
import { StatusSelector } from './statusSelector'
import { AccessWrapper } from './AccessHelper'

export const DEFAULT_FILTER_CONFIG = {
  showYear: true,
  showSaleDate: true,
  showSearchBox: true,
  showStatus: true,
  showBrokerFilter: false,
  showFactory: false,
  showExport: true,
  showDateFilter: false, // This is not used in the component but kept for consistency
  showDisbursementFilter: false,
} as const

export type PageFilterConfig = Record<
  keyof typeof DEFAULT_FILTER_CONFIG,
  boolean
>

export interface MainFilters {
  status?: string
  year: number
  lotNo?: string
  saleDate: string
  broker?: string
  factory?: string
  disbursementType?: DisbursementType | null
}

export interface SearchByValueConfig<T> {
  filterLabel?: string
  filterKey: keyof T
  type: 'string' | 'numeric'
}

interface PageFiltersProps<T> {
  title?: string
  subtitle?: string
  filters: T
  statuses?: readonly string[]
  filterConfig?: PageFilterConfig
  searchByValues?: SearchByValueConfig<T>[] // Updated type
  onSearch: (filters: T) => void
  onExport: (filters: T) => void
}

export function PageFilters<T extends MainFilters>({
  title = 'Sales',
  subtitle = '',
  filters,
  statuses = [],
  filterConfig = DEFAULT_FILTER_CONFIG,
  searchByValues = [],
  onSearch,
  onExport,
}: PageFiltersProps<T>) {
  const viewingYear = useMemo(() => generateYears(), [])
  const [year, setYear] = useState<string>(
    filters.year?.toString() ||
      viewingYear[0] ||
      String(new Date().getFullYear())
  )
  const { isExportingCatalogues } = useAppSelector((state) => state.catalogues)

  const {
    showExport,
    showSearchBox,
    showYear,
    showSaleDate,
    showStatus,
    showBrokerFilter,
    showFactory,
    showDisbursementFilter,
  } = filterConfig
  const [searchBy, setSearchBy] = useState<string>(
    searchByValues.length > 0 ? (searchByValues[0].filterKey as string) : ''
  )

  const searchByLabel = useMemo(() => {
    const map: Record<string, string> = {}
    searchByValues.forEach((value) => {
      map[String(value.filterKey)] =
        value.filterLabel || String(value.filterKey)
    })
    return map
  }, [searchByValues])

  const [searchValue, setSearchValue] = useState('')
  const debouncedSearchValue = useDebounce(searchValue, 500)

  const currentSearchConfig = useMemo(
    () => searchByValues.find((item) => item.filterKey === searchBy),
    [searchByValues, searchBy]
  )

  const searchPlaceHolder = `Search ${searchByLabel?.[searchBy] || searchBy || ''}`

  useEffect(() => {
    handleChange(searchBy as keyof T, validateSearchValue(debouncedSearchValue))
  }, [debouncedSearchValue, searchBy])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(validateSearchValue(value))
  }

  const validateSearchValue = (value: string): string => {
    if (currentSearchConfig?.type === 'numeric') {
      return DECIMAL_NUMERIC_REGEX.test(value) ? value : ''
    }
    return value
  }

  const handleChange = (field: keyof T, value: string | number) => {
    if (filters[field as keyof T] === value) {
      return
    }
    let updatedFilters = {
      ...filters,
      [field]: value,
    }

    const searchValuesKeys = searchByValues
      ?.map((v) => v.filterKey)
      .filter((v) => v !== searchBy)
    searchValuesKeys?.forEach((key) => {
      delete updatedFilters[key]
    })
    onSearch(updatedFilters)
  }

  const handleAuctionChange = (schedule: AuctionSchedule) => {
    handleChange('saleDate', schedule.saleCode)
    handleChange('year', schedule.year)
  }
  const handleBrokerChange = (brokerName: string | null) => {
    handleChange('broker', brokerName ?? '')
  }
  const handleFactoryChange = (factoryName: string | null) => {
    handleChange('factory', factoryName ?? '')
  }
  const handleDisbursementChange = (
    disbursementType: DisbursementType | null
  ) => {
    handleChange('disbursementType', disbursementType ?? '')
  }
  function handleYearChange(event: SelectChangeEvent) {
    setYear(event.target.value)
    //Reset Sale on year change
    handleChange('saleDate', '')
  }

  const handleStatusChange = (value: string | null) => {
    handleChange('status', value || '')
  }

  return (
    <Stack sx={{ backgroundColor: '#FFFFFF' }}>
      <Stack
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          width: '100%',
          flexDirection: 'row',
          alignItems: 'center',
          gap: '0.5rem',
          paddingInline: 3,
          paddingBlock: 1,
        }}
      >
        <Stack>
          {title && (
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: '#000A12',
              }}
            >
              {title}
            </Typography>
          )}

          {subtitle && (
            <Typography
              variant="subtitle2"
              sx={{
                color: '#000A12',
              }}
            >
              {subtitle}
            </Typography>
          )}
        </Stack>

        <Stack direction="row" spacing={2}>
          {showYear && (
            <Stack spacing={1}>
              <Typography
                variant="body2"
                sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
              >
                Viewing Year
              </Typography>

              <Select
                IconComponent={(iconProps) => (
                  <KeyboardArrowDownRounded
                    {...iconProps}
                    sx={{
                      color: '#667085',
                      marginleft: '0.5rem',
                      ...(iconProps?.sx || {}),
                    }}
                  />
                )}
                value={year}
                onChange={handleYearChange}
                sx={{
                  width: '100%',
                  '.MuiInputBase-input.MuiOutlinedInput-input ': {
                    py: '6px !important',
                  },
                  background: '#FFFFFF',
                  '& fieldset': {
                    border: `1px solid #D0D5DD !important`,
                  },
                  borderRadius: '8px',
                  color: '#667085',
                }}
              >
                {viewingYear.map((year) => (
                  <MenuItem key={year} value={year}>
                    {year}
                  </MenuItem>
                ))}
              </Select>
            </Stack>
          )}

          {showSaleDate && (
            <Stack spacing={1}>
              <Typography
                variant="body2"
                sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
              >
                Viewing Auction
              </Typography>
              {/*
            Todo: For post auction - display only done auctions and pre-populate the latest, Pre Auction- Display all and preselect the  current upcoming week sale

            */}
              <AuctionPicker
                year={year}
                value={filters.saleDate}
                onChange={handleAuctionChange}
              />
            </Stack>
          )}
        </Stack>
      </Stack>
      <Divider />
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          justifyContent: 'space-between',
          width: '100%',
        }}
        direction="row"
        spacing={2}
        paddingInline={3}
        paddingBlock={2}
        alignItems="flex-end"
      >
        {showSearchBox && (
          <Stack spacing={1} sx={{ width: '100%' }}>
            <Typography
              variant="body2"
              sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
            >
              Select Search Filter
            </Typography>
            <CustomFilterUserBox
              searchValue={searchValue}
              selectedSearchBy={searchBy}
              searchByValues={searchByValues.map(
                (item) => item.filterKey as string
              )}
              searchByLabel={searchByLabel}
              setSearchByValue={setSearchBy}
              onHandleSearch={handleSearch}
              searchPlaceHolder={searchPlaceHolder}
              prependSearchBy={true}
            />
          </Stack>
        )}
        {showBrokerFilter && (
          <BrokerSelector
            value={filters.broker}
            onChange={handleBrokerChange}
          />
        )}

        {showFactory && (
          <MarkSelector
            value={filters.factory}
            onChange={handleFactoryChange}
          />
        )}
        {showDisbursementFilter && (
          <DisbursementSelector
            value={filters.disbursementType}
            onChange={handleDisbursementChange}
          />
        )}
        {showStatus && (
          <StatusSelector
            value={filters.status}
            statuses={statuses}
            onChange={handleStatusChange}
          />
        )}

        {showExport && (
          //Hide for superadmin
          <AccessWrapper
            clientTypes={['Broker', 'Producer', 'Buyer']}
            backofficeAccess={false}
          >
            <ExportButton
              onClick={() => onExport(filters)}
              ButtonText={isExportingCatalogues ? 'Exporting...' : 'Export CSV'}
            />
          </AccessWrapper>
        )}
      </Stack>
    </Stack>
  )
}
