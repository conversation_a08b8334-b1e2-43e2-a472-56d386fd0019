import React, { useState } from 'react'
import {
  Autocomplete,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import * as Yup from 'yup'
import { Form, FormikProvider, useFormik } from 'formik'
import CloseIcon from '@mui/icons-material/Close'
import { FileUpload } from '@/components/FileUpload'
import { useAppDispatch, useAppSelector } from '@/store'
import { uploadSalesSchedule } from '@/store/actions/AuctionSchedule'
import { UploadIcon } from '@dtbx/ui/icons'

type UploadDialogProps = {
  years: string[]
  open: boolean
  handleClose: () => void
}

export const UploadWeeklyScheduleDialog = ({
  years,
  open,
  handleClose,
}: UploadDialogProps) => {
  const validationSchema = Yup.object({
    year: Yup.string().required('Year is required'),
    startDate: Yup.string().required('Start month is required'),
    endDate: Yup.string().required('End month is required'),
  })

  const dispatch = useAppDispatch()
  const { isLoadingSchedules } = useAppSelector((state) => state.salesSchedule)

  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [selectedYear, setSelectedYear] = useState<string>('')
  const [startMonth, setStartMonth] = useState<string>('')
  const [endMonth, setEndMonth] = useState<string>('')

  const formik = useFormik({
    initialValues: {
      year: '',
      startDate: '',
      endDate: '',
    },
    validationSchema,
    onSubmit: async () => {
      if (!selectedFile) return

      setUploadProgress(0)

      await uploadSalesSchedule(
        dispatch,
        selectedFile,
        selectedYear,
        startMonth,
        endMonth,
        {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              )
              setUploadProgress(progress)
            }
          },
        }
      )

      handleClose()
    },
  })

  const MONTH_OPTIONS = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ]

  return (
    <Dialog maxWidth="sm" open={open} onClose={handleClose}>
      <DialogTitle>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Typography variant="subtitle1" fontWeight="bold">
            Upload New Schedule of Weekly Sales.
          </Typography>
          <IconButton
            onClick={handleClose}
            sx={{
              height: '2rem',
              width: '2rem',
              border: '1px solid #D0D5DD',
              borderRadius: '0.5rem',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <DialogContentText sx={{ mb: 2 }}>
          Please ensure that you use the schedule template to ensure the dates
          are accurate.{' '}
          <a href="/eatta/templates/schedule.xlsx" download>
            Download Template
          </a>
        </DialogContentText>

        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack spacing={1} marginTop={2}>
              <Typography fontWeight="bolder">Select Year</Typography>
              <Autocomplete
                disablePortal
                size="small"
                options={years}
                value={selectedYear || null}
                onChange={(_, value) => {
                  setSelectedYear(value || '')
                  formik.setFieldValue('year', value ?? '')
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Select Year"
                    error={Boolean(formik.touched.year && formik.errors.year)}
                    helperText={formik.touched.year && formik.errors.year}
                  />
                )}
              />
            </Stack>

            <Stack direction="row" spacing={2} marginTop={3}>
              <Stack width="100%">
                <Typography fontWeight={600}>Starts on:</Typography>
                <Autocomplete
                  disablePortal
                  size="small"
                  options={MONTH_OPTIONS}
                  value={startMonth}
                  onChange={(_, value) => {
                    setStartMonth(value || '')
                    formik.setFieldValue('startDate', value || '')
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Select Month"
                      error={Boolean(
                        formik.touched.startDate && formik.errors.startDate
                      )}
                      helperText={
                        formik.touched.startDate && formik.errors.startDate
                      }
                    />
                  )}
                />
              </Stack>

              <Stack width="100%">
                <Typography fontWeight={600}>Ends on:</Typography>
                <Autocomplete
                  disablePortal
                  size="small"
                  options={MONTH_OPTIONS}
                  value={endMonth}
                  onChange={(_, value) => {
                    setEndMonth(value || '')
                    formik.setFieldValue('endDate', value || '')
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Select Month"
                      error={Boolean(
                        formik.touched.endDate && formik.errors.endDate
                      )}
                      helperText={
                        formik.touched.endDate && formik.errors.endDate
                      }
                    />
                  )}
                />
              </Stack>
            </Stack>

            <FileUpload
              progress={uploadProgress}
              disabled={isLoadingSchedules}
              onFileChange={(file) => setSelectedFile(file)}
            />

            <Stack direction="row" spacing={3} mt={4}>
              <Button
                fullWidth
                variant="contained"
                onClick={handleClose}
                sx={{ background: '#D92D20' }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                fullWidth
                variant="contained"
                startIcon={
                  isLoadingSchedules ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <UploadIcon />
                  )
                }
                disabled={isLoadingSchedules || !selectedFile}
                sx={{
                  bgcolor: selectedFile ? 'primary.main' : '#FFF',
                  border: '1px solid #E4E7EC',
                  color: selectedFile ? '#FFFFFF' : '#98A2B3',
                  fontWeight: 600,
                  '&:hover': {
                    bgcolor: 'primary.main',
                    color: '#FFFFFF',
                    '& svg': {
                      stroke: '#FFFFFF',
                    },
                  },
                }}
              >
                {isLoadingSchedules ? 'Uploading...' : 'Upload Schedule'}
              </Button>
            </Stack>
          </Form>
        </FormikProvider>
      </DialogContent>
    </Dialog>
  )
}
