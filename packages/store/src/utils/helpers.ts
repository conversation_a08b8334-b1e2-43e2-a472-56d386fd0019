import { ReadonlyURLSearchParams } from 'next/navigation'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'

dayjs.extend(customParseFormat)

export interface IApprovalRequest {
  checker?: string
  checkerComments?: string
  id: string
  maker: string
  dateCreated: string
  dateModified: string
  makerCheckerType: {
    channel: string
    checkerPermissions: string[]
    description?: string
    makerPermissions: string[]
    module: string
    name: string
    overridePermissions: string[]
    type: string
  }
  entityId?: string
  entity?: string
  diff: IDiffValues[]
  makerComments?: string
  status: string
}

export interface IDiffValues {
  field: string
  name?: string
  oldValue: IDiffValues[] | string
  newValue: IDiffValues[] | string
}

/**
 * Gets the initials of a full name eg Jane <PERSON> => JD
 * @param name
 */
// export const getInitials = (name = '') =>
//   name
//     .replace(/\s+/, ' ')
//     .split(' ')
//     .slice(0, 2)
//     .map((v) => v && v[0]?.toUpperCase())
//     .join('')

export const getInitials = (name = '') =>
  name
    .replace(/\s+/, ' ')
    .split(' ')
    .slice(0, 2)
    .map((v) => v && v[0].toUpperCase())
    .join('')

export const rightsFormatter = (str: string) => {
  str = str.replace(/([A-Z]+)/g, '_$1').toLowerCase()
  str = str.startsWith('_') ? str.slice(1) : str
  return str
}
// type Mark = {
//   value: number
//   label: string
// }

export function getTimeOfDay() {
  const date = new Date()
  const currentHour = date.getHours()
  let timeOfDay
  if (currentHour < 12) {
    timeOfDay = 'morning'
  } else if (currentHour < 18) {
    timeOfDay = 'afternoon'
  } else {
    timeOfDay = 'evening'
  }

  const hours = date.getHours() % 12 || 12 // Convert "0" to "12"
  const minutes = date.getMinutes()
  const amPm = date.getHours() >= 12 ? 'PM' : 'AM'
  const formattedTime = `${hours}:${minutes < 10 ? '0' : ''}${minutes} ${amPm}`

  return { timeOfDay, formattedTime }
}

export function formatCurrency(
  value: string | number | undefined,
  currency: string = 'KES',
  locale: string = 'en-KE'
) {
  const numberValue = typeof value === 'string' ? parseFloat(value) : value
  if (!numberValue && numberValue !== 0) return `${currency} 0.00`
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
  }).format(numberValue)
}

export function formatTimestamp(timestamp: string) {
  if (!timestamp) {
    return 'N/A'
  }

  const date = new Date(timestamp)
  if (isNaN(date.getTime())) {
    return 'N/A'
  }
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

export const formatTimeOnly = (dateString: string): string => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    })
  } catch {
    return ''
  }
}

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const options = {
    month: 'long' as const,
    day: 'numeric' as const,
    year: 'numeric' as const,
  }
  return date.toLocaleDateString('en-US', options)
}

/**
 * Calculates the week number of the year for a given date.
 *
 * @param {Date} [date=new Date()] - The date for which to calculate the week number. Defaults to the current date if no date is provided.
 * @return {number} The week number of the year corresponding to the given date.
 */
export function getWeekOfYear(date = new Date()) {
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1)
  const pastDaysOfYear =
    (date.getTime() - firstDayOfYear.getTime()) / (1000 * 60 * 60 * 24) + 1
  const firstDayOfWeek =
    firstDayOfYear.getDay() === 0 ? 7 : firstDayOfYear.getDay()
  return Math.ceil((pastDaysOfYear + firstDayOfWeek - 1) / 7)
}
export function getAuctionWeek() {
  return getWeekOfYear() - 1
}

/**
 *
 * @param date
 * @param inputFormat Date format
 * @param outputFormat Output format
 */

export const formatCustomDate = (
  date: string,
  inputFormat: string = 'YYYY-MM-DD',
  outputFormat: string = 'MMMM D, YYYY'
): string => {
  return dayjs(date, inputFormat).format(outputFormat)
}

export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('en-KE', { hour12: true })
}

export const isObjEmpty = (obj: object): boolean => {
  return Object.keys(obj).length === 0
}
export interface FormValues {
  [key: string]: string | number | boolean | FormValues | FormValues[]
}

export const trimSpace = <T extends FormValues>(obj: T): T => {
  const trimmedObj: FormValues = {}
  for (const key in obj) {
    if (typeof obj[key] === 'string') {
      trimmedObj[key] = (obj[key] as string).trim()
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      if (Array.isArray(obj[key])) {
        trimmedObj[key] = (obj[key] as FormValues[]).map(trimSpace)
      } else {
        trimmedObj[key] = trimSpace(obj[key] as FormValues)
      }
    } else {
      trimmedObj[key] = obj[key]
    }
  }
  return trimmedObj as T
}

export const generateMarks = (step: number, start: number, end: number) => {
  const marks = []
  for (let value = start; value <= end; value += step) {
    marks.push({
      value: value,
      label: `${value}`,
    })
  }
  return marks
}

export const downloadBlob = (blob: Blob, filename: string) => {
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(link.href)
}

export const handleDiff = (diff: IDiffValues[] = []) => {
  return diff
    .map((change, index) => {
      if (Array.isArray(change.newValue)) {
        let newValueString = ''
        let oldValueString = change.oldValue || 'null'

        if (change.field === 'permissions') {
          newValueString = change.newValue
            .map((val) => `${val.name}`)
            .join(', ')

          // *** CHANGED: Added a check to ensure oldValue is an array before mapping over it ***
          if (Array.isArray(change.oldValue)) {
            oldValueString = change.oldValue
              .map((val) => `${val.name}`)
              .join(', ')
          } else {
            oldValueString = change.oldValue || 'null'
          }
        } else {
          newValueString = change.newValue
            .map((val) => `${val.field}: ${val.newValue}`)
            .join(', ')
        }

        return `${index + 1}. ${change.field} was changed from ${oldValueString} to ${newValueString}`
      } else {
        // *** CHANGED: Added fallback to handle non-array values safely ***
        return `${index + 1}. ${change.field} was changed from ${change.oldValue || 'null'} to ${change.newValue || 'null'}`
      }
    })
    .join('\n')
}

/** This is used to extract info from approval requests**/

export const extractFields = (field: string, row: IApprovalRequest) => {
  const result = row.diff.find((val) => val.field === field)
  return result?.newValue
}

/** get base64 from a file **/
export function getBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject(error)
  })
}

export const isAccountLinkingApprovalRequest = (entity: string) => {
  const accountLinkingEntities = ['accounts']
  const parsedEntity = JSON.parse(entity)
  return Object.keys(parsedEntity).some((key) =>
    accountLinkingEntities.includes(key)
  )
}
/*
 * This function accepts the current path, pathname and search-params to accommodate lms product paths and matches it with existing paths to determine the active path and returns a boolean
 * */
type MatchPathOptions = {
  path: string
  end: boolean
}
export const matchActivePath = (
  path: string,
  pathname: string,
  searchParams: string | ReadonlyURLSearchParams
) => {
  const url = `${pathname}?${searchParams}`
  const matchPath = (options: MatchPathOptions, pathname: string) => {
    const { path, end } = options
    const regex = new RegExp(`^${path}${end ? '$' : ''}`, 'i')
    return regex.test(pathname)
  }
  return path && searchParams.toString().length > 0
    ? path === url
    : path && searchParams.toString().length === 0
      ? matchPath({ path, end: false }, pathname)
      : false
}

export const isNavMatch = (
  configPath: string,       
  pathname: string,           
  searchParams: URLSearchParams
) => {
  const currentFullPath = `${pathname}?${searchParams.toString()}`
  return currentFullPath.startsWith(configPath)
}
export const formatText = (text: string): string => {
  return text
    .replace(/_/g, ' ')
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .toUpperCase()
}

export const addUnderscores = (text: string): string => {
  return text.trim().replace(/\s+/g, '_')
}

export const formatCamelCaseToWords = (text: string): string => {
  return text
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .replace(/^./, (str) => str.toUpperCase())
}
