import {
  <PERSON>,
  Button,
  Circular<PERSON><PERSON>ress,
  Di<PERSON>r,
  <PERSON>,
  <PERSON>ack,
  Typography,
} from '@mui/material'
import React, { FC, useMemo, useState } from 'react'
import Grid from '@mui/material/Grid2'
import { DownloadPng } from '@/components/SvgIcons/DownloadPng'
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight'
import MakerCheckerActivities from '../../../components/MakerCheckerActivities'
import { useAppDispatch, useAppSelector } from '@/store'
import { Company, MakeRequest } from '@/store/interfaces'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { useRouter } from 'next/navigation'
import {
  approveCompany,
  createCompany,
  downloadOnboardingDocuments,
} from '@/store/actions'
import { CreateCompanySteps } from '@/features/companies/create/CreateCompanyPage'
import { CommentDialog } from '@/components/CommentDialog'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { getRoutePath } from '@/utils/routePath'
import { safeJsonParse } from '@/utils/objectUtil'
import { CreateUserSteps } from '@/features/companies/users/create/CreateUserPage'
import { ApproveMemberSteps } from '@/features/members/approve/ApproveMemberCreation'
import { ApproveCompanySteps } from '@/features/companies/approve/ApproveCompanyPage'

interface DetailsVerificationProps {
  setStep: (step: number) => void
  actionType: 'SUBMISSION' | 'CHECKER'
}

const DetailsVerification: FC<DetailsVerificationProps> = ({
  setStep,
  actionType = 'SUBMISSION',
}) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const { onboardingCompany, isLoading } = useAppSelector(
    (state) => state.companies
  )
  const company: Company | null = useMemo(
    () => safeJsonParse(onboardingCompany?.entity),
    [onboardingCompany]
  )
  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_ORGANIZATION),
    []
  )

  const isConfirmAction = actionType === 'SUBMISSION'
  const [openCommentDialog, setOpenCommentsDialog] = useState(false)

  const handleNextStep = async () => {
    if (!onboardingCompany) return
    //confirmation
    if (isConfirmAction) {
      const payload: MakeRequest = {
        stepName: actionType,
        approvalId: onboardingCompany?.id,
      }
      await createCompany(isSuper, dispatch, payload, handleConfirmSuccess)
      return
    }
    //Approval
    setOpenCommentsDialog(true)
  }

  const handleConfirmSuccess = () => {
    if (isSuper) {
      setStep(CreateCompanySteps.COMPLETE)
      return
    }

    router.replace('/backoffice/companies?tab=2')
  }

  const handleOnSubmitCommentsDialog = async (comment: string) => {
    setOpenCommentsDialog(false)
    if (!onboardingCompany) return
    await approveCompany(dispatch, onboardingCompany.id, comment, () =>
      setStep(
        isConfirmAction
          ? CreateCompanySteps.COMPLETE
          : ApproveCompanySteps.COMPLETE
      )
    )
  }

  const handleDownload = async (url: string) => {
    await downloadOnboardingDocuments(dispatch, url, true)
  }

  return (
    <Stack spacing={3}>
      <Stack>
        <Stack
          spacing={3}
          sx={{
            backgroundColor: 'primary.main',
            padding: 3,
            borderRadius: '0.5rem 0.5rem 0  0',
          }}
        >
          <Stack
            direction="row"
            alignItems="center"
            spacing={2}
            flexWrap="wrap"
          >
            <Typography
              variant="subtitle1"
              fontWeight={600}
              sx={{ color: '#FFFFFF' }}
            >
              {company?.name}
            </Typography>
            <Box
              sx={{
                backgroundColor: '#F0F0F0',
                color: '#000000',
                px: 1.5,
                borderRadius: '999px',
                fontSize: '0.75rem',
                fontWeight: 500,
              }}
            >
              {company?.type}
            </Box>
          </Stack>

          <Stack
            spacing={2}
            direction="row"
            useFlexGap
            sx={{ justifyContent: 'space-between', flexWrap: 'wrap' }}
          >
            <Stack sx={{ minWidth: '200px' }}>
              <Typography
                variant={'subtitle3'}
                sx={{ color: '#FFFFFF' }}
                fontWeight="700"
              >
                EATTA Membership Code
              </Typography>
              <Typography sx={{ color: '#FFFFFF' }} fontWeight="500">
                {company?.code ?? 'N/A'}
              </Typography>
            </Stack>

            <Box sx={{ minWidth: '200px' }}>
              <Typography
                variant={'subtitle3'}
                sx={{ color: '#FFFFFF' }}
                fontWeight="700"
              >
                Phone Number
              </Typography>
              <Typography sx={{ color: '#FFFFFF' }} fontWeight="500">
                {company?.phoneNumber ?? 'N/A'}
              </Typography>
            </Box>

            <Box sx={{ minWidth: '200px' }}>
              <Typography
                variant={'subtitle3'}
                sx={{ color: '#FFFFFF' }}
                fontWeight="700"
              >
                Email Address
              </Typography>
              <Typography sx={{ color: '#FFFFFF' }} fontWeight="500">
                {company?.emailAddress ?? 'N/A'}
              </Typography>
            </Box>
          </Stack>
        </Stack>

        <Stack
          spacing={3}
          sx={{
            padding: 3,
            backgroundColor: '#FFFFFF',
            borderRadius: '0 0 0.5rem 0.5rem',
          }}
        >
          <Box>
            <Typography variant="subtitle1" fontWeight={500}>
              Settlement Account Details.
            </Typography>

            <Grid
              container
              spacing={4}
              sx={{
                fontWeight: 'bold',
                paddingBlock: '0.8rem',
                backgroundColor: '#FFFFFF',
              }}
            >
              <Grid size={{ xs: 12, sm: 3, md: 6 }}>
                <Typography variant={'subtitle3'} fontWeight={600}>
                  {company?.bankName}
                </Typography>
                <Typography fontWeight={400}>
                  Acc. No. {company?.bankAccountNumber} ({company?.currency})
                </Typography>
              </Grid>

              <Grid size={{ xs: 12, sm: 3, md: 6 }}>
                <Typography variant={'subtitle3'} fontWeight={600}>
                  Branch
                </Typography>
                <Typography fontWeight={400}>
                  {company?.bankBranchName ?? 'N/A'}
                </Typography>
              </Grid>

              <Grid size={{ xs: 12, sm: 3, md: 6 }}>
                <Typography variant={'subtitle3'} fontWeight={600}>
                  Settlement Type
                </Typography>
                <Typography fontWeight={400}>
                  {company?.channel ?? 'N/A'}
                </Typography>
              </Grid>

              <Grid size={{ xs: 12, sm: 3, md: 6 }}>
                <Typography variant={'subtitle3'} fontWeight={600}>
                  Swift Code
                </Typography>
                <Typography fontWeight={400}>{company?.swiftCode}</Typography>
              </Grid>
            </Grid>
          </Box>

          {company?.certificateUrl && (
            <>
              <Divider />

              <Stack spacing={3}>
                <Typography variant="subtitle1" fontWeight={500}>
                  Company Registration
                </Typography>

                <Stack
                  direction="row"
                  justifyContent="space-between"
                  alignItems="flex-start"
                  flexWrap="wrap"
                  width="100%"
                  mb={3}
                >
                  <Box display="flex" alignItems="flex-start" gap={1}>
                    <DownloadPng />
                    <Box>
                      <Typography fontWeight={600} mb={0.5}>
                        EATTA Registration Document
                      </Typography>
                      <Button
                        variant="text"
                        onClick={() => handleDownload(company.certificateUrl!)}
                        sx={{
                          padding: '0px',
                          color: '#1976d2 !important',
                          textDecoration: 'underline',
                          textDecorationColor: '#1976d2',
                        }}
                      >
                        Download
                      </Button>
                    </Box>
                  </Box>
                </Stack>
              </Stack>
            </>
          )}
        </Stack>
      </Stack>

      {!isConfirmAction && (
        <MakerCheckerActivities
          activities={[
            {
              action: 'Company profile created ',
              actionedBy: onboardingCompany?.maker ?? '',
              actionedDate: onboardingCompany?.dateCreated ?? '',
            },
          ]}
        />
      )}

      <AccessControlWrapper
        rights={
          isConfirmAction
            ? ACCESS_CONTROLS.CREATE_ORGANIZATION
            : ACCESS_CONTROLS.ACCEPT_CREATE_ORGANIZATION
        }
        makerId={onboardingCompany?.maker}
        isMake={isConfirmAction}
      >
        <Button
          variant="contained"
          color="primary"
          onClick={handleNextStep}
          disabled={isLoading}
          endIcon={
            isLoading ? (
              <CircularProgress size={20} thickness={3.0} />
            ) : (
              <KeyboardArrowRightIcon />
            )
          }
        >
          {isConfirmAction ? 'Submit Company Profile' : 'Approve'}
        </Button>
      </AccessControlWrapper>

      {openCommentDialog && (
        <CommentDialog
          open={openCommentDialog}
          onClose={() => setOpenCommentsDialog(false)}
          onSubmit={handleOnSubmitCommentsDialog}
        />
      )}
    </Stack>
  )
}

export default DetailsVerification
