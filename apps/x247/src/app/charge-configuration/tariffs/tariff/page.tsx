'use client'
import React, { useState, useEffect } from 'react'
import { TariffTypeStatus } from './pageHeader'
import {
  Box,
  Button,
  Paper,
  Stack,
  Typography,
  Breadcrumbs,
} from '@mui/material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { SearchRounded } from '@mui/icons-material'
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined'
import AddIcon from '@mui/icons-material/Add'
import { useCustomRouter } from '@dtbx/ui/hooks'
import TarrifConfigurationsPage from './index'
import { CreateConfig } from './CreateConfigDrawer'
import { Status } from '@/store/interfaces'
import { CustomerInfoChip, CustomerStatusChip } from '@dtbx/ui/components/Chip'
import { RootState, useAppSelector } from '@/store'
import TariffStatusModal from '../../TariffsMoreMenu'
import ChangesLogDrawer from './ChangesLogDrawer'
import ApprovalRequestDrawer from './ApprovalRequestDrawer'
import {
  getTariffConfigByTariffCurrency,
  getConfigurableServices,
} from '@/store/actions'
import { useAppDispatch } from '@dtbx/store'
import { ACCESS_CONTROLS, AccessControlWrapper } from '@dtbx/store/utils'

const renderStatusChip = (status: Status | undefined) => {
  switch (status) {
    case 'ACTIVE':
      return <CustomerStatusChip label="ACTIVE" />
    case 'INACTIVE':
      return <CustomerStatusChip label="INACTIVE" />
    case 'PENDING':
      return <CustomerStatusChip label="PENDING" />
    default:
      return <CustomerStatusChip label="ACTIVE" />
  }
}
const getValidTariffStatus = (status: string | undefined): Status => {
  switch (status) {
    case 'ACTIVE':
    case 'INACTIVE':
    case 'PENDING':
      return status
    default:
      return 'ACTIVE'
  }
}
function TarrifDetailsPage() {
  const router = useCustomRouter()
  const [open, setOpen] = useState<boolean>(false)
  const [currency, setCurrency] = useState<string>('KES')
  const [type, setType] = useState<string>('')
  const [serviceType, setServiceType] = useState<string>('PAYMENT')
  const [drawerOpen, setDrawerOpen] = useState(false)

  const handleOpenDrawer = () => {
    setDrawerOpen(true)
  }

  const handleCloseDrawer = () => {
    setDrawerOpen(false)
  }

  const { selectedTariff } = useAppSelector(
    (state: RootState) => state.chargeConfiguration
  )
  const dispatch = useAppDispatch()

  const getSelectedCurrency = (currency: string) => {
    setCurrency(currency)
    if (!selectedTariff?.name) return
    getTariffConfigByTariffCurrency(
      { tariff: selectedTariff?.name, currency },
      dispatch
    )
  }

  useEffect(() => {
    if (selectedTariff?.name) {
      getTariffConfigByTariffCurrency(
        {
          tariff: selectedTariff.name,
          currency: currency,
          chargeType: type,
          serviceType,
        },
        dispatch
      )
      getConfigurableServices(selectedTariff.name, currency, dispatch)
    }
  }, [type])

  return (
    <>
      <Stack
        sx={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: '10px',
          marginBottom: '0.2%',
          borderBottom: '1px solid #EAECF0',
          paddingLeft: '3.5%',
          paddingRight: '4.5%',
          paddingBottom: '1%',
        }}
      >
        <Stack direction="row" alignItems="center" gap="10px">
          <Breadcrumbs>
            <Typography
              variant="body2"
              sx={{ color: '#555C61', cursor: 'pointer', fontSize: '0.875rem' }}
              onClick={() => router.push('/charge-configuration')}
            >
              Tariffs
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: 'primary.main', fontSize: '0.875rem' }}
            >
              {selectedTariff?.name}
            </Typography>
          </Breadcrumbs>
          {renderStatusChip(selectedTariff?.status as Status)}
          <CustomerInfoChip label={`Pending approval :`} requests={[]} />
        </Stack>
        <Stack direction="row" alignItems="center" gap={2.5}>
          <ApprovalRequestDrawer
            drawerOpen={drawerOpen}
            onClose={handleCloseDrawer}
            title="Approval Rsequest"
            description="Review and approve changes."
          />
          <Button
            // onClick={handleOpen}
            onClick={handleOpenDrawer}
            variant="outlined"
            sx={{
              border: '1px solid #D0D5DD',
              borderRadius: '4px',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              height: '40px',
              color: '#555C61',
              fontSize: '15px',
              fontWeight: '500',
              textWrap: 'noWrap',
            }}
          >
            Review approval requests
            <Typography
              sx={{
                border: '1px solid #EAECF0',
                background: '#F9FAFB',
                width: '25px',
                height: '25px',
                borderRadius: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginLeft: '8px',
              }}
            >
              1
            </Typography>
          </Button>

          <ChangesLogDrawer />

          <TariffStatusModal
            name={selectedTariff?.name || ''}
            status={getValidTariffStatus(selectedTariff?.status)}
            label={
              getValidTariffStatus(selectedTariff?.status) === 'ACTIVE'
                ? 'Deactivate'
                : 'Activate'
            }
          />
        </Stack>
      </Stack>

      <Paper
        elevation={0}
        sx={{
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          borderRadius: '8px',
          border: '1px solid #EAECF0',
          background: '#FFFFFF',
          margin: '1% 2% 0 2%',
        }}
      >
        <Stack
          direction="row"
          sx={{
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '1.75em 2.25em',
          }}
        >
          <Button
            variant="outlined"
            sx={{
              width: '12%',
              px: 0,
              border: '1px solid #AAADB0',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              marginLeft: '1%',
            }}
            startIcon={<ArrowBackIcon />}
            onClick={() => window.history.back()}
          >
            All tariffs
          </Button>
        </Stack>

        <TariffTypeStatus
          Tariffname={selectedTariff?.name || ''}
          TariffStatus={getValidTariffStatus(selectedTariff?.status as Status)}
          getSelectedCurrency={getSelectedCurrency}
        />

        <Stack sx={{ flexDirection: 'column', gap: 3, padding: '2%' }}>
          <Stack
            sx={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 3,
            }}
          >
            <Stack
              sx={{
                flexDirection: 'row',
                gap: 3,
                alignItems: 'center',
                marginLeft: '2%',
              }}
            >
              <CustomSearchInput
                placeholder="Search among all configurations"
                endAdornment={
                  <Box
                    sx={{
                      backgroundColor: '#EDEEEE',
                      padding: '8px 12px',
                      width: '38px',
                      height: '100%',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      position: 'absolute',
                      right: 0,
                      top: 0,
                      borderTopRightRadius: '4px',
                      borderBottomRightRadius: '4px',
                    }}
                  >
                    <SearchRounded
                      sx={{
                        color: '#555C61',
                      }}
                    />
                  </Box>
                }
                sx={{
                  width: '45vw',
                  background: '#FFFFFF',
                  borderRadius: '4px',
                  '& fieldset': {
                    border: '1px solid #D0D5DD !important',
                  },
                }}
              />
              <Button
                variant="outlined"
                sx={{ border: '1px solid #D0D5DD' }}
                startIcon={<FilterListOutlinedIcon />}
              >
                Filter
              </Button>
            </Stack>
            <AccessControlWrapper
              rights={[...ACCESS_CONTROLS.CREATE_SERVICE_CONFIGS]}
            >
              <Button
                onClick={() => setOpen(true)}
                startIcon={<AddIcon sx={{ color: 'white' }} />}
                variant="contained"
                sx={{
                  display: 'flex',
                  gap: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  px: 3,
                  py: 2,
                  textTransform: 'none',
                  backgroundColor: '#101828',
                  borderRadius: '8px',
                  marginRight: '1%',
                }}
              >
                <Typography
                  variant="body3"
                  sx={{
                    textWrap: 'nowrap',
                    color: 'white',
                    fontWeight: '700',
                    fontSize: '14px',
                  }}
                >
                  Add new configuration
                </Typography>
              </Button>
            </AccessControlWrapper>
          </Stack>
        </Stack>
        <TarrifConfigurationsPage
          setType={setType}
          setServiceType={setServiceType}
        />
      </Paper>
      <CreateConfig open={open} setOpen={setOpen} currency={currency} />
    </>
  )
}

export default TarrifDetailsPage
