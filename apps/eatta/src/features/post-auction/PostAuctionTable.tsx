/**
 * <AUTHOR> on 29/04/2025
 */
import React, { FC } from 'react'
import {
  BrokerInvoiceStatus,
  Catalogue,
  InvoiceEntryStatus,
} from '@/store/interfaces'
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import { CustomTableHeader } from '@dtbx/ui/components/Table'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { useAppSelector } from '@dtbx/store'
import {
  BROKER_INVOICE_STATUS_COLOR,
  INVOICE_ENTRY_STATUS_COLOR,
  INVOICE_ENTRY_STATUS_MAP,
} from '@/utils/statusChips'
import { formatDate } from '@dtbx/store/utils'

interface PostAuctionTableProps {
  catalogues: Catalogue[]
}

export const PostAuctionTable: FC<PostAuctionTableProps> = ({ catalogues }) => {
  const { decodedToken } = useAppSelector((state) => state.auth)
  const isProducer = decodedToken.clientType === 'Producer'

  const POST_AUCTION_HEADERS = [
    { id: 'lotNo', label: 'Lot', alignRight: false },
    { id: 'gardenInvoice', label: 'Garden Invoice', alignRight: false },
    { id: 'producer', label: 'Producer', alignRight: false },
    { id: 'broker', label: 'Broker', alignRight: false },
    { id: 'buyer', label: 'Buyer', alignRight: false },
    { id: 'buyerName', label: 'Buyer Name', alignRight: false },
    { id: 'wareHouse', label: 'Warehouse', alignRight: false },
    { id: 'grade', label: 'Grade', alignRight: false },
    { id: 'pkgs', label: 'Pkgs', alignRight: false },
    { id: 'unitWeight', label: 'Unit weight', alignRight: false },
    { id: 'netWeight', label: 'Sold Weight (Kgs)', alignRight: false },
    {
      id: 'purchasePrice',
      label: 'Purchase price ($)',
      alignRight: false,
    },
    { id: 'totalValue', label: 'Total Value ($)', alignRight: false },
    {
      id: 'brokerCommissionFromProducer',
      label: 'Broker (0.75%)',
      alignRight: false,
    },
    {
      id: 'brokerProducerWithholdingTax',
      label: 'Broker Comm WHT',
      alignRight: false,
    },
    {
      id: 'warehouseCharges',
      label: 'Warehouse Charge (4.1%)',
      alignRight: false,
    },
    { id: 'penalties', label: 'Late Payment Charge', alignRight: false },
    { id: 'datePaid', label: 'Date Paid', alignRight: false },
    {
      id: isProducer ? 'invoiceEntryStatus' : 'invoiceStatus',
      label: 'Status',
      alignRight: false,
    },
  ]
  return (
    <TableContainer
      component={Paper}
      sx={{
        boxShadow: 'none',
        '& .MuiTableCell-root': {
          paddingInline: '1.5rem',
          paddingBlock: '0.5rem',
          textAlign: 'left',
        },
      }}
    >
      <Table
        sx={{ minWidth: 650 }}
        aria-label="post auction table"
        size="small"
      >
        <CustomTableHeader
          order={'asc'}
          orderBy={''}
          headLabel={
            isProducer
              ? POST_AUCTION_HEADERS.filter(
                  (header) =>
                    header.id !== 'producer' &&
                    header.id !== 'wareHouse' &&
                    header.id !== 'status'
                )
              : POST_AUCTION_HEADERS.filter(
                  (header) =>
                    header.id !== 'brokerCommissionFromProducer' &&
                    header.id !== 'brokerProducerWithholdingTax' &&
                    header.id !== 'warehouseCharges' &&
                    header.id !== 'penalties'
                )
          }
          showCheckbox={false}
          rowCount={catalogues.length}
          numSelected={0}
          onRequestSort={() => {}}
        />
        <TableBody>
          {catalogues.map((row) => {
            return (
              <TableRow
                hover
                key={row.id}
                tabIndex={-1}
                role="checkbox"
                aria-checked={false}
              >
                <TableCell>{row.lotNo}</TableCell>
                <TableCell>
                  <Typography
                    variant="body1"
                    sx={{
                      fontWeight: 400,
                      color: '#101828',
                      fontSize: '0.875rem',
                    }}
                  >
                    {row.invoiceNo}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: 400,
                      color: '#475467',
                      fontSize: '0.875rem',
                    }}
                  >
                    {row.factory}
                  </Typography>
                </TableCell>
                {!isProducer && <TableCell>{row.producer}</TableCell>}
                <TableCell>{row.broker}</TableCell>
                <TableCell>{row.buyer}</TableCell>
                <TableCell>{row.buyerName || '_'}</TableCell>
                {!isProducer && <TableCell>{row.wareHouse}</TableCell>}
                <TableCell>{row.grade}</TableCell>
                <TableCell>{row.pkgs}</TableCell>
                <TableCell>{row.kgs}</TableCell>
                <TableCell>
                  {isProducer ? row.totalWeight : row.netWeight}
                </TableCell>
                <TableCell>
                  {isProducer ? row.pricePerKg : row.salePrice}
                </TableCell>
                <TableCell>{row.totalValue}</TableCell>
                {isProducer && (
                  <>
                    <TableCell>{row.brokerCommissionFromProducer}</TableCell>
                    <TableCell>{row.brokerProducerWithholdingTax}</TableCell>
                    <TableCell>{row.warehouseCharges}</TableCell>
                    <TableCell>{row.penalties}</TableCell>
                    <TableCell>
                      {row.datePaid ? formatDate(row.datePaid) : '-'}
                    </TableCell>
                  </>
                )}
                {!isProducer && (
                  <>
                    <TableCell>
                      {row.datePaid ? formatDate(row.datePaid) : '-'}
                    </TableCell>
                  </>
                )}
                <TableCell>
                  <StatusChip
                    label={
                      isProducer
                        ? INVOICE_ENTRY_STATUS_MAP(
                            row.invoiceEntryStatus as InvoiceEntryStatus,
                            row.lateDays
                          )
                        : row.invoiceStatus
                    }
                    status={
                      isProducer
                        ? INVOICE_ENTRY_STATUS_COLOR[
                            row.invoiceEntryStatus as InvoiceEntryStatus
                          ]
                        : BROKER_INVOICE_STATUS_COLOR[
                            row.invoiceStatus as BrokerInvoiceStatus
                          ]
                    }
                  />
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </TableContainer>
  )
}
