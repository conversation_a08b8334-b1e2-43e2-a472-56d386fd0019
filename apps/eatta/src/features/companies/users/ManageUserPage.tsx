'use client'
import { Stack } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { OrganizationStatus, UserFilters } from '@/store/interfaces'
import { TabPanel } from '@dtbx/ui/components/Tabs'
import { UserHeader } from '@/features/companies/users/UserHeader'
import UsersTable from '@/features/companies/users/UsersTable'
import { getOrganizationById } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import UserApprovalsTable from '@/features/companies/users/UserApprovalsTable'
import { CustomTabs, TabType } from '@/components/CustomTabs'

const TABS: TabType<OrganizationStatus>[] = [
  {
    title: 'Members',
    canSelect: true,
    status: 'ACTIVE',
  },
  {
    title: 'Registration Ongoing',
    canSelect: true,
    status: 'STAGING',
  },
  {
    title: 'Awaiting Approval',
    canSelect: false,
    status: 'PENDING',
  },
]

const initialFilters = {
  page: 1,
  size: 10,
  name: '',
  email: '',
  phoneNumber: '',
  username: '',
  status: '',
}

const ManageUserPage = ({ organizationId }: { organizationId: string }) => {
  const dispatch = useAppDispatch()
  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState<TabType<OrganizationStatus>[]>(TABS)
  const [selectedTab, setSelectedTab] = useState(initialTab)

  const [filters, setFilters] = useState<UserFilters>(initialFilters)

  const { isLoading } = useAppSelector((state) => state.companies)

  const handleTabSelected = (index: number) => {
    setSelectedTab(index)
  }

  const handleChange = (field: keyof UserFilters, value: string) => {
    setFilters({
      ...initialFilters,
      [field]: value,
    })
  }

  const handleCountChange = (
    count: number,
    tab: TabType<OrganizationStatus>
  ) => {
    setTabs((prevTabs) =>
      prevTabs.map((t) =>
        t.title === tab.title ? { ...t, itemCounts: count } : t
      )
    )
  }

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    replace(`${pathname}?${params.toString()}`)
  }

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab])

  useEffect(() => {
    const getOrganization = async () => {
      await getOrganizationById(dispatch, organizationId)
    }
    getOrganization()
  }, [organizationId])

  return (
    <Stack sx={{ height: '100%' }}>
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
        }}
      >
        <UserHeader filters={filters} onSearchChange={handleChange} />

        <CustomTabs
          tabs={tabs}
          selectedTab={selectedTab}
          onTabSelected={handleTabSelected}
          isLoading={isLoading}
        />
      </Stack>

      <Stack sx={{ overflow: 'auto' }}>
        {tabs.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            {tab.title === 'Members' ? (
              <UsersTable
                filters={filters}
                onMembersCountChange={(count) => handleCountChange(count, tab)}
              />
            ) : (
              <UserApprovalsTable
                status={tab.status}
                filters={filters}
                onApprovalsCountChange={(count) =>
                  handleCountChange(count, tab)
                }
              />
            )}
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}

export default ManageUserPage
