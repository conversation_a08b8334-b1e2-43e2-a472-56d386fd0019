/**
 * <AUTHOR> on 17/10/2024
 */
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  FormControl,
  FormHelperText,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import {
  Company,
  COMPANY_TYPES,
  CompanyProfileRequest,
  CompanyType,
  FactoryProfileRequest,
} from '@/store/interfaces'
import { useAppDispatch } from '@dtbx/store'
import { useAppSelector } from '@/store'
import { matchIsValidTel, MuiTelInput } from 'mui-tel-input'
import React, { FC, useEffect, useMemo, useState } from 'react'
import { ALPHA_NUMERIC_REGEX, ORG_NAME_REGEX } from '@/utils/validators'
import { createCompany, uploadOnboardingDocuments } from '@/store/actions'
import { FileUpload } from '@/components/FileUpload'
import { CreateCompanySteps } from '@/features/companies/create/CreateCompanyPage'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'

const validationSchema = Yup.object({
  type: Yup.string<CompanyType>().required('Company type should not be empty'),
  code: Yup.string()
    .required('Membership code not be empty')
    .matches(ALPHA_NUMERIC_REGEX, 'Only alphanumeric characters are allowed')
    .min(3, 'Membership code must be at least 3 characters'),
  name: Yup.string()
    .required('Company name should not be empty')
    .matches(
      ORG_NAME_REGEX,
      'Only letters, numbers, spaces, and brackets are allowed. No leading or trailing spaces.'
    )
    .test(
      'alpha',
      'Name must contain at least three letter.',
      (value) => (value.match(/[a-zA-Z]/g) || []).length >= 3
    ),
  phoneNumber: Yup.string()
    .required('Phone number should not be empty')
    .test('isValidPhone', 'Phone number is invalid', (value) =>
      matchIsValidTel(value)
    ),
  emailAddress: Yup.string()
    .required('Email should not be empty')
    .email('Enter valid email'),
  logo: Yup.mixed().nullable(),
  file: Yup.mixed().required('File is required'),
})

const factoryValidationSchema = Yup.object({
  name: Yup.string()
    .required('Company name should not be empty')
    .matches(
      ORG_NAME_REGEX,
      'Only letters, numbers, spaces, and brackets are allowed. No leading or traili,ng spaces.'
    )
    .test(
      'alpha',
      'Name must contain at least three letter.',
      (value) => (value.match(/[a-zA-Z]/g) || []).length >= 3
    ),
  phoneNumber: Yup.string().test(
    'isValidPhone',
    'Phone number is invalid',
    (value) => {
      if (!value) return true
      return matchIsValidTel(value)
    }
  ),
  emailAddress: Yup.string().email('Enter valid email'),
})

type ProfileProps = {
  isFactory: boolean
  setStep: (step: CreateCompanySteps) => void
}

const Profile: FC<ProfileProps> = ({ isFactory, setStep }) => {
  const dispatch = useAppDispatch()
  const { isLoading, onboardingCompany, selectedCompany } = useAppSelector(
    (state) => state.companies
  )
  const company: Company | null = useMemo(
    () =>
      onboardingCompany?.entity ? JSON.parse(onboardingCompany.entity) : null,
    [onboardingCompany]
  )

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_ORGANIZATION),
    []
  )

  const [logoUrl, setLogoUrl] = useState<string | undefined>(
    company?.certificateUrl
  )
  const [documentUrl, setDocumentUrl] = useState<string | undefined>(
    company?.logoUrl
  )

  useEffect(() => {
    setDocumentUrl(company?.certificateUrl)
    setLogoUrl(company?.logoUrl)
  }, [company])

  const formik = useFormik({
    initialValues: {
      type: company?.type ?? '',
      code: company?.code ?? '',
      name: company?.name ?? '',
      phoneNumber: company?.phoneNumber ?? '',
      emailAddress: company?.emailAddress ?? '',
      stepName: 'PROFILE',
      file: undefined,
      logo: undefined,
    },
    validateOnMount: true,
    validationSchema: isFactory ? factoryValidationSchema : validationSchema,
    onSubmit: async (values) => {
      //Create company other types
      if (!isFactory) {
        try {
          const { file, logo, ...otherValues } = values
          const payload = {
            ...otherValues,
            certificateUrl: documentUrl,
            logoUrl: logoUrl,
            approvalId: onboardingCompany?.id,
          } as CompanyProfileRequest

          //upload files
          const promises: Promise<string | undefined>[] = []
          const uploadedUrls: string[] = []

          if (!documentUrl && file) {
            promises.push(uploadOnboardingDocuments(dispatch, file as File, {}))
            uploadedUrls.push('document')
          }
          if (!logoUrl && logo) {
            promises.push(uploadOnboardingDocuments(dispatch, logo as File, {}))
            uploadedUrls.push('logo')
          }

          if (promises.length > 0) {
            const results = await Promise.all(promises)
            results.forEach((url, index) => {
              if (uploadedUrls[index] === 'document') {
                setDocumentUrl(url)
                payload.certificateUrl = url
              } else if (uploadedUrls[index] === 'logo') {
                setLogoUrl(url)
                payload.logoUrl = url
              }
            })
          }
          await handleSubmitCompany(payload)
        } catch (error) {
          console.error('Error during submission:', error)
        }

        return
      }

      //Create factory
      if (!selectedCompany) {
        console.log('Error', 'Parent organization not found')
        return
      }

      const { name, phoneNumber, emailAddress, stepName, ...otherValues } =
        values
      const payload: FactoryProfileRequest = {
        name,
        phoneNumber,
        emailAddress,
        stepName: 'PROFILE',
        parentOrganizationId: selectedCompany?.id,
        type: 'Factory',
        approvalId: onboardingCompany?.id,
      }
      await handleSubmitCompany(payload)
    },
  })

  const handleSubmitCompany = async (
    payload: CompanyProfileRequest | FactoryProfileRequest
  ) => {
    await createCompany(isSuper, dispatch, payload, () =>
      setStep(CreateCompanySteps.PAYMENT)
    )
  }

  const [phoneNumber, setPhoneNumber] = useState<string>(
    formik.values.phoneNumber
  )
  const handlePhoneChange = (value: string) => {
    setPhoneNumber(value)
    formik.setFieldTouched('phoneNumber', true, false)
    formik.setFieldValue('phoneNumber', value.replace(/\s/g, ''))
  }

  return (
    <Stack spacing={2} sx={{ height: '100%' }}>
      <Typography variant="h6" fontWeight={600}>
        Company profile
      </Typography>
      <Typography
        variant="body1"
        fontWeight={400}
        sx={{ color: '#344054', fontSize: '1rem' }}
      >
        Please ensure the account details provided match the company's identity.
      </Typography>
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <Stack spacing={2}>
            <Stack
              spacing={3}
              sx={{
                padding: 3,
                backgroundColor: '#FFFFFF',
                borderRadius: '0.5rem',
              }}
            >
              <Stack spacing={2}>
                {!isFactory && (
                  <Stack direction="row" spacing={2}>
                    <Stack sx={{ width: '100%' }} spacing={1}>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        Select Company Type{' '}
                        <Box component="span" sx={{ color: 'primary.main' }}>
                          *
                        </Box>
                      </Typography>
                      <Autocomplete
                        disablePortal
                        size="small"
                        id="type"
                        options={COMPANY_TYPES}
                        {...formik.getFieldProps('type')}
                        onChange={(_, value) => {
                          formik.setFieldValue('type', value)
                        }}
                        renderInput={(params) => (
                          <TextField
                            hiddenLabel
                            placeholder="Select type"
                            {...params}
                            error={Boolean(
                              formik.touched.type && formik.errors.type
                            )}
                            helperText={
                              formik.touched.type && formik.errors.type
                                ? formik.errors.type
                                : ''
                            }
                          />
                        )}
                      />
                    </Stack>

                    <Stack sx={{ width: '100%' }} spacing={1}>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        Enter EATTA Membership Code{' '}
                        <Box component="span" sx={{ color: 'primary.main' }}>
                          *
                        </Box>
                      </Typography>
                      <TextField
                        hiddenLabel
                        size="small"
                        type="text"
                        placeholder="Enter membership code"
                        sx={{ marginBlock: '0' }}
                        margin={'normal'}
                        {...formik.getFieldProps('code')}
                        fullWidth
                        error={Boolean(
                          formik.touched.code && formik.errors.code
                        )}
                        helperText={formik.touched.code && formik.errors.code}
                      />
                    </Stack>
                  </Stack>
                )}

                <Stack sx={{ width: '100%' }}>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    Enter the company name{' '}
                    <Box component="span" sx={{ color: 'primary.main' }}>
                      *
                    </Box>
                  </Typography>
                  <TextField
                    hiddenLabel
                    size="small"
                    type="text"
                    placeholder="Enter name"
                    sx={{ marginBlock: '0' }}
                    margin={'normal'}
                    {...formik.getFieldProps('name')}
                    fullWidth
                    error={Boolean(formik.touched.name && formik.errors.name)}
                    helperText={formik.touched.name && formik.errors.name}
                  />
                </Stack>

                <FormControl
                  fullWidth
                  error={Boolean(
                    formik.touched.phoneNumber && formik.errors.phoneNumber
                  )}
                  sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}
                >
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    Company Phone number
                    <Box component="span" sx={{ color: 'primary.main' }}>
                      {isFactory ? '(optional)' : '*'}
                    </Box>
                  </Typography>
                  <MuiTelInput
                    hiddenLabel
                    size="small"
                    value={phoneNumber}
                    name="phoneNumber"
                    defaultCountry="KE"
                    onlyCountries={['KE', 'UG', 'TZ', 'BI']}
                    onChange={handlePhoneChange}
                    slotProps={{
                      htmlInput: {
                        maxLength: 15,
                      },
                    }}
                  />
                  {formik.touched.phoneNumber && formik.errors.phoneNumber && (
                    <FormHelperText error>
                      {formik.errors.phoneNumber}
                    </FormHelperText>
                  )}
                </FormControl>
                <Stack spacing={1}>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    Enter company email{' '}
                    <Box component="span" sx={{ color: 'primary.main' }}>
                      {isFactory ? '(optional)' : '*'}
                    </Box>
                  </Typography>
                  <TextField
                    hiddenLabel
                    size="small"
                    type="text"
                    placeholder="<EMAIL>"
                    sx={{ marginBlock: '0' }}
                    margin={'normal'}
                    {...formik.getFieldProps('emailAddress')}
                    fullWidth
                    error={Boolean(
                      formik.touched.emailAddress && formik.errors.emailAddress
                    )}
                    helperText={
                      formik.touched.emailAddress && formik.errors.emailAddress
                    }
                  />
                </Stack>
              </Stack>
            </Stack>
            {!isFactory && (
              <>
                <Typography fontWeight="bold">
                  Attach Company Logo (Optional).
                </Typography>
                <Stack
                  sx={{
                    padding: 3,
                    backgroundColor: '#FFFFFF',
                    borderRadius: '0.5rem',
                  }}
                >
                  <FileUpload
                    fileUrl={company?.logoUrl}
                    description="Only Images file allowed."
                    mimeTypes={['image/png', 'image/jpeg', 'image/jpg']}
                    required={false}
                    disabled={isLoading || Boolean(company?.logoUrl)}
                    onFileChange={(file) => formik.setFieldValue('logo', file)}
                  />
                </Stack>

                <Typography fontWeight="bold">
                  Attach EATTA Registration Document:
                </Typography>
                <Stack
                  sx={{
                    padding: 3,
                    backgroundColor: '#FFFFFF',
                    borderRadius: '0.5rem',
                  }}
                >
                  <FileUpload
                    fileUrl={company?.certificateUrl}
                    description="Only PDF file allowed."
                    mimeTypes={['application/pdf']}
                    required={true}
                    disabled={isLoading || Boolean(company?.certificateUrl)}
                    onFileChange={(file) => formik.setFieldValue('file', file)}
                  />
                </Stack>
              </>
            )}

            <Stack direction="row" spacing={2} sx={{ width: '100%' }}>
              <AccessControlWrapper
                rights={ACCESS_CONTROLS.CREATE_ORGANIZATION}
                makerId={onboardingCompany?.maker}
                isMake={true}
              >
                <Button
                  fullWidth
                  variant="contained"
                  type="submit"
                  disabled={!formik.isValid || isLoading}
                  endIcon={
                    isLoading ? (
                      <CircularProgress size={20} thickness={3.0} />
                    ) : undefined
                  }
                >
                  Next
                </Button>
              </AccessControlWrapper>
            </Stack>
          </Stack>
        </Form>
      </FormikProvider>
    </Stack>
  )
}

export default Profile
