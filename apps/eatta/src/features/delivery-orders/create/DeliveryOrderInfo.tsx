'use client'
import * as React from 'react'
import { Stack, Typography } from '@mui/material'
import { useAppSelector } from '@/store'

interface DeliveryInfoPair {
  label: string
  value: string
}

const DeliveryOrderInfo: React.FC = () => {
  const { selectedDeliveryOrderEntries } = useAppSelector(
    (state) => state.invoices
  )

  const data: DeliveryInfoPair[] = [
    {
      label: 'TRD Number:',
      value: selectedDeliveryOrderEntries?.trdDocNumber || '',
    },
    {
      label: 'Invoice No:',
      value: selectedDeliveryOrderEntries?.invoiceNumber || '',
    },
    {
      label: 'Auction No:',
      value: selectedDeliveryOrderEntries?.auctionNo || '',
    },
    {
      label: 'Sale Date:',
      value: selectedDeliveryOrderEntries?.saleDate || '',
    },
  ]

  return (
    <Stack
      direction="row"
      spacing={0.5}
      width="100%"
      sx={{ mt: 4, mb: 2, flexWrap: 'wrap' }}
    >
      {data.map(({ label, value }) => (
        <Stack direction="row" gap="0.5rem" alignItems="center" key={label}>
          <Typography
            sx={{
              fontSize: '1rem',
              fontWeight: 500,
              color: '#101828',
              textWrap: 'nowrap',
            }}
          >
            {label}
          </Typography>
          <Typography
            sx={{
              fontSize: '1rem',
              fontWeight: 700,
              color: '#344054',
              textWrap: 'nowrap',
            }}
          >
            {value}
          </Typography>
        </Stack>
      ))}
    </Stack>
  )
}

export default DeliveryOrderInfo
