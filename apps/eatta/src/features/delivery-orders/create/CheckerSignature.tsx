'use client'
import React, { FC, useState } from 'react'
import {
  Button,
  Typography,
  CircularProgress,
  Stack,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
} from '@mui/material'
import * as Yup from 'yup'
import { Form, FormikProvider, useFormik } from 'formik'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { useAppDispatch, useAppSelector } from '@/store'
import { ConfirmCancel } from '@/components/ConfirmCancel'
import { signDeliveryOrder } from '@/store/actions/invoices'
import { useRouter } from 'next/navigation'

interface CheckerSignatureProps {
  open: boolean
  onClose: () => void
  isLoading?: boolean
  actionType?: 'MAKER' | 'CHECKER'
}

const CheckerSignature: FC<CheckerSignatureProps> = ({
  open,
  onClose,
  isLoading,
  actionType,
}) => {
  const dispatch = useAppDispatch()
  const router = useRouter()
  const [toggleApproveTerms, setToggleApproveTerms] = useState<boolean>(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const { decodedToken } = useAppSelector((state) => state.auth)
  const { selectedDeliveryOrderEntries } = useAppSelector(
    (state) => state.invoices
  )

  const formik = useFormik({
    initialValues: {
      fullName: '',
      approvedTerms: false,
    },
    validateOnMount: true,
    validationSchema: Yup.object({
      fullName: Yup.string()
        .required('Full Name should not be empty')
        .oneOf([decodedToken.name || ''], 'Full Name should match your name'),
      approvedTerms: Yup.boolean().oneOf(
        [true],
        'Approve Terms should not be empty'
      ),
    }),
    onSubmit: (values) => {
      signDeliveryOrder(dispatch, {
        orderId: selectedDeliveryOrderEntries?.id || '',
        signer: values.fullName,
      }, () => {
        onClose()
      })
      router.replace(`/delivery-orders?tab=0`)
    },
  })

  const handleClose = () => {
    if (formik.dirty) {
      setShowConfirmDialog(true)
    } else {
      onClose()
    }
  }

  const handleConfirmClose = () => {
    setShowConfirmDialog(false)
    onClose()
    formik.resetForm()
  }

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={handleClose}>
      <Stack
        spacing={2}
        sx={{
          bgcolor: '#FFFFFF',
          width: '100%',
          p: 1,
        }}
      >
        <DialogTitle fontWeight={600}>
          <Typography
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
          >
            Please sign before you send.
          </Typography>
        </DialogTitle>
        <DialogContent>
          <FormikProvider value={formik}>
            <Form onSubmit={formik.handleSubmit}>
              <Stack
                spacing={2}
                sx={{
                  width: '100%',
                }}
              >
                <Typography
                  sx={{ fontSize: '1rem', fontWeight: 400, color: '#000A12' }}
                >
                  Please type your full name{' '}
                  <strong>({decodedToken.name})</strong> below to sign this
                  delivery order. This confirms that you've reviewed the details
                  and approve it for release.
                </Typography>

                <Typography
                  sx={{ fontSize: '1rem', fontWeight: 400, color: '#000A12' }}
                >
                  Once you click Send Delivery Order:
                </Typography>

                <Stack direction="column" spacing={2} sx={{ mt: 2 }}>
                  <Typography
                    sx={{ fontSize: '1rem', fontWeight: 700, color: '#000A12' }}
                  >
                    &#x2022; A copy will be sent to the warehouse and buyer.
                  </Typography>

                  <Typography
                    sx={{ fontSize: '1rem', fontWeight: 700, color: '#000A12' }}
                  >
                    &#x2022; You will receive the original broker's copy for
                    your records.
                  </Typography>
                </Stack>
              </Stack>

              <TextField
                hiddenLabel
                size="small"
                type="text"
                sx={{ marginBottom: '1rem', mt: 2 }}
                {...formik.getFieldProps('fullName')}
                fullWidth
                error={Boolean(
                  formik.touched.fullName && formik.errors.fullName
                )}
                helperText={formik.touched.fullName && formik.errors.fullName}
              />

              <Stack
                direction="row"
                spacing={2}
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: '5px',
                  cursor: 'pointer',
                }}
              >
                <CustomCheckBox
                  onClick={() => setToggleApproveTerms(!toggleApproveTerms)}
                  onChange={() =>
                    formik.setFieldValue(
                      'approvedTerms',
                      !formik.values.approvedTerms
                    )
                  }
                  customColor={'#26b43b'}
                  style={{ padding: '2px' }}
                />
                <Typography variant="body2">
                  I confirm that I have reviewed this Delivery Order and approve
                  its release to the listed parties.
                </Typography>
              </Stack>
              <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  fullWidth
                  disabled={isLoading}
                  onClick={handleClose}
                  sx={{
                    mt: 2,
                  }}
                  endIcon={
                    isLoading ? (
                      <CircularProgress size={20} thickness={3.0} />
                    ) : undefined
                  }
                >
                  {'Cancel'}
                </Button>

                <Button
                  variant="contained"
                  fullWidth
                  disabled={!formik.isValid || !formik.values.approvedTerms}
                  onClick={() => formik.handleSubmit()}
                  sx={{
                    mt: 2,
                  }}
                  endIcon={
                    isLoading ? (
                      <CircularProgress size={20} thickness={3.0} />
                    ) : undefined
                  }
                >
                  {'Send delivery order'}
                </Button>
              </Stack>
            </Form>
          </FormikProvider>
        </DialogContent>
      </Stack>
      <ConfirmCancel
        open={showConfirmDialog}
        title="Discard Changes"
        description="Are you sure you want to discard your changes?"
        confirmLabel="Discard"
        cancelLabel="Keep Editing"
        onConfirm={handleConfirmClose}
        onCancel={() => setShowConfirmDialog(false)}
      />
    </Dialog>
  )
}

export default CheckerSignature
