import { Dispatch } from '@reduxjs/toolkit'
import {
  Bank,
  BankBranch,
  Company,
  CompanyFilters,
  CompanyKycCheckRequest,
  CompanyKycCheckResponse,
  CompanyKycDetails,
  CompanyPaymentRequest,
  CompanyProfileRequest,
  CompanyUser,
  EditCompanyUser,
  FactoryProfileRequest,
  MakeRequest,
  ProducerFactory,
  ProducerFactoryFilters,
  UserFilters,
  UserIdentityRequest,
  UserKycCheckRequest,
  UserKycResults,
  UserProfileRequest,
} from '../interfaces'
import {
  setBankBranches,
  setBanks,
  setCompaniesResponse,
  setCompanyApprovalRequest,
  setCompanyKycCheckResponse,
  setCompanyKycDetails,
  setCompanyUsersResponse,
  setFactoryResponse,
  setIsCheckingKyc,
  setIsCreatingFactory,
  setIsCreatingUser,
  setIsCreatingWarehouses,
  setIsEditingUser,
  setIsLoadingCompanies,
  setIsUpdatingWarehouse,
  setOnboardedCompany,
  setOnboardedUser,
  setOnboardingCompany,
  setOnboardingUser,
  setProducerFactoryResponse,
  setSelectedCompanies,
  setSelectedCompany,
  setUploadDocuments,
  setUsersApprovalRequest,
  setWarehousesResponse,
} from '../reducers'
import { setNotification } from '@dtbx/store/reducers'
import { ApiResponse, PaginatedResponse } from '@dtbx/store/interfaces'
import { apiclient } from '@/utils/apiclient'
import { mapObjectToUrlParams, removeFalsyValues } from '@/utils/objectUtil'
import {
  EditFactoryDetails,
  FactoryCreationRequest,
  FactoryFilters,
  FactoryResponse,
} from '../interfaces/factory'
import {
  WarehouseCreationRequest,
  WarehouseFilters,
  WarehouseResponse,
  WarehouseUpdateRequest,
} from '../interfaces/warehouse'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { v4 as uuidv4 } from 'uuid'
import {
  ApprovalRequestFilters,
  IApprovalRequest,
} from '@/store/interfaces/makerChecker'
import { encryptFile } from '@/utils/fileEncryption'
import { AxiosRequestConfig } from 'axios'

export const setSelectedCompanyRecord = (
  dispatch: Dispatch,
  companies: Company[]
) => {
  dispatch(setSelectedCompanies(companies))
}

export const getCompanies = async (
  dispatch: Dispatch,
  params: CompanyFilters
) => {
  try {
    const isBackOffice = checkIfBackOffice()
    dispatch(setIsLoadingCompanies(true))

    const searchParams = mapObjectToUrlParams({ ...params, ascending: false })

    const url = isBackOffice
      ? `/backoffice-bff/eatta-service/onboarding/organizations?${searchParams}`
      : `/v1/eatta-service/onboarding/organizations?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<Company>>(url)
    dispatch(setCompaniesResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const getCompanyApprovalRequests = async (
  dispatch: Dispatch,
  filters: ApprovalRequestFilters
) => {
  try {
    dispatch(setIsLoadingCompanies(true))

    const searchParams = mapObjectToUrlParams({ ...filters })

    const url = `/backoffice-auth/maker-checker/approvals?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<IApprovalRequest>>(url)

    dispatch(setCompanyApprovalRequest(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const createCompany = async (
  isSuper: boolean,
  dispatch: Dispatch,
  data:
    | CompanyProfileRequest
    | CompanyPaymentRequest
    | MakeRequest
    | FactoryProfileRequest,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsLoadingCompanies(true))

    const url = `/backoffice-bff/eatta-service/onboarding/register-organization/${isSuper ? '' : 'make'}`
    const res = await apiclient.post(url, removeFalsyValues(data))

    if (isSuper && data.stepName === 'SUBMISSION') {
      dispatch(setOnboardedCompany(res.data.data))
    } else {
      dispatch(setOnboardingCompany(isSuper ? res.data.data : res.data))
    }

    dispatch(
      setNotification({
        message: `Company details saved successfully  ${!isSuper && data.stepName === 'SUBMISSION' ? 'and is pending approval' : ''}`,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const approveCompany = async (
  dispatch: Dispatch,
  approvalId: string,
  comments: string,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsLoadingCompanies(true))

    const url = `/backoffice-bff/eatta-service/onboarding/register-organization/approve/${approvalId}`
    const res = await apiclient.put(url, { comments })
    dispatch(setOnboardedCompany(res.data.data))
    dispatch(
      setNotification({
        message: 'Company approved successfully',
        type: 'success',
      })
    )
    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const createUser = async (
  isSuper: boolean,
  dispatch: Dispatch,
  data: UserIdentityRequest | UserProfileRequest | MakeRequest,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsCreatingUser(true))

    const url = `/backoffice-bff/eatta-service/onboarding/register-user/${isSuper ? '' : 'make'}`
    const res = await apiclient.post(url, removeFalsyValues(data))
    dispatch(
      setNotification({
        message: `User details saved successfully  ${!isSuper && data.stepName === 'SUBMISSION' ? 'and is pending approval' : ''}`,
        type: 'success',
      })
    )

    if (isSuper && data.stepName === 'SUBMISSION') {
      dispatch(setOnboardedUser(res.data.data))
    } else {
      dispatch(setOnboardingUser(isSuper ? res.data.data : res.data))
    }

    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message

    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingUser(false))
  }
}
export const approveUser = async (
  dispatch: Dispatch,
  approvalId: string,
  comments: string,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsCreatingUser(true))

    const url = `/backoffice-bff/eatta-service/onboarding/register-user/approve/${approvalId}`
    const res = await apiclient.put(url, { comments })
    dispatch(setOnboardedUser(res.data.data))
    dispatch(
      setNotification({
        message: 'User approved successfully',
        type: 'success',
      })
    )
    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingUser(false))
  }
}
export const userKycCheck = async (
  dispatch: Dispatch,
  user: UserIdentityRequest,
  onSuccess: (result: UserKycResults) => void
) => {
  try {
    dispatch(setIsCreatingUser(true))
    const { citizenship, nationalId, firstName, middleName, lastName } = user
    const payload: UserKycCheckRequest = {
      idNumber: nationalId,
      firstName,
      lastName,
      otherName: middleName,
      citizenship,
      requestId: uuidv4(),
      tag: 'EATTA',
    }
    const response = await apiclient.post<UserKycResults>(
      '/backoffice-bff/eatta-service/kyc/user',
      payload
    )
    dispatch(
      setNotification({
        message: `Running IPRS checks for ${user.firstName}`,
        type: 'success',
      })
    )
    onSuccess(response.data)
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingUser(false))
  }
}
//get organization by organization id
export const getOrganizationById = async (
  dispatch: Dispatch,
  organizationId: string
) => {
  try {
    dispatch(setIsLoadingCompanies(true))
    const url = `/backoffice-bff/eatta-service/onboarding/${organizationId}`

    const res = await apiclient.get<ApiResponse<Company>>(url)

    dispatch(setSelectedCompany(res.data.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const getUsersApprovalRequests = async (
  dispatch: Dispatch,
  filters: ApprovalRequestFilters
) => {
  try {
    dispatch(setIsLoadingCompanies(true))

    const searchParams = mapObjectToUrlParams({ ...filters })

    const url = `/backoffice-auth/maker-checker/approvals?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<IApprovalRequest>>(url)

    dispatch(setUsersApprovalRequest(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}
//company users
export const getUsersByOrganizationCode = async (
  dispatch: Dispatch,
  organizationCode: string,
  params: UserFilters
) => {
  const searchParams = mapObjectToUrlParams(params)

  try {
    dispatch(setIsLoadingCompanies(true))
    const url = `/backoffice-bff/eatta-service/users/${organizationCode}?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<CompanyUser>>(url)
    dispatch(setCompanyUsersResponse(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const editUserByAuthUserId = async (
  dispatch: Dispatch,
  authUserId: string,
  editUser: EditCompanyUser
) => {
  try {
    dispatch(setIsEditingUser(true))
    const url = `/backoffice-bff/eatta-service/users/update-user/${authUserId}`
    await apiclient.patch(url, { ...editUser })

    dispatch(
      setNotification({
        message: `User profile updated successfully`,
        type: 'success',
      })
    )
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsEditingUser(false))
  }
}

export const fetchBanks = async (dispatch: Dispatch) => {
  try {
    const [banksRes, branchRes] = await Promise.all([
      apiclient.get<Bank[]>('/backoffice-bff/eatta-service/banks'),
      apiclient.get<BankBranch[]>(
        '/backoffice-bff/eatta-service/banks/branches'
      ),
    ])
    dispatch(setBanks(banksRes.data))
    dispatch(setBankBranches(branchRes.data))
  } catch (e: any) {
    const message = (e as Error).message

    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

//Factory
export const createFactory = async (
  dispatch: Dispatch,
  factory: FactoryCreationRequest,
  onCreateFactorySuccess: () => void
) => {
  try {
    const { parentOrganizationId, type, name, ...paymentDetails } = factory
    dispatch(setIsCreatingFactory(true))
    const res = await apiclient.post<ApiResponse<Company>>(
      '/backoffice-bff/eatta-service/onboarding/register-organization/factory',
      { parentOrganizationId, type, name, ...paymentDetails }
    )
    const organizationId = res.data.data.id
    const url = `/backoffice-bff/eatta-service/onboarding/organization/${organizationId}`
    await apiclient.post(url, paymentDetails)

    dispatch(
      setNotification({
        message: 'Factory added successfully',
        type: 'success',
      })
    )
    onCreateFactorySuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingFactory(false))
  }
}

export const getFactories = async (
  dispatch: Dispatch,
  isBackOffice: boolean,
  params: FactoryFilters
) => {
  try {
    dispatch(setIsLoadingCompanies(true))
    const searchParams = mapObjectToUrlParams({ ...params, ascending: false })
    const url = isBackOffice
      ? `/backoffice-bff/eatta-service/onboarding/factories?${searchParams}`
      : `/v1/eatta-service/onboarding/factories?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<FactoryResponse>>(url)
    dispatch(setFactoryResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const getProducerFactories = async (
  dispatch: Dispatch,
  params: ProducerFactoryFilters
) => {
  try {
    dispatch(setIsLoadingCompanies(true))
    const searchParams = mapObjectToUrlParams({ ...params, ascending: false })
    const url = `/v1/eatta-service/catalogue/query/producer-factory-summary?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<ProducerFactory>>(url)
    dispatch(setProducerFactoryResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const updateFactoryDetails = async (
  dispatch: Dispatch,
  organizationId: string,
  editFactory: EditFactoryDetails
) => {
  try {
    dispatch(setIsCreatingFactory(true))
    const url = `/backoffice-bff/eatta-service/onboarding/${organizationId}`
    await apiclient.patch(url, { ...editFactory })
    dispatch(
      setNotification({
        message: 'Factory saved successfully',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingFactory(false))
  }
}
//warehouse
export const createWarehouse = async (
  dispatch: Dispatch,
  warehouse: WarehouseCreationRequest,
  onCreateWarehouseSuccess: () => void
) => {
  try {
    dispatch(setIsCreatingWarehouses(true))
    const url = `/backoffice-bff/eatta-service/onboarding/register-godown`
    await apiclient.post<WarehouseResponse>(url, warehouse)
    dispatch(
      setNotification({
        message: 'Warehouse created successfully',
        type: 'success',
      })
    )
    onCreateWarehouseSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingWarehouses(false))
  }
}

export const updateWarehouse = async (
  dispatch: Dispatch,
  warehouse: WarehouseUpdateRequest,
  onUpdateWarehouseSuccess: () => void
) => {
  try {
    dispatch(setIsUpdatingWarehouse(true))
    const url = `/backoffice-bff/eatta-service/onboarding/update-godown/${warehouse.warehouseId}`
    await apiclient.patch<WarehouseResponse>(url, warehouse)

    dispatch(
      setNotification({
        message: 'Warehouse updated successfully',
        type: 'success',
      })
    )
    onUpdateWarehouseSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsUpdatingWarehouse(false))
  }
}

export const getWarehouses = async (
  dispatch: Dispatch,
  params: WarehouseFilters
) => {
  try {
    dispatch(setIsLoadingCompanies(true))
    const searchParams = mapObjectToUrlParams({ ...params, ascending: false })
    const url = `/backoffice-bff/eatta-service/onboarding/fetch-godowns?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<WarehouseResponse>>(url)
    dispatch(setWarehousesResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const uploadOnboardingDocuments = async (
  dispatch: Dispatch,
  file: File,
  config: AxiosRequestConfig
): Promise<string | undefined> => {
  try {
    dispatch(setUploadDocuments(true))
    const url = `/backoffice-bff/eatta-service/onboarding/documents/upload`
    const res = await apiclient.postForm(url, { file }, config)
    return res.data.data.path
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    return undefined
  } finally {
    dispatch(setUploadDocuments(false))
  }
}

export const downloadOnboardingDocuments = async (
  dispatch: Dispatch,
  resourceUrl: string,
  external: boolean = false
): Promise<File | undefined> => {
  try {
    dispatch(setUploadDocuments(true))
    const fileName = resourceUrl.split('/').pop() || 'Onboarding_form.pdf'
    const url = `/backoffice-bff/eatta-service/onboarding/documents/download?resourceUrl=${resourceUrl}`
    const res = await apiclient.get<File>(url, {
      headers: {
        'Content-Type': 'application/pdf',
      },
      responseType: 'blob',
    })
    const blob = new Blob([res.data])
    if (external) {
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    return new File([blob], fileName)
  } catch (e) {
    console.error(e)
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    return undefined
  } finally {
    dispatch(setUploadDocuments(false))
  }
}
