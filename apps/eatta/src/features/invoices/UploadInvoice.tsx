'use client'

import {
  <PERSON><PERSON>,
  <PERSON>ir<PERSON><PERSON>rogress,
  IconButton,
  Stack,
  Typography,
} from '@mui/material'
import React, { FC, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { UploadIcon } from '@dtbx/ui/components/SvgIcons'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import { getRoutePath } from '@/utils/routePath'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import { AuctionPicker } from '@/components/AuctionPicker'
import { FileUpload } from '@/components/FileUpload'
import { AuctionSchedule, BrokerInvoicePayload } from '@/store/interfaces'
import { CustomRadioButton } from '@/components/CustomRadioButton'
import { formatDate } from '@dtbx/store/utils'
import { uploadBrokerInvoice } from '@/store/actions'

export interface UploadInvoiceProps {
  title?: string
  file?: File | null
  fileSize?: number
  progress?: number
}

const validationSchema = Yup.object({
  saleNumber: Yup.string().required('Sale number is required'),
  auctionDate: Yup.string().required('Auction date is required'),
  promptDate: Yup.string().required('Prompt date is required'),
  file: Yup.mixed<File>().required('Invoice file is required'),
})

type AuctionDate = {
  day: string
  date: string
}

export const UploadInvoice: FC<UploadInvoiceProps> = ({}) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { isUploadingBrokerInvoices } = useAppSelector(
    (state) => state.invoices
  )
  const [auctionDates, setAuctionDates] = useState<AuctionDate[]>([])
  const [uploadProgress, setUploadProgress] = useState(0)

  const formik = useFormik({
    initialValues: {
      saleNumber: '',
      auctionDate: '',
      promptDate: '',
      file: null,
    },
    validationSchema,
    onSubmit: async (_values) => {
      setUploadProgress(0)
      await uploadBrokerInvoice(
        dispatch,
        formik.values as unknown as BrokerInvoicePayload,
        {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              )
              setUploadProgress(progress)
            }
          },
        },
        handleUploadSuccess
      )
    },
  })

  const handleUploadSuccess = () => {
    router.replace(getRoutePath('/invoices'))
  }

  const handleAuctionScheduleChange = (schedule: AuctionSchedule) => {
    formik.setFieldValue('saleNumber', schedule.saleCode)
    formik.setFieldValue('promptDate', schedule.promptDate)
    const dates = [
      { day: schedule.dayOne, date: schedule.dayOneDate },
      { day: schedule.dayTwo, date: schedule.dayTwoDate },
    ]
    setAuctionDates(dates)
  }

  return (
    <Stack
      spacing={3}
      sx={{
        width: '100%',
        maxWidth: '42rem',
        mx: 'auto',

        px: 4,
        py: 10,
      }}
    >
      <Stack
        spacing={3}
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
        }}
      >
        <IconButton
          sx={{
            backgroundColor: '#FFFFFF',
            borderRadius: '8px',
            border: '1px solid #D0D5DD',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          onClick={() => router.back()}
        >
          <ArrowBackIosNewOutlinedIcon />
        </IconButton>

        <Typography
          variant="h4"
          sx={{
            fontWeight: 600,
            fontSize: '1.875rem',
            color: '#101828',
            mb: 4,
            textAlign: 'center',
          }}
        >
          Upload New Invoice
        </Typography>
      </Stack>

      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <Stack
            spacing={3}
            paddingBlock={4}
            paddingInline={3}
            sx={{ backgroundColor: '#FFFFFF', borderRadius: '10px' }}
          >
            <Stack spacing={1.5}>
              <Typography fontWeight="bolder">Select sale:</Typography>

              <AuctionPicker
                sx={{ height: '3rem' }}
                placeholder="Select sale:"
                value={formik.values.saleNumber}
                onChange={handleAuctionScheduleChange}
                touched={formik.touched.saleNumber}
                error={formik.errors.saleNumber}
                onBlur={() => {
                  formik.setFieldTouched('saleNumber', true)
                }}
                required
              />
            </Stack>

            <Stack spacing={1.5}>
              <Typography fontWeight="bolder">Select Auction Date:</Typography>
              {auctionDates.map((auctionDates) => {
                const value = auctionDates.date
                const selected = formik.values.auctionDate === value
                return (
                  <Stack
                    key={value}
                    spacing={0.5}
                    display="flex"
                    flexDirection="row"
                    justifyContent="space-between"
                    alignItems="center"
                    sx={{
                      paddingInline: 2,
                      paddingBlock: 1,
                      borderRadius: 2,
                      border: selected ? '2px solid #26b43b' : '1px solid #ccc',
                      backgroundColor: selected ? '#f5fff5' : 'transparent',
                      cursor: 'pointer',
                    }}
                    onClick={() => formik.setFieldValue('auctionDate', value)}
                  >
                    <Typography variant="subtitle2">
                      {auctionDates.day} : {formatDate(value)}
                    </Typography>

                    <CustomRadioButton
                      checked={formik.values.auctionDate === value}
                      customColor={'#26b43b'}
                    />
                  </Stack>
                )
              })}
            </Stack>

            <FileUpload
              progress={uploadProgress}
              disabled={isUploadingBrokerInvoices}
              onFileChange={(file) => formik.setFieldValue('file', file)}
            />

            <Typography>
              Please ensure you use the invoice template to ensure the lots and
              prices you upload are processed accurately to match your records.{' '}
              <a href="/templates/invoice.xlsx" download>
                Download Template
              </a>
            </Typography>

            <Stack direction="row" spacing={3} sx={{ mt: 4 }}>
              <Button
                type="button"
                fullWidth
                variant="contained"
                onClick={() => router.back()}
                sx={{ background: '#D92D20' }}
              >
                Cancel
              </Button>
              <Button
                fullWidth
                variant="contained"
                type="submit"
                startIcon={
                  isUploadingBrokerInvoices ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <UploadIcon
                      stroke={formik.isValid ? ' #FFFFFF' : '#344054'}
                    />
                  )
                }
                sx={{
                  border: '1px solid #E4E7EC',
                  fontWeight: 600,
                  textWrap: 'noWrap',
                  '&:hover': {
                    color: '#FFFFFF',
                    '& svg': {
                      stroke: '#FFFFFF',
                    },
                  },
                }}
                disabled={!formik.isValid || isUploadingBrokerInvoices}
              >
                {isUploadingBrokerInvoices ? 'Uploading...' : 'Upload'}
              </Button>
            </Stack>
          </Stack>
        </Form>
      </FormikProvider>
    </Stack>
  )
}
