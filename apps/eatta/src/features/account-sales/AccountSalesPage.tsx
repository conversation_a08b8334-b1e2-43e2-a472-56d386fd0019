'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { Stack, Divider, Typography } from '@mui/material'
import { CatalogueFilters } from '@/store/interfaces'
import { AccountSalesTable } from './AccountSalesTable'
import { MainPageHeader } from '@/components/MainPageHeader'
import { DEFAULT_FILTER_CONFIG, PageFilters } from '@/components/PageFilters'
import { getAuctionWeek } from '@dtbx/store/utils'
import { useAppDispatch, useAppSelector } from '@/store'
import { downloadCatalogues, getProducerAccountSales } from '@/store/actions'
import { Elipse } from '@/components/SvgIcons/Elipse'
import { checkIfBackOffice } from '@/utils/appTypeChecker'

export const AccountSalesPage = () => {
  const isBackOffice = checkIfBackOffice()
  const { auctionScheduleResponse } = useAppSelector(
    (state) => state.salesSchedule
  )
  const { data } = auctionScheduleResponse
  const week = getAuctionWeek().toString()
  const [filters, setFilters] = useState<CatalogueFilters>({
    producer: '',
    amount: 0,
    lotNo: '',
    broker: '',
    buyerName: '',
    saleDate: week,
    page: 1,
    size: 10,
    year: new Date().getFullYear(),
  })
  const dispatch = useAppDispatch()

  const handleSearch = useCallback((newFilters: CatalogueFilters) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
      page: 1,
    }))
  }, [])

  const handleExport = useCallback(() => {
    downloadCatalogues(dispatch, isBackOffice, filters)
  }, [dispatch, isBackOffice, filters])

  const promptDate = data.find(
    (schedule) => schedule.saleCode === filters.saleDate
  )

  useEffect(() => {
    if (filters.broker && filters.factory) {
      getProducerAccountSales(dispatch, filters)
    }
  }, [filters])

  return (
    <Stack sx={{ height: '100%' }}>
      <MainPageHeader />

      <Divider />

      <PageFilters
        title="Account Sales"
        subtitle="Expected sales per account, per broker and per mark."
        onSearch={handleSearch}
        filters={filters}
        filterConfig={{
          ...DEFAULT_FILTER_CONFIG,
          showBrokerFilter: true,
          showStatus: false,
          showExport: true,
          showFactory: true,
          showSearchBox: false,
        }}
        onExport={handleExport}
      />
      <Stack
        direction="row"
        spacing={3}
        sx={{
          backgroundColor: '#f2f4f7',
          paddingInline: '1.5rem',
          paddingBlock: '1rem',
        }}
      >
        <Typography
          sx={{
            color: '#000A12',
            fontSize: '1rem',
            fontWeight: 500,
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
          }}
        >
          <Elipse /> {`Auction Number: ${filters.year}/${filters.saleDate}`}
        </Typography>
        <Typography
          sx={{
            color: '#000A12',
            fontSize: '1rem',
            fontWeight: 500,
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
          }}
        >
          <Elipse /> {`Prompt Date: ${promptDate?.promptDate || ''}`}
        </Typography>
      </Stack>
      <AccountSalesTable broker={filters.broker} factory={filters.factory} />
    </Stack>
  )
}
