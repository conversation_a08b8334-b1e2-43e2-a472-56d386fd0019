/**
 * <AUTHOR> on 05/06/2025
 */
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Stack, Typography } from '@mui/material'
import { FC } from 'react'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { ArrowRightIcon } from '@dtbx/ui/icons'
import { InvoiceEslip } from '@/store/interfaces'
import {
  AccessControlWrapper,
  formatCurrency,
  formatDate,
} from '@dtbx/store/utils'
import { KeyboardArrowRight } from '@mui/icons-material'
import { ACCESS_CONTROLS } from '@/utils/constants'

interface ESlipDetailsProps {
  eslip: InvoiceEslip
  canSelect?: boolean
  onEslipSelected?: (eslip: InvoiceEslip) => void
}

export const EslipDetails: FC<ESlipDetailsProps> = ({
  eslip,
  canSelect = false,
  onEslipSelected,
}) => {
  const {
    invoiceNumber,
    totalAmountToPay,
    totalSoldWeight,
    balance,
    paidAmount,
    lotCount,
    buyer,
    promptDate,
    created<PERSON><PERSON>,
  } = eslip
  return (
    <Stack
      spacing={3}
      padding={3}
      sx={{
        border: '1px solid #D0D5DD',
        borderRadius: '6px',
      }}
    >
      <Stack direction="row" justifyContent="flex-start" alignItems="center">
        <StatusChip label="Unallocated" status="warn" />
      </Stack>

      <Stack direction="row" spacing={2}>
        <Stack spacing={2} flex={1}>
          <Typography
            variant="body2"
            sx={{ color: '#475467', fontWeight: 400, fontSize: '0.875rem' }}
          >
            E-Slip Number:
          </Typography>
          <Typography
            variant="body1"
            sx={{ color: '#101828', fontWeight: 600, fontSize: '1.125rem' }}
          >
            {invoiceNumber}
          </Typography>
        </Stack>
        <Stack spacing={2} flex={1}>
          <Typography
            variant="body2"
            sx={{ color: '#475467', fontWeight: 400, fontSize: '0.875rem' }}
          >
            Created By:
          </Typography>
          <Typography
            variant="body1"
            sx={{ color: '#101828', fontWeight: 600, fontSize: '1.125rem' }}
          >
            {buyer ?? createdBy ?? ''}
          </Typography>
        </Stack>
      </Stack>
      <Divider />

      <Stack spacing={3}>
        <Stack direction="row" justifyContent="space-between" spacing={2}>
          <Typography
            variant="body2"
            sx={{ color: '#475467', fontWeight: 600, fontSize: '1.125rem' }}
          >
            Amount Payable ($)
          </Typography>
          <Typography
            variant="body1"
            sx={{ color: '#101828', fontWeight: 600, fontSize: '1.125rem' }}
          >
            {formatCurrency(totalAmountToPay, 'USD', 'en-US')}
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between" spacing={2}>
          <Typography
            variant="body2"
            sx={{ color: '#475467', fontWeight: 600, fontSize: '1.125rem' }}
          >
            Amount Paid ($)
          </Typography>
          <Typography
            variant="body1"
            sx={{ color: '#101828', fontWeight: 600, fontSize: '1.125rem' }}
          >
            {formatCurrency(paidAmount, 'USD', 'en-US')}
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between" spacing={2}>
          <Typography
            variant="body2"
            sx={{ color: '#475467', fontWeight: 600, fontSize: '1.125rem' }}
          >
            Balance ($)
          </Typography>
          <Typography
            variant="body1"
            sx={{ color: '#101828', fontWeight: 600, fontSize: '1.125rem' }}
          >
            {formatCurrency(balance, 'USD', 'en-US')}
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between" spacing={2}>
          <Typography
            variant="body2"
            sx={{ color: '#475467', fontWeight: 600, fontSize: '1.125rem' }}
          >
            No of Lots
          </Typography>

          <Typography
            variant="body1"
            sx={{ color: '#101828', fontWeight: 600, fontSize: '1.125rem' }}
          >
            {lotCount}
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between" spacing={2}>
          <Typography
            variant="body2"
            sx={{ color: '#475467', fontWeight: 600, fontSize: '1.125rem' }}
          >
            Total Sold Weight (Kgs)
          </Typography>

          <Typography
            variant="body1"
            sx={{ color: '#101828', fontWeight: 600, fontSize: '1.125rem' }}
          >
            {totalSoldWeight}
          </Typography>
        </Stack>
        <Stack direction="row" justifyContent="space-between" spacing={2}>
          <Typography
            variant="body2"
            sx={{ color: '#475467', fontWeight: 600, fontSize: '1.125rem' }}
          >
            Prompt Date
          </Typography>

          <Typography
            variant="body1"
            sx={{ color: '#101828', fontWeight: 600, fontSize: '1.125rem' }}
          >
            {formatDate(promptDate)}
          </Typography>
        </Stack>
      </Stack>
      {canSelect && (
        <AccessControlWrapper rights={ACCESS_CONTROLS.ALLOCATE_PAYMENT}>
          <Button
            onClick={() =>
              onEslipSelected ? onEslipSelected(eslip) : undefined
            }
            fullWidth
            variant="contained"
            color="primary"
            endIcon={<KeyboardArrowRight />}
            sx={{
              border: '1px solid #73FF96',
              borderRadius: '8px',
              backgroundColor: 'transparent',
              color: 'primary.main',
              fontSize: '1rem',
              fontWeight: 600,
              textTransform: 'none',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              textWrap: 'noWrap',
              boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
              '&:hover': {
                bgcolor: 'primary.main',
                color: '#FFFFFF',
                border: 'transparent',
                '& .MuiSvgIcon-root': {
                  color: '#FFFFFF',
                },
              },
            }}
          >
            Allocate Slip
          </Button>
        </AccessControlWrapper>
      )}
    </Stack>
  )
}
