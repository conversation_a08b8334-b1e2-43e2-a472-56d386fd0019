import React from 'react'
import { Stack, Typography, Divider } from '@mui/material'
import { GradeSummary as GradeSummaryType } from '@/store/interfaces'

export interface GradeSummaryProps {
  title: string
  data: GradeSummaryType
}

export const GradeSummary: React.FC<GradeSummaryProps> = ({ title, data }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  return (
    <Stack spacing={2} sx={{ width: '100%' }}>
      <Typography
        variant="h6"
        sx={{
          color: '#344054',
          fontWeight: 600,
          fontSize: '1rem',
          textAlign: 'start',
          mb: 2,
        }}
      >
        {title}
      </Typography>

      <Stack spacing={2}>
        <Stack direction="row" justifyContent="space-between">
          <Typography
            sx={{
              color: '#344054',
              fontWeight: 600,
              fontSize: '0.875rem',
            }}
          >
            Gross Value (USD)
          </Typography>
          <Typography
            sx={{
              color: '#000A12',
              fontWeight: 600,
              fontSize: '0.875rem',
            }}
          >
            {formatCurrency(data.totalGrossValue)}
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between">
          <Typography
            sx={{
              color: '#D92D20',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            Broker Commission (0.75%)
          </Typography>
          <Typography
            sx={{
              color: '#D92D20',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            {formatCurrency(data.totalBrokerCommissionFromProducer)} USD
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between">
          <Typography
            sx={{
              color: '#D92D20',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            Withholding Tax (5% Broker Commission)
          </Typography>
          <Typography
            sx={{
              color: '#D92D20',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            {formatCurrency(data.totalWithholdingTax)} USD
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between">
          <Typography
            sx={{
              color: '#D92D20',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            Catalogue & (Jar/Crates) on Sold Lots
          </Typography>
          <Typography
            sx={{
              color: '#D92D20',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            0.00 USD
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between">
          <Typography
            sx={{
              color: '#D92D20',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            Warehouse Charges For Sold Lots
          </Typography>
          <Typography
            sx={{
              color: '#D92D20',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            {formatCurrency(data.totalWareHouseCharges)} USD
          </Typography>
        </Stack>

        <Divider sx={{ my: 1 }} />

        <Stack direction="row" justifyContent="space-between">
          <Typography
            sx={{
              color: '#344054',
              fontWeight: 600,
              fontSize: '0.875rem',
            }}
          >
            Net Proceeds Due ($)
          </Typography>
          <Typography
            sx={{
              color: '#000A12',
              fontWeight: 600,
              fontSize: '0.875rem',
            }}
          >
            {formatCurrency(data.totalNetProceedsDue)} USD
          </Typography>
        </Stack>
      </Stack>
    </Stack>
  )
}
