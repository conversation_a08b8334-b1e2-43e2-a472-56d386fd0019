import { <PERSON>complete, Stack, TextField, Typography } from '@mui/material'
import { FC } from 'react'
import { AllCompanyType, COMPANY_TYPES } from '@/store/interfaces'

export interface CompanyTypeSelectorProps {
  label?: string
  placeholder?: string
  options?: readonly AllCompanyType[]
  value?: AllCompanyType | null
  onChange: (value: AllCompanyType | null) => void
}

export const CompanyTypeSelector: FC<CompanyTypeSelectorProps> = ({
  label = 'Select Company Type',
  placeholder = '',
  options = COMPANY_TYPES,
  value,
  onChange,
}) => {
  return (
    <Stack
      spacing={1}
      sx={{
        width: '100%',
        maxWidth: '10rem',
        '.MuiInputBase-input.MuiOutlinedInput-input ': {
          py: '6px !important',
        },
      }}
    >
      <Typography
        variant="body2"
        sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
      >
        {label}
      </Typography>
      <Autocomplete
        disablePortal
        size="small"
        id="disbursement-type-selector"
        options={options}
        value={value}
        onChange={(_, newValue) => {
          onChange(newValue)
        }}
        sx={{
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '3px !important',
          },
          background: '#FFFFFF',
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
            },
          },
          borderRadius: '0.5rem',
          color: '#667085',
        }}
        getOptionLabel={(option) =>
          option
            .replace(/_/g, ' ')
            .toLocaleLowerCase('en-US')
            .replace(/\b\w/g, (c) => c.toUpperCase())
        }
        renderInput={(params) => (
          <TextField
            hiddenLabel
            placeholder={placeholder}
            {...params}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '0.5rem',
              },
            }}
          />
        )}
      />
    </Stack>
  )
}
