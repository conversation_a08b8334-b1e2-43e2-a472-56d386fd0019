import { screen, fireEvent, waitFor, within } from '@testing-library/react'
import { render } from '__tests__/test-utils'
import { expect, describe, it, vi, beforeEach } from 'vitest'
import ResolvedRequestDetails from '@/app/approval-requests/All/Dialog/ResolvedRequestDetails'
import {
  mockApprovalRequest,
  mockCustomerAccount,
  mockDevice,
} from '__tests__/app/stubs/stubs'
import { ApprovalRequestRouting } from '@/app/approval-requests/RequestRouting'

const mockPush = vi.fn()
const mockApprovalRequestRouting = vi.fn()

vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => ({
    push: mockPush,
    pushWithTrailingSlash: vi.fn(),
  }),
}))

vi.mock('@/app/approval-requests/RequestRouting', () => ({
  ApprovalRequestRouting: vi.fn().mockImplementation(async (request, dispatch, router) => {
    mockApprovalRequestRouting()
    router.push('/customers')
  }),
}))

vi.mock('@/store', async () => {
  const actual = await vi.importActual<typeof import('@/store')>('@/store')
  return {
    ...actual,
    useAppDispatch: () => vi.fn(),
  }
})

vi.mock('next/navigation', async (importOriginal) => {
  const mod = await importOriginal<typeof import('next/navigation')>()
  return {
    ...mod,
    useRouter: vi.fn(() => ({
      push: mockPush,
      refresh: vi.fn(),
      prefetch: vi.fn(),
      replace: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
    })),
  }
})

vi.mock('@/store/actions', async () => {
  const { mockCustomerStub } = await import('__tests__/app/stubs/stubs')
  const { mockDevice } = await import('__tests__/app/stubs/stubs')
  const { mockCustomerAccount } = await import('__tests__/app/stubs/stubs')
  return {
    getCustomerProfile: vi.fn().mockResolvedValue(mockCustomerStub),
    getCustomerDeviceDetail: vi.fn().mockResolvedValue(mockDevice),
    getCustomerAccountByAccountNo: vi
      .fn()
      .mockResolvedValue(mockCustomerAccount),
  }
})

vi.mock('@/app/approval-requests/All/Dialog/EntityPreview', () => ({
  AllEntityPreview: ({ entity, keys }: { entity: any; keys: string[] }) => (
    <div data-testid="mock-entity-preview">
      {keys.map((key) => (
        <div key={key} data-testid={`preview-${key}`}>
          {entity?.[key]}
        </div>
      ))}
    </div>
  ),
}))

describe('ResolvedRequestDetails', () => {
  const mockRequest = {
    ...mockApprovalRequest,
    makerCheckerType: {
      ...mockApprovalRequest.makerCheckerType,
      module: 'Customers',
      type: 'UPDATE_CUSTOMERS',
    },
  }
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks()
  })

  describe('openCloseDrawer', () => {
    it('renders and toggles the drawer when clicking on "See request summary"', async () => {
      render(<ResolvedRequestDetails request={mockApprovalRequest} />)

      // MenuItem renders
      const button = screen.getByText(/See request summary/i)
      expect(button).toBeInTheDocument()

      //open drawer on button click
      fireEvent.click(button)
      const drawerHeader = await screen.findByText(/Approval request details/i)
      expect(drawerHeader).toBeInTheDocument()
      expect(screen.getByLabelText(/Maker timestamp/i)).toBeInTheDocument()

      const backBtn = screen.getByRole('button', { name: /Back/i })
      fireEvent.click(backBtn)

      await waitFor(() => {
        expect(
          screen.queryByText(/Approval request details/i)
        ).not.toBeVisible()
      })
      expect(screen.getByLabelText(/Maker timestamp/i)).not.toBeVisible()
    })
  })

  describe('contentRenderingByModule', () => {
    it('renders customer profile data when module is Customer and type does not include CREATE (module: Customers)', async () => {
      render(
        <ResolvedRequestDetails
          request={{
            ...mockApprovalRequest,
            makerCheckerType: {
              ...mockApprovalRequest.makerCheckerType,
              module: 'Customers',
              type: 'UPDATE_USER',
            },
          }}
        />
      )

      // Open drawer
      fireEvent.click(screen.getByText(/See request summary/i))

      await waitFor(() => {
        expect(screen.getByTestId('preview-firstName')).toHaveTextContent(
          'John'
        )
        expect(screen.getByTestId('preview-lastName')).toHaveTextContent('Doe')
      })
    })
    it('renders customer data from entity JSON when type is CREATE_CUSTOMERS (module: Customers)', async () => {
      render(
        <ResolvedRequestDetails
          open
          // @ts-ignore
          request={{
            entity: JSON.stringify({
              firstName: 'Nelson',
              lastName: 'Agwenchez',
            }),
            makerCheckerType: {
              module: 'Customers',
              type: 'CREATE_CUSTOMERS',
              channel: '',
              checkerPermissions: [],
              makerPermissions: [],
              name: '',
              overridePermissions: [],
            },
          }}
        />
      )
      fireEvent.click(screen.getByText(/See request summary/i))

      expect(await screen.findByText('Nelson')).toBeInTheDocument()
      expect(screen.getByText('Agwenchez')).toBeInTheDocument()
    })
    it('should render multiple accounts from parsed JSON entity for CREATE_ACCOUNTS (module: accounts)', async () => {
      const accounts = {
        accounts: [
          {
            accNumber: '****************',
            tariffName: 'Staff',
            accClassDesc: 'test staff desc',
          },
          {
            accNumber: '***************',
            tariffName: 'M-pesa',
            accClassDesc: 'test m-pesa desc',
          },
        ],
      }

      render(
        <ResolvedRequestDetails
          request={{
            ...mockApprovalRequest,
            entity: JSON.stringify(accounts),
            makerCheckerType: {
              ...mockApprovalRequest.makerCheckerType,
              module: 'accounts',
              type: 'CREATE_ACCOUNTS',
            },
          }}
        />
      )

      fireEvent.click(screen.getByText(/See request summary/i))

      await waitFor(() => {
        const accountNumbers = screen.getAllByTestId('preview-accNumber')
        expect(accountNumbers).toHaveLength(2)
        expect(accountNumbers[0]).toHaveTextContent('001')
        expect(accountNumbers[1]).toHaveTextContent('002')
      })
    })
    it('renders single account when type is not CREATE_ACCOUNTS (module: accounts)', async () => {
      const accounts = {
        accounts: [mockCustomerAccount],
      }
      render(
        <ResolvedRequestDetails
          request={{
            ...mockApprovalRequest,
            entity: JSON.stringify(accounts),
            makerCheckerType: {
              ...mockApprovalRequest.makerCheckerType,
              module: 'accounts',
              type: 'UPDATE_ACCOUNTS',
            },
          }}
        />
      )
      fireEvent.click(screen.getByText(/See request summary/i))
      await waitFor(() => {
        const accountNumbers = screen.getAllByTestId('preview-accountNo')
        expect(accountNumbers).toHaveLength(1)
      })
    })
    it('renders device details for ProfileDevices when type is not CREATE_PROFILEDEVICES (module: ProfileDevices)', async () => {
      render(
        <ResolvedRequestDetails
          request={{
            ...mockApprovalRequest,
            entity: JSON.stringify({ profileId: 'profile-1234' }),
            entityId: 'device-1234',
            makerCheckerType: {
              ...mockApprovalRequest.makerCheckerType,
              module: 'ProfileDevices',
              type: 'UPDATE_PROFILEDEVICES',
            },
          }}
        />
      )

      fireEvent.click(screen.getByText(/See request summary/i))
      await waitFor(() => {
        expect(screen.getByTestId('preview-deviceName')).toHaveTextContent(
          mockDevice.deviceName
        )
        expect(screen.getByTestId('preview-deviceType')).toHaveTextContent(
          mockDevice.deviceType
        )
      })
    })
  })
  describe('helper values', () => {
    it('renders maker and checker usernames', async () => {
      render(
        <ResolvedRequestDetails
          request={{
            ...mockApprovalRequest,
            maker: 'John Maker',
            checker: 'Jane Checker',
          }}
        />
      )

      fireEvent.click(screen.getByText(/See request summary/i))

      await waitFor(() => {
        expect(screen.getByLabelText(/^Maker$/)).toHaveValue('John Maker')
        expect(screen.getByLabelText(/^Checker$/)).toHaveValue('Jane Checker')
      })
    })

    it('renders maker and checker comments if available', async () => {
      render(
        <ResolvedRequestDetails
          request={{
            ...mockApprovalRequest,
            makerComments: 'Looks good.',
            checkerComments: 'Approved without issue.',
          }}
        />
      )

      fireEvent.click(screen.getByText(/See request summary/i))

      await waitFor(() => {
        expect(screen.getByLabelText(/Maker comment/)).toHaveValue(
          'Looks good.'
        )
        expect(screen.getByLabelText(/Checker comment/)).toHaveValue(
          'Approved without issue.'
        )
      })
    })

    it('renders formatted maker and checker timestamps', async () => {
      vi.mock('@dtbx/store/utils', () => ({
        formatTimestamp: (date: string) =>
          new Date(date).toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            timeZone: 'UTC',
          }),
      }))

      render(
        <ResolvedRequestDetails
          request={{
            ...mockApprovalRequest,
            dateCreated: '2024-04-22T15:30:00.000Z',
            dateModified: '2024-04-23T08:45:00.000Z',
          }}
        />
      )

      fireEvent.click(screen.getByText(/See request summary/i))

      await waitFor(() => {
        expect(screen.getByLabelText(/Maker timestamp/)).toHaveValue(
          'Apr 22, 2024, 3:30 PM'
        )
        expect(screen.getByLabelText(/Checker timestamp/)).toHaveValue(
          'Apr 23, 2024, 8:45 AM'
        )
      })
    })
  })

  describe.skip('Go to module (Navigation)', () => {
    // beforeEach(() => {
    //   mockPush.mockClear()
    // })
    beforeEach(() => {
      vi.clearAllMocks()

      vi.mocked(ApprovalRequestRouting).mockImplementation(
        async (_, __, router) => {
          router.push('/customers')
        }
      )
    })

    it('should call ApprovalRequestRouting with correct parameters', async () => {
      render(<ResolvedRequestDetails request={mockRequest} />)

      fireEvent.click(
        await screen.findByRole('menuitem', {
          name: /See request summary/i,
        })
      )

      const button = await screen.findByRole('button', {
        name: /Go to module/i,
      })
      fireEvent.click(button)

      await waitFor(() => {
        expect(ApprovalRequestRouting).toHaveBeenCalledWith(
          mockRequest,
          expect.any(Function),
          expect.objectContaining({
            push: expect.any(Function),
            pushWithTrailingSlash: expect.any(Function),
          })
        )
      })
    })

    it('should navigate to customers route', async () => {
      render(<ResolvedRequestDetails request={mockRequest} />)

      // Open drawer
      fireEvent.click(
        await screen.findByRole('menuitem', { name: /See request summary/i })
      )

      await waitFor(() => {
        expect(screen.getByText(/Approval request details/i)).toBeVisible()
      })

      const button = await screen.findByRole('button', {
        name: /Go to module/i,
      })

      fireEvent.click(button)

      await waitFor(
        () => {
          expect(mockApprovalRequestRouting).toHaveBeenCalled()
          expect(mockPush).toHaveBeenCalledWith('/customers')
        },
        { timeout: 2000 }
      )
    })
  })

  describe.skip('Accessibility (Aria)', () => {})
})
