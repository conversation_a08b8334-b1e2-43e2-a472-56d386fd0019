/**
 * <AUTHOR> on 16/06/2025
 */
'use client'

import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { getAuctionWeek } from '@dtbx/store/utils'
import { Divider, Stack } from '@mui/material'
import { DEFAULT_FILTER_CONFIG, PageFilters } from '@/components/PageFilters'
import { TabPanel } from '@dtbx/ui/components/Tabs'
import { CustomTabs, TabType } from '@/components/CustomTabs'
import { MainPageHeader } from '@/components/MainPageHeader'
import { DeliveryOrdersTable } from '@/features/delivery-orders/DeliveryOrdersTable'
import { DeliveryOrderFilters } from '@/store/interfaces/invoices'

const TABS: TabType<string>[] = [
  {
    title: 'New',
    status: 'NEW',
    canSelect: true,
  },
  {
    title: 'Pending Approval',
    status: 'PENDING_APPROVAL',
    canSelect: false,
  },
  {
    title: 'Dispatched',
    status: 'DISPATCHED',
    canSelect: false,
  },
]

export const DeliveryOrderPage = () => {
  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState(TABS)
  const [selectedTab, setSelectedTab] = useState(initialTab)

  const week = useMemo(() => getAuctionWeek().toString(), [])

  const [filters, setFilters] = useState<DeliveryOrderFilters>({
    saleDate: searchParams.get('saleDate') ?? week,
    year: +(searchParams.get('year') ?? new Date().getFullYear()),
    page: 1,
    size: 10,
  })

  const handleSearch = useCallback((newFilters: DeliveryOrderFilters) => {
    setFilters({
      ...newFilters,
      page: 1,
    })
  }, [])

  const handleCountChange = (count: number, tab: TabType<string>) => {
    setTabs((prevTabs) =>
      prevTabs.map((t) =>
        t.title === tab.title ? { ...t, itemCounts: count } : t
      )
    )
  }

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    params.set('year', filters.year.toString())
    params.set('saleDate', filters.saleDate)
    replace(`${pathname}?${params.toString()}`)
  }

  useEffect(() => {
    // Update filters when URL parameters change
    const saleDate = searchParams.get('saleDate')
    const year = searchParams.get('year')

    if (saleDate || year) {
      setFilters((prev) => ({
        ...prev,
        saleDate: saleDate ?? prev.saleDate,
        year: year ? +year : prev.year,
      }))
    }
  }, [searchParams])

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab, filters])

  return (
    <Stack sx={{ height: '100%' }}>
      <MainPageHeader />

      <Divider />

      <PageFilters
        title="Delivery Orders"
        filterConfig={{
          ...DEFAULT_FILTER_CONFIG,
          showStatus: false,
          showExport: false,
          showDateFilter: true,
        }}
        onSearch={handleSearch}
        filters={filters}
        searchByValues={[
          { filterLabel: 'Document Number', filterKey: 'docNumber', type: 'string' },
          { filterLabel: 'TRD Number', filterKey: 'trdDocNumber', type: 'string' },
          { filterLabel: 'Buyer', filterKey: 'buyerCode', type: 'string' },
          { filterLabel: 'Warehouse', filterKey: 'warehouse', type: 'string' },
        ]}
        onExport={() => {}}
      />
      <Divider />
      <CustomTabs
        isLoading={false}
        tabs={tabs}
        selectedTab={selectedTab}
        onTabSelected={setSelectedTab}
      />
      <Stack sx={{ overflow: 'auto' }}>
        {tabs.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            <DeliveryOrdersTable
              status={tab.status}
              filters={filters}
              canSelect={tabs[selectedTab].canSelect}
              onDeliveryOrderCountChange={(count) =>
                handleCountChange(count, tabs[selectedTab])
              }
              onSetCurrentTab={setSelectedTab}
            />
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}
