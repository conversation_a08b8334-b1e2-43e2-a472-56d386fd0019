import React from 'react'
import {
  <PERSON><PERSON>,
  Circular<PERSON>rogress,
  <PERSON><PERSON>,
  Stack,
  Typography,
} from '@mui/material'
import * as Yup from 'yup'
import { UploadIcon } from '@dtbx/ui/icons'
import { uploadCatalogue } from '@/store/actions/catalogues'
import { useAppDispatch, useAppSelector } from '@/store'
import { FileUpload } from '@/components/FileUpload'
import { AuctionPicker } from '@/components/AuctionPicker'
import { AuctionSchedule, AuctionType } from '@/store/interfaces'
import { canUploadAuction } from '@/utils/auctionChecker'
import { Form, FormikProvider, useFormik } from 'formik'

interface UploadCatalogueProps {
  auctionType?: AuctionType
  onClose: (success: boolean) => void
  open: boolean
}

const validationSchema = Yup.object({
  saleDate: Yup.string(),
  file: Yup.mixed<File>().required('catalogue file is required'),
})

export const UploadCatalogue = ({
  auctionType = 'PRE_AUCTION',
  onClose,
  open,
}: UploadCatalogueProps) => {
  const dispatch = useAppDispatch()
  const { isUploadingCatalogues } = useAppSelector((state) => state.catalogues)
  const [uploadProgress, setUploadProgress] = React.useState(0)
  const [dateError, setDateError] = React.useState<string | null>(null)

  const formik = useFormik({
    initialValues: {
      saleDate: '',
      file: null,
    },
    validationSchema,
    onSubmit: async (values) => {
      if (!values.saleDate) {
        setDateError('Please select a sale date')
        return
      }
      setUploadProgress(0)
      if (!formik.values.file) return

      await uploadCatalogue(
        dispatch,
        {
          file: formik.values.file as File,
          saleDate: formik.values.saleDate,
          auctionType: auctionType,
        },
        {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              )
              setUploadProgress(progress)
            }
          },
        },
        handleUploadSuccess
      )
    },
  })

  const handleAuctionScheduleChange = (schedule: AuctionSchedule) => {
    const auctionDate = new Date(schedule.dayOneDate)
    auctionDate.setHours(8, 0, 0, 0)
    formik.setFieldValue('saleDate', schedule.saleCode)

    if (auctionDate < new Date() && auctionType === 'PRE_AUCTION') {
      setDateError(
        'The selected sale date is for a past auction. Please choose an upcoming auction date.'
      )
      return
    }

    if (!canUploadAuction(auctionDate) && auctionType === 'PRE_AUCTION') {
      setDateError(
        'Cannot upload catalogue within 1 hour of auction start time (9:00 AM)'
      )
      return
    }

    setDateError(null)
  }

  const handleUploadSuccess = () => {
    onClose(true)
  }

  return (
    <Dialog fullWidth open={open} onClose={onClose}>
      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <Stack
            spacing={2}
            sx={{
              width: '100%',
              p: 2,
              maxWidth: 683,
              mx: 'auto',
            }}
          >
            <Stack
              padding={2}
              sx={{
                width: '100%',
                display: 'flex',
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 600,
                  fontSize: '1.5rem',
                  color: '#101828',
                  textAlign: 'start',
                  textTransform: 'capitalize',
                }}
              >
                Upload New {auctionType?.toLocaleLowerCase().replace('_', '-')}{' '}
                Catalogue.
              </Typography>
            </Stack>

            <Stack spacing={3} padding={2}>
              <Stack spacing={1.5}>
                <AuctionPicker
                  value={formik.values.saleDate}
                  onChange={handleAuctionScheduleChange}
                  touched={formik.touched.saleDate}
                  error={formik.errors.saleDate}
                  onBlur={() => {
                    formik.setFieldTouched('saleDate', true)
                  }}
                  required
                />
              </Stack>
              {dateError && (
                <Stack>
                  <Typography
                    sx={{
                      color: '#D92D20',
                      fontSize: '0.875rem',
                      textAlign: 'center',
                    }}
                  >
                    {dateError}
                  </Typography>
                </Stack>
              )}
              <FileUpload
                progress={uploadProgress}
                disabled={isUploadingCatalogues || !!dateError}
                onFileChange={(file) => formik.setFieldValue('file', file)}
              />

              <Stack
                direction="row"
                spacing={3}
                sx={{ mt: 4, background: '#FFFFFF' }}
              >
                <Button
                  fullWidth
                  variant="contained"
                  onClick={() => onClose(false)}
                  sx={{ background: '#D92D20' }}
                >
                  Cancel
                </Button>
                <Button
                  fullWidth
                  type="submit"
                  variant="contained"
                  startIcon={
                    isUploadingCatalogues ? (
                      <CircularProgress size={20} color="inherit" />
                    ) : (
                      <UploadIcon
                        stroke={formik.isValid ? ' #FFFFFF' : '#344054'}
                      />
                    )
                  }
                  sx={{
                    border: '1px solid #E4E7EC',
                    fontWeight: 600,
                    textWrap: 'noWrap',
                    '&:hover': {
                      color: '#FFFFFF',
                      '& svg': {
                        stroke: '#FFFFFF',
                      },
                    },
                  }}
                  disabled={
                    isUploadingCatalogues ||
                    !formik.isValid ||
                    !formik.values.file ||
                    !!formik.errors.saleDate
                  }
                >
                  {isUploadingCatalogues ? 'Uploading...' : 'Upload Catalogue'}
                </Button>
              </Stack>
            </Stack>
          </Stack>
        </Form>
      </FormikProvider>
    </Dialog>
  )
}
