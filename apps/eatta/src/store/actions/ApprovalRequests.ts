import { Dispatch } from '@reduxjs/toolkit'
import {
  ApprovalRequestFilters,
  IApprovalRequest,
} from '../interfaces/makerChecker'
import { mapObjectToUrlParams } from '@/utils/objectUtil'
import { apiclient } from '@/utils/apiclient'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { setNotification } from '@dtbx/store/reducers'
import {
  setApprovalRequests,
  setIsLoadingApprovals,
} from '../reducers/approvalRequestsReducer'

export const getApprovalRequests = async (
  dispatch: Dispatch,
  filters: ApprovalRequestFilters
) => {
  try {
    dispatch(setIsLoadingApprovals(true))

    const searchParams = mapObjectToUrlParams({ ...filters })

    const url = `/backoffice-auth/maker-checker/approvals?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<IApprovalRequest>>(url)

    dispatch(setApprovalRequests(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingApprovals(false))
  }
}
