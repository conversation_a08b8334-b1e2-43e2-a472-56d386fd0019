/**
 * <AUTHOR> on 19/12/2024
 */
import { Dispatch } from '@reduxjs/toolkit'
import {
  AccountSalesFilter,
  BrokerInvoice,
  BrokerInvoicePayload,
  BrokerInvoicesEntryResponse,
  InvoicesFilters,
  CreateInvoicePayload,
  DisbursementsEntryResponse,
  InvoiceEslip,
  InvoicesEntryFilters,
  PageFilters,
  ProducerInvoiceEntryFilters,
  ProducerInvoiceEntryResponse,
  RemoveInvoiceEntryPayload,
  TransactionHistoryResponse,
  EslipFilters,
  DeliveryOrderFilters,
  DeliveryOrderResponse,
  SignDeliveryOrderPayload,
} from '@/store/interfaces'
import {
  setAccountSalesEntryData,
  setBrokerInvoiceEntryResponse,
  setBrokerInvoiceResponse,
  setDeliveryOrdersResponse,
  setBrokerInvoicesResponse,
  setDisbursementsEntryResponse,
  setInvoiceEslip,
  setInvoiceEslipsResponse,
  setIsCheckingOut,
  setIsDispatchingInvoices,
  setIsLoading,
  setIsLoadingAccountSalesInfo,
  setIsUploadingBrokerInvoices,
  setIsUploadingCatalogues,
  setProducerInvoiceEntryResponse,
  setSelectedDeliveryOrderEntries,
  setSelectedAuctionEntry,
  setSelectedInvoiceLots,
  setSelectedInvoices,
  setTransactionHistoryResponse,
  setIsSigningDeliveryOrder,
  setIsSubmittingDeliveryOrder,
} from '@/store/reducers'
import { apiclient } from '@/utils/apiclient'
import { ApiResponse, PaginatedResponse } from '@dtbx/store/interfaces'
import { setNotification } from '@dtbx/store/reducers'
import { BrokerInvoicesResponse } from '../interfaces'
import { AxiosRequestConfig } from 'axios'
import { mapObjectToUrlParams } from '@/utils/objectUtil'
import { encryptFile } from '@/app/(client)/(dashboard)/actions'
import { checkIfBackOffice } from '@/utils/appTypeChecker'

export const getInvoiceEslips = async (
  dispatch: Dispatch,
  params: InvoicesFilters | EslipFilters
) => {
  try {
    dispatch(setIsLoading(true))

    const searchParams = mapObjectToUrlParams(params)
    const isBackoffice = checkIfBackOffice()
    const url = isBackoffice
      ? `/backoffice-bff/eatta-service/invoice?${searchParams}`
      : `/v1/eatta-service/invoice?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<InvoiceEslip>>(url)
    dispatch(setInvoiceEslipsResponse(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

export const createInvoiceEslip = async (
  dispatch: Dispatch,
  data: CreateInvoicePayload,
  showCheckout: (id: string) => void
) => {
  try {
    dispatch(setIsCheckingOut(true))
    const url = '/v1/eatta-service/invoice'
    const res = await apiclient.post<ApiResponse<InvoiceEslip>>(url, data)
    //reset selections
    dispatch(setSelectedInvoices([]))
    dispatch(setSelectedInvoiceLots([]))
    showCheckout(res.data.data.id)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCheckingOut(false))
  }
}

export const getInvoiceEslipsById = async (
  dispatch: Dispatch,
  eslipId: string
) => {
  try {
    dispatch(setIsLoading(true))

    const url = `/v1/eatta-service/invoice/${eslipId}`

    const res = await apiclient.get<ApiResponse<InvoiceEslip>>(url)
    dispatch(setInvoiceEslip(res.data.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

export const removeInvoiceEslipEntry = async (
  dispatch: Dispatch,
  data: RemoveInvoiceEntryPayload
) => {
  try {
    dispatch(setIsLoading(true))
    const url = '/v1/eatta-service/invoice'
    const res = await apiclient.delete<ApiResponse<InvoiceEslip>>(url, {
      data: data,
    })
    dispatch(setInvoiceEslip(res.data.data))
    dispatch(
      setNotification({
        message: 'Invoice successfully updated',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

export const uploadBrokerInvoice = async (
  dispatch: Dispatch,
  payload: BrokerInvoicePayload,
  config: AxiosRequestConfig,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsUploadingBrokerInvoices(true))
    const encryptionPayload = new FormData()
    encryptionPayload.append('file', payload.file)
    const { error, encryptedFile } = await encryptFile(encryptionPayload)
    if (error || !encryptedFile) {
      dispatch(
        setNotification({
          message: 'Unable to encrypt the invoice file: ' + error,
          type: 'error',
        })
      )
      dispatch(setIsUploadingCatalogues(false))
      return
    }
    const file = new File([encryptedFile], `${payload.file.name}.pgp`, {
      type: 'application/octet-stream',
    })
    const formData = new FormData()
    formData.append('file', file)
    formData.append('saleNumber', payload.saleNumber)
    formData.append('auctionDate', payload.auctionDate)
    formData.append('promptDate', payload.promptDate)

    const res = await apiclient.postForm(
      '/v1/eatta-service/catalogue/broker-invoice',
      formData,
      config
    )

    dispatch(
      setNotification({
        message: res.data.data,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsUploadingBrokerInvoices(false))
  }
}

export const getBrokerInvoices = async (
  dispatch: Dispatch,
  filters: InvoicesFilters,
  isBackOffice: boolean
) => {
  try {
    dispatch(setIsLoading(true))
    const searchParams = mapObjectToUrlParams(filters)
    const url = isBackOffice
      ? `/backoffice-bff/eatta-service/catalogue/query/broker-invoice?${searchParams}`
      : `/v1/eatta-service/catalogue/query/broker-invoice?${searchParams}`
    const response = await apiclient.get<BrokerInvoicesResponse>(url)
    dispatch(setBrokerInvoicesResponse(response.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

export const getBrokerInvoiceEntries = async (
  dispatch: Dispatch,
  filters: InvoicesEntryFilters
) => {
  try {
    dispatch(setIsLoading(true))
    const searchParams = mapObjectToUrlParams(filters)

    const url = `/v1/eatta-service/catalogue/query/broker-invoice-entry?${searchParams}`
    const response = await apiclient.get<BrokerInvoicesEntryResponse>(url)

    dispatch(setBrokerInvoiceEntryResponse(response.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

export const getInvoiceById = async (
  dispatch: Dispatch,
  brokerInvoiceId: string
) => {
  try {
    dispatch(setIsLoading(true))
    const url = `/v1/eatta-service/catalogue/broker-invoice/${brokerInvoiceId}`
    const response = await apiclient.get<ApiResponse<BrokerInvoice>>(url)
    dispatch(setBrokerInvoiceResponse(response.data.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

export const getTransactionHistory = async (
  dispatch: Dispatch,
  filters: PageFilters,
  brokerInvoiceId?: string
) => {
  try {
    const searchParams = mapObjectToUrlParams(filters)

    dispatch(setIsLoading(true))

    let url: string

    if (brokerInvoiceId) {
      url = `/v1/eatta-service/wallet/transaction/${brokerInvoiceId}/?${searchParams}`
    } else {
      url = `/v1/eatta-service/wallet/transaction/?${searchParams}`
    }

    const response = await apiclient.get<TransactionHistoryResponse>(url)
    dispatch(setTransactionHistoryResponse(response.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

//dispatch invoices
export const dispatchInvoices = async (
  dispatch: Dispatch,
  invoiceIds: string[],
  onSuccess: () => void
) => {
  try {
    dispatch(setIsDispatchingInvoices(true))

    const url = `/v1/eatta-service/catalogue/broker-invoice/dispatch`

    const response = await apiclient.post(url, {
      invoiceIds,
    })

    dispatch(
      setNotification({
        message: response.data.message,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsDispatchingInvoices(false))
  }
}

export const getProducerInvoiceEntries = async (
  dispatch: Dispatch,
  filters: ProducerInvoiceEntryFilters
) => {
  try {
    dispatch(setIsLoading(true))
    const searchParams = mapObjectToUrlParams(filters)

    const url = `/v1/eatta-service/catalogue/query/producer-broker-invoice-entry?${searchParams}`
    const response = await apiclient.get<ProducerInvoiceEntryResponse>(url)

    dispatch(setProducerInvoiceEntryResponse(response.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

export const getDisbursementsEntries = async (
  dispatch: Dispatch,
  filters: ProducerInvoiceEntryFilters
) => {
  try {
    dispatch(setIsLoading(true))
    const searchParams = mapObjectToUrlParams(filters)

    const url = `/v1/eatta-service/payment-detail/disbursements?${searchParams}`
    const response = await apiclient.get<DisbursementsEntryResponse>(url)

    dispatch(setDisbursementsEntryResponse(response.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

export const getProducerAccountSales =
  (filters: AccountSalesFilter) => async (dispatch: Dispatch) => {
    try {
      dispatch(setIsLoadingAccountSalesInfo(true))
      const searchParams = mapObjectToUrlParams(filters)
      const url = `/v1/eatta-service/catalogue/query/producer-account-sales?${searchParams}`

      const res = await apiclient.get(url)
      dispatch(setAccountSalesEntryData(res.data.data))
    } catch (e) {
      const message = (e as Error).message
      dispatch(
        setNotification({
          message,
          type: 'error',
        })
      )
    } finally {
      dispatch(setIsLoadingAccountSalesInfo(false))
    }
  }

//Delivery orders
export const getDeliveryOrders = async (
  dispatch: Dispatch,
  filters: DeliveryOrderFilters
) => {
  try {
    dispatch(setIsLoading(true))

    const searchParams = mapObjectToUrlParams({ ...filters, ascending: false })

    const res = await apiclient.get<PaginatedResponse<DeliveryOrderResponse>>(
      `/v1/eatta-service/electronic-delivery-order?${searchParams}`
    )
    dispatch(setDeliveryOrdersResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

export const getDeliveryOrderById = async (
  dispatch: Dispatch,
  deliveryOrderId: string
) => {
  try {
    dispatch(setIsLoading(true))

    const res = await apiclient.get<ApiResponse<DeliveryOrderResponse>>(
      `/v1/eatta-service/electronic-delivery-order/${deliveryOrderId}`
    )
    dispatch(setSelectedDeliveryOrderEntries(res.data.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoading(false))
  }
}

export const signDeliveryOrder = async (
  dispatch: Dispatch,
  payload: SignDeliveryOrderPayload,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsSigningDeliveryOrder(true))

    const url = `/v1/eatta-service/electronic-delivery-order/sign`

    const response = await apiclient.post(url, payload)

    dispatch(
      setNotification({
        message: response.data.message,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsSigningDeliveryOrder(false))
  }
}

export const submitDeliveryOrder = async (
  dispatch: Dispatch,
  deliveryOrderId: string,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsSubmittingDeliveryOrder(true))

    const url = `/v1/eatta-service/electronic-delivery-order/submit/${deliveryOrderId}`

    const response = await apiclient.post(url)

    dispatch(
      setNotification({
        message: response.data.message,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsSubmittingDeliveryOrder(false))
  }
}

export const selectDeliveryOrderEntries = (
  dispatch: Dispatch,
  deliveryOrderId: string,
  deliveryOrders: DeliveryOrderResponse[]
) => {
  const selectedOrder = deliveryOrders.find(
    (order) => order.id === deliveryOrderId
  )
  if (selectedOrder && selectedOrder.entries) {
    dispatch(setSelectedDeliveryOrderEntries(selectedOrder))
  }
}

export const selectAuctionEntry = (
  dispatch: Dispatch,
  lotNumber: number,
  selectedDeliveryOrder: DeliveryOrderResponse | null
) => {
  if (selectedDeliveryOrder?.entries) {
    const entry = selectedDeliveryOrder.entries.find(
      (e) => e.lotNumber === lotNumber
    )
    if (entry?.auctionEntry) {
      dispatch(setSelectedAuctionEntry(entry.auctionEntry))
    }
  }
}
