import { I<PERSON><PERSON>utton, LinearProgress, Stack, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import {
  ExcelIcon,
  ImageIcon,
  PdfIcon,
  TrashIcon,
  UploadIcon,
} from '@dtbx/ui/icons'
import { hasDataInColumns } from '@/utils/excelColumnParser'
import * as XLSX from 'xlsx'
import { SheetConfig } from '@/store/interfaces'
import { downloadOnboardingDocuments } from '@/store/actions'
import { useAppDispatch } from '@/store'
import { DownloadPng } from '@/components/SvgIcons/DownloadPng'

export interface ExcelFileUploadProps {
  fileUrl?: string
  title?: string
  fileSize?: number
  mimeTypes?: string[]
  description?: string
  progress?: number
  disabled?: boolean
  required?: boolean
  sheetConfig?: SheetConfig
  onFileChange: (file: File | null) => void
}

const ExcelMimeTypes = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
]

export const FileUpload: React.FC<ExcelFileUploadProps> = ({
  fileUrl,
  mimeTypes = ExcelMimeTypes,
  description = 'Only Excel file allowed.',
  progress,
  disabled,
  required = true,
  sheetConfig,
  onFileChange,
}) => {
  const dispatch = useAppDispatch()
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null)
  const fileInputRef = React.useRef<HTMLInputElement>(null)
  const [isDragging, setIsDragging] = React.useState(false)
  const [dataValidationError, setDataValidationError] = useState<string | null>(
    null
  )

  const handleFileSelect = (file: File) => {
    const isValid = mimeTypes.includes(file.type)
    if (!isValid) {
      setDataValidationError('Invalid file type. Please upload valid file.')
      return
    }
    setDataValidationError(null)

    // Read the file and check if data exists in the sheet
    const isExel = mimeTypes.some((m) => ExcelMimeTypes.includes(m))
    if (isExel) {
      handleExelFiles(file)
      return
    }

    handleFileChange(file)
  }

  const handleExelFiles = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })

        const sheetName = sheetConfig?.sheetName || workbook.SheetNames[0]
        const columnRange = sheetConfig?.columnRange || 'A:E'

        const hasData = hasDataInColumns(workbook, sheetName, columnRange)

        if (!hasData) {
          setDataValidationError(
            'No data found in the Excel file. Please check your file and try again.'
          )
        }

        handleFileChange(file)
      } catch (e) {
        const message = (e as Error).message
        setDataValidationError(
          'Error reading Excel file. Please check the file format and try again..'
        )
        console.error(
          `Error reading Excel file. Please check the file format and try again.: ${message}`
        )
      }
    }

    reader.readAsArrayBuffer(file)
  }

  const handleFileChange = (file: File) => {
    setSelectedFile(file)
    onFileChange(file)
  }

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    if (disabled) return
    setIsDragging(false)
    const file = e.dataTransfer.files[0]
    if (file) handleFileSelect(file)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleClick = async () => {
    if (disabled) return
    fileInputRef.current?.click()
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return
    const file = e.target.files?.[0]
    if (file) handleFileSelect(file)
  }

  const handleDelete = async () => {
    setSelectedFile(null)
    setDataValidationError(null)
    onFileChange(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const getFilePreviewIcon = () => {
    if (mimeTypes.includes('application/pdf')) {
      return <PdfIcon />
    }
    if (mimeTypes.some((m) => m.includes('image/'))) {
      return <DownloadPng />
    }
    return <ExcelIcon />
  }

  const downloadDoc = async (url: string) => {
    const file = await downloadOnboardingDocuments(dispatch, url)
    if (!file) return
    handleFileChange(file)
  }

  useEffect(() => {
    if (!fileUrl) return
    downloadDoc(fileUrl)
  }, [fileUrl])

  return (
    <Stack spacing={3}>
      {/* Upload area - hide when file is selected */}
      {!selectedFile && (
        <Stack
          sx={{
            border: '1px dashed #EAECF0',
            borderRadius: '0.5rem',
            p: 1,
            alignItems: 'center',
            textAlign: 'center',
            position: 'relative',
            backgroundColor: isDragging ? 'rgba(0, 0, 0, 0.04)' : 'transparent',
            transition: 'all 0.2s ease-in-out',
            opacity: disabled ? 0.5 : 1,
            pointerEvents: disabled ? 'none' : 'auto',
            cursor: disabled ? 'not-allowed' : 'pointer',
            '&::after': isDragging
              ? {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(25, 118, 210, 0.08)',
                  borderRadius: '12px',
                  border: '2px dashed #1976d2',
                  zIndex: 1,
                }
              : {},
          }}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onClick={handleClick}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleInputChange}
            accept={mimeTypes.join(',')}
            aria-label="Upload Excel file"
            title="Upload Excel file"
            style={{ display: 'none' }}
            required={required}
          />
          <IconButton
            sx={{
              color: '#475467',
              mb: 3,
              border: '1px solid #EAECF0',
              borderRadius: '8px',
            }}
          >
            <UploadIcon />
          </IconButton>
          <Stack
            direction="row"
            spacing={1}
            sx={{ cursor: 'pointer', alignItems: 'center' }}
          >
            <Typography
              sx={{
                color: 'primary.main',
                fontWeight: 600,
              }}
            >
              Click to upload
            </Typography>
            <Typography sx={{ color: '#475467' }}>or drag and drop</Typography>
          </Stack>
          <Typography sx={{ color: '#475467', fontSize: '0.875rem' }}>
            {description}
          </Typography>
        </Stack>
      )}
      {selectedFile && (
        <>
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            sx={{
              mt: 3,
              p: 2,
              border: '1px solid #EAECF0',
              borderRadius: '8px',
            }}
          >
            <Stack direction="row" spacing={2} alignItems="center">
              <IconButton sx={{ color: '#475467' }}>
                {getFilePreviewIcon()}
              </IconButton>
              <Stack>
                <Typography sx={{ fontWeight: 500, color: '#344054' }}>
                  {selectedFile.name}
                </Typography>
                <Typography sx={{ color: '#475467', fontSize: '0.75rem' }}>
                  {Math.round(selectedFile.size / 1024)} KB
                </Typography>
              </Stack>
            </Stack>
            <IconButton
              disabled={disabled}
              sx={{ color: '#475467' }}
              onClick={handleDelete}
            >
              <TrashIcon />
            </IconButton>
          </Stack>

          {progress !== undefined && progress > 0 && (
            <Stack
              direction="row"
              spacing={2}
              alignItems="center"
              sx={{ mt: 2 }}
            >
              <LinearProgress
                variant="determinate"
                value={progress}
                sx={{ flexGrow: 1 }}
              />
              <Typography
                variant="body2"
                sx={{ color: '#475467', minWidth: 45 }}
              >
                {progress}%
              </Typography>
              {progress === 100 && (
                <CheckCircleIcon fontSize="small" sx={{ color: '#12B76A' }} />
              )}
            </Stack>
          )}
        </>
      )}
      {dataValidationError && (
        <Stack sx={{ mt: 2 }}>
          <Typography
            sx={{
              color: '#D92D20',
              fontSize: '0.875rem',
              textAlign: 'center',
            }}
          >
            {dataValidationError}
          </Typography>
        </Stack>
      )}
    </Stack>
  )
}
