import { CloseRounded } from '@mui/icons-material'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  IconButton,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  formatTimestamp,
  handleDiff,
} from '@dtbx/store/utils'
import { setDrawer } from '@dtbx/store/reducers'
import { IApprovalRequest } from '@/store/interfaces/makerChecker'

const ReviewRequest = ({
  request,
  closeParent,
}: {
  request: IApprovalRequest
  closeParent: () => void
}) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [open, setOpen] = useState<boolean>(false)
  const [commentsError, setCommentsError] = useState<boolean>(false)
  const [checkerComments, setCheckerComments] = useState<string>('')

  const handleSeeRequest = async () => {
    setOpen(!open)
  }

  const handleApprove = async () => {
    if (!checkerComments) {
      setCommentsError(true)
      return
    }
    setOpen(false)
    setCheckerComments('')
    dispatch(
      setDrawer({
        open: false,
        drawerChildren: null,
        header: '',
      })
    )
  }

  const handleReject = async () => {
    if (!checkerComments) {
      setCommentsError(true)
      return
    }

    setCheckerComments('')
    setOpen(false)
  }
  const handleClose = (e: object | {}, reason: string) => {
    if (reason && reason === 'backdropClick') return
    setOpen(false)
  }
  return (
    <>
      <MenuItem onClick={handleSeeRequest}>
        <Typography>See request summary</Typography>
      </MenuItem>
      <Drawer
        open={open}
        variant="persistent"
        hideBackdrop={false}
        sx={{
          '.MuiDrawer-paper': {
            width: '30%',
          },
        }}
        anchor={'right'}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: '30%',
          },
        }}
      >
        <Stack
          sx={{
            flexDirection: 'column',
            gap: '9px',
          }}
        >
          <Stack
            sx={{
              justifyContent: 'space-between',
              alignItems: 'center',
              flexDirection: 'row',
              background: '#F9FAFB',
              borderBottom: '2px solid  #F2F4F7',
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                fontSize: '18px',
                px: '1vw',
              }}
            >
              Approval request details
            </Typography>
            <IconButton onClick={(e) => handleClose(e, 'close')}>
              <CloseRounded />
            </IconButton>
          </Stack>
          <Stack sx={{ px: '1vw', py: '1vh' }}>
            <Stack
              sx={{
                gap: '20px',
              }}
            >
              <TextField
                fullWidth
                label="Approval request type"
                sx={{}}
                value={
                  request?.makerCheckerType.type
                    ? sentenceCase(request?.makerCheckerType.type)
                    : ''
                }
              />
              <TextField
                fullWidth
                label="Changes made"
                sx={{
                  width: 'auto',
                }}
                multiline
                value={handleDiff(request.diff)}
              />
              <TextField
                fullWidth
                label="Maker"
                sx={{}}
                value={request && request.maker}
              />
              <TextField
                fullWidth
                label="Maker timestamp"
                sx={{}}
                value={request && formatTimestamp(request.dateCreated)}
              />
              <TextField
                fullWidth
                label="Maker comment"
                sx={{}}
                value={
                  request && request.makerComments
                    ? request.makerComments
                    : 'No comment'
                }
              />
              <TextField
                fullWidth
                label="Checker Comments"
                onChange={(e) => {
                  setCheckerComments(e.target.value)
                  e.target.value.length > 0
                    ? setCommentsError(false)
                    : setCommentsError(true)
                }}
                helperText={commentsError ? 'Please enter comments' : ''}
                error={commentsError}
                value={checkerComments}
                multiline
                rows={3}
              />
            </Stack>
            <Stack
              sx={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                py: '2vh',
                gap: '10px',
              }}
            >
              <AccessControlWrapper
                rights={request?.makerCheckerType?.checkerPermissions?.filter(
                  (perm) => perm.includes('REJECT')
                )}
                makerId={request?.maker}
              >
                <Button
                  variant="outlined"
                  fullWidth
                  disabled={checkerComments.length === 0}
                  sx={{
                    maxHeight: '34px',
                    background: '#E3E4E4',
                    border: '1px solid #AAADB0',
                    boxShadow: ' 0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  }}
                  onClick={handleReject}
                >
                  Reject
                </Button>
              </AccessControlWrapper>

              <AccessControlWrapper
                rights={[
                  ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_CUSTOMERS,
                  ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_ROLES,
                  ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_ROLES,
                  ...ACCESS_CONTROLS.REJECT_APPROVALREQUEST_CUSTOMERS,
                  ...ACCESS_CONTROLS.REJECT_APPROVALREQUEST_ROLES,
                  ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_USERS,
                  ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_PROFILES,
                ]}
              >
                <Button
                  variant="contained"
                  sx={{
                    height: '34px',
                    textWrap: 'nowrap',
                  }}
                  fullWidth
                  onClick={handleApprove}
                  disabled={checkerComments.length === 0}
                >
                  Approve
                </Button>
              </AccessControlWrapper>
            </Stack>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}

export default ReviewRequest
