import { ExpandIcon } from '@/components/SvgIcons/ExpandIcon'
import { Stack, Typography, Box, Button } from '@mui/material'
import { useState } from 'react'
import TeaDetails from './TeaDetails'
import { useAppSelector, useAppDispatch } from '@/store'
import { DeliveryOrderEntry } from '@/store/interfaces/invoices'
import { selectAuctionEntry } from '@/store/actions/invoices'

export interface LotInfoCardProps {
  lotNumber?: string
  invoiceNo?: string
  buyer?: string
  pkgs?: string
  pkgType?: string
  grade?: string
  weight?: string
  amountInvoiced?: string
  amountReceived?: string
  showExpandButton?: boolean
}

interface LotInfoPair {
  label: string
  value: string
}

export interface LotInfoCardListProps {
  lots?: LotInfoCardProps[]
  showExpandButton?: boolean
}

export const mapDeliveryEntryToLotInfo = (entry: DeliveryOrderEntry): LotInfoCardProps => {
  return {
    lotNumber: entry.lotNumber?.toString() || '',
    invoiceNo: entry.auctionEntry?.invoiceNo || '',
    buyer: entry.auctionEntry?.buyer || entry.buyerName || '',
    pkgs: entry.bags || '',
    pkgType: 'PB', 
    grade: entry.grade || '',
    weight: entry.totalWeight?.toFixed(2) || '',
    amountInvoiced: entry.totalValue?.toFixed(2) || '',
    amountReceived: entry.netPayable?.toFixed(2) || '',
  }
}

export const LotInfoCard: React.FC<LotInfoCardProps> = ({
  lotNumber,
  invoiceNo,
  buyer,
  pkgs,
  pkgType,
  grade,
  weight,
  amountInvoiced,
  amountReceived,
  showExpandButton = true,
}) => {
  const dispatch = useAppDispatch()
  const { selectedDeliveryOrderEntries } = useAppSelector(
    (state) => state.invoices
  )

  const LotInfoItems: LotInfoPair[] = [
    { label: 'Pkgs', value: pkgs || '' },
    { label: 'Pkg Type', value: pkgType || '' },
    { label: 'Grade', value: grade || '' },
    { label: 'Sold Weight (Kgs)', value: weight || '' },
    { label: 'Amount Invoiced (USD)', value: amountInvoiced || '' },
    { label: 'Amount Received (USD)', value: amountReceived || '' },
  ]
  const [open, setOpen] = useState(false)

  const handleExpand = () => {
    setOpen(true)
    selectAuctionEntry(dispatch, Number(lotNumber), selectedDeliveryOrderEntries)
  }

  return (
    <Stack
      padding={3}
      sx={{
        bgcolor: '#FFFFFF',
        border: '1px solid #D0D5DD',
        borderRadius: 2,
        width: 'fit-content',
        mt: 2,
        mb: 2,
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        width="100%"
        justifyContent="space-between"
      >
        <Box display="flex" alignItems="flex-start">
          <Typography
            sx={{ fontSize: '1.125rem', fontWeight: 700, color: '#101828' }}
          >
            Lot Number: {lotNumber || ''}
          </Typography>
          <Typography
            sx={{ fontSize: '1.125rem', fontWeight: 700, color: '#101828' }}
          >
            GDN: {invoiceNo || ''}
          </Typography>
          <Typography
            sx={{
              fontSize: '1.125rem',
              fontWeight: 700,
              color: '#101828',
              ml: 2,
            }}
          >
            {buyer || ''}
          </Typography>
        </Box>
        {showExpandButton && (
          <Button
            variant="text"
            endIcon={<ExpandIcon stroke="#029327" />}
            onClick={handleExpand}
            sx={{
              color: '#029327',
              fontSize: '1rem',
              fontWeight: 600,
              '&:hover': {
                backgroundColor: 'transparent',
              },
            }}
          >
            Expand
          </Button>
        )}
      </Stack>
      <Stack
        direction={{ xs: 'column', md: 'column', lg: 'row' }}
        spacing={0.5}
        width="100%"
        sx={{ mt: 1 }}
      >
        {LotInfoItems.map((item) => (
          <Stack key={item.label} sx={{ width: '100%' }}>
            <Typography
              sx={{
                fontSize: '1rem',
                fontWeight: 500,
                color: '#101828',
                textWrap: 'nowrap',
              }}
            >
              {item.label}
            </Typography>
            <Typography
              sx={{
                fontSize: '1rem',
                fontWeight: 600,
                color: '#101828',
                textWrap: 'nowrap',
              }}
            >
              {item.value}
            </Typography>
          </Stack>
        ))}
      </Stack>
      <TeaDetails open={open} onClose={() => setOpen(false)} />
    </Stack>
  )
}

export default LotInfoCard

export const LotInfoCardList: React.FC<LotInfoCardListProps> = ({
  lots,
  showExpandButton = true,
}) => {
  const { selectedDeliveryOrderEntries } = useAppSelector(
    (state) => state.invoices
  )

  const lotInfoData =
    selectedDeliveryOrderEntries?.entries?.length && selectedDeliveryOrderEntries.entries.length > 0
      ? selectedDeliveryOrderEntries.entries.map(mapDeliveryEntryToLotInfo)
      : lots || []

  return (
    <Stack spacing={2} width="100%">
      {lotInfoData.map((lot) => (
        <LotInfoCard
          key={lot.lotNumber}
          {...lot}
          showExpandButton={showExpandButton}
        />
      ))}
    </Stack>
  )
}
