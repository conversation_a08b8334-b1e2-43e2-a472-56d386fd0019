'use client'
import {
  Autocomplete,
  Button,
  FormControl,
  FormHelperText,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import React, { useEffect, useState } from 'react'
import { Form, FormikProvider, useFormik } from 'formik'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import * as Yup from 'yup'
import { matchIsValidTel, MuiTelInput } from 'mui-tel-input'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  createOrganization,
  getBankBranches,
  makeCreateOrganization,
} from '@/store/actions'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { HasAccessToRights } from '@dtbx/store/utils'
import { LoadingButton } from '@dtbx/ui/components/Loading'

const CreateOrganizationPage = () => {
  const router = useCustomRouter()
  const { isLoadingCreateOrganization, bankBranches } = useAppSelector(
    (state) => state.loans
  )
  const dispatch = useAppDispatch()
  const [phone, setPhone] = useState<string>('')

  const physicalAddressValidation = Yup.object({
    country: Yup.string().required('Country must not be empty'),
    town: Yup.string().required('Town must not be empty'),
    physicalAddress: Yup.string().required(
      'Physical Address must not be empty'
    ),
  })
  const orgValidation = Yup.object({
    mobile: Yup.string()
      .required('Phone must not be empty')
      .test('is-valid-phone', 'Invalid phone number', (value) => {
        return matchIsValidTel(value || '')
      }),
    name: Yup.string()
      .matches(/^[A-Za-z\s]+$/, 'Only alphabets are allowed')
      .required('Name must not be empty'),
    email: Yup.string()
      .email('Invalid email')
      .required('Email must not be empty'),
    bankName: Yup.string().required('Bank Name must not be empty'),
    bankAccountNumber: Yup.string().required(
      'Bank Account Number must not be empty'
    ),
    swiftCode: Yup.string().required('Swift Code must not be empty'),
    branchCode: Yup.string().required('Branch Code must not be empty'),
    accountBranchName: Yup.string().required(
      'Account Branch Name must not be empty'
    ),
    physicalAddress: physicalAddressValidation,
    limit: Yup.number().required('Limit must not be empty'),
    limitCurrency: Yup.string().required('Limit Currency must not be empty'),
  })

  const formik = useFormik({
    initialValues: {
      name: '',
      mobile: '',
      cbsIdentifier: '',
      email: '',
      status: 'Active',
      bankName: 'Diamond Trust Bank',
      bankAccountNumber: '',
      swiftCode: 'DTBKEKENA',
      branchCode: '',
      bankCode: '063',
      accountBranchName: '',
      physicalAddress: {
        country: 'Kenya',
        town: '',
        physicalAddress: '',
      },
      limit: 0,
      limitCurrency: 'KES',
      comments: 'Organization creation',
    },
    validationSchema: orgValidation,
    onSubmit: async (values) => {
      if (HasAccessToRights(['SUPER_CREATE_INSURER'])) {
        await createOrganization(dispatch, values)
      } else {
        await makeCreateOrganization(dispatch, values)
      }
      router.push('/settings/organizations/')
    },
  })
  const handlePhoneChange = (value: string) => {
    setPhone(value)
    formik.setFieldTouched('mobile', true, false)
    if (!matchIsValidTel(value)) {
      formik.setFieldError('mobile', 'Invalid mobile number')
    }
    formik.setFieldValue('mobile', value.replace(/\s/g, ''))
  }
  const { handleSubmit, getFieldProps, touched, errors } = formik
  useEffect(() => {
    getBankBranches(dispatch)
  }, [])
  return (
    <Stack
      sx={{
        px: '2%',
        background: '#F7F7F7',
        minHeight: '92vh',
        py: '2%',
        flexDirection: 'column',
        gap: '3vh',
      }}
    >
      <IconButton
        sx={{
          background: '#FFFFFF',
          borderRadius: '8px',
          border: '1px solid #D0D5DD',
          width: '3vw',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        onClick={() => window.history.back()}
      >
        <ArrowBackIosNewOutlinedIcon />
      </IconButton>
      <Typography variant="subtitle1">Create Organization</Typography>
      <Typography variant="subtitle2">
        Please fill the form below to add a new organization
      </Typography>
      <Stack
        sx={{
          flexDirection: 'column',
          width: '80%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
        }}
      >
        <FormikProvider value={formik}>
          <Form noValidate onSubmit={handleSubmit}>
            <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
              <TextField
                autoComplete="name"
                type="text"
                label="Organization Name"
                margin={'normal'}
                {...getFieldProps('name')}
                fullWidth
                error={Boolean(touched.name && errors.name)}
                helperText={touched.name && errors.name}
              />
              <FormControl
                fullWidth
                sx={{
                  marginTop: '16px',
                }}
                error={Boolean(touched.mobile && errors.mobile)}
              >
                <MuiTelInput
                  value={phone}
                  name="mobile"
                  label="Mobile Number"
                  defaultCountry="KE"
                  onlyCountries={['KE', 'UG', 'TZ', 'BI']}
                  onChange={handlePhoneChange}
                />
                <FormHelperText>
                  {touched.mobile && errors.mobile}
                </FormHelperText>
              </FormControl>
            </Stack>
            <TextField
              autoComplete="email"
              type="email"
              label="Email Address"
              margin={'normal'}
              {...getFieldProps('email')}
              fullWidth
              error={Boolean(touched.email && errors.email)}
              helperText={touched.email && errors.email}
            />

            <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
              <TextField
                autoComplete="bankName"
                type="text"
                label="Bank Name"
                margin={'normal'}
                {...getFieldProps('bankName')}
                fullWidth
                inputProps={{ readOnly: true }}
                error={Boolean(touched.bankName && errors.bankName)}
                helperText={touched.bankName && errors.bankName}
              />
              <TextField
                autoComplete="bankAccountNumber"
                type="text"
                label="Bank Account Number"
                margin={'normal'}
                {...getFieldProps('bankAccountNumber')}
                fullWidth
                error={Boolean(
                  touched.bankAccountNumber && errors.bankAccountNumber
                )}
                helperText={
                  touched.bankAccountNumber && errors.bankAccountNumber
                }
              />
            </Stack>
            <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
              <TextField
                autoComplete="swiftCode"
                type="text"
                label="Swift Code"
                margin={'normal'}
                {...getFieldProps('swiftCode')}
                fullWidth
                inputProps={{ readOnly: true }}
                error={Boolean(touched.swiftCode && errors.swiftCode)}
                helperText={touched.swiftCode && errors.swiftCode}
              />
              <TextField
                autoComplete="cbsIdentifier"
                type="text"
                label="CBS Identifier"
                margin={'normal'}
                {...getFieldProps('cbsIdentifier')}
                fullWidth
                error={Boolean(touched.cbsIdentifier && errors.cbsIdentifier)}
                helperText={touched.cbsIdentifier && errors.cbsIdentifier}
              />
            </Stack>
            <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
              <FormControl fullWidth margin="normal">
                <Autocomplete
                  disablePortal
                  id="combo-box-demo"
                  options={bankBranches}
                  getOptionLabel={(option) => option.branchName}
                  value={bankBranches.find(
                    (branch) => formik.values.branchCode === branch.branchCode
                  )}
                  onChange={(event, newValue) => {
                    formik.setFieldValue(
                      'accountBranchName',
                      newValue?.branchName
                    )
                    formik.setFieldValue('branchCode', newValue?.branchCode)
                  }}
                  renderInput={(params) => (
                    <TextField {...params} label={'Select Branch Name'} />
                  )}
                />
              </FormControl>
              <TextField
                autoComplete="bankCode"
                type="text"
                label="Bank Code"
                margin={'normal'}
                {...getFieldProps('bankCode')}
                fullWidth
                inputProps={{ readOnly: true }}
                error={Boolean(touched.bankCode && errors.bankCode)}
                helperText={touched.bankCode && errors.bankCode}
              />
            </Stack>
            <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
              <TextField
                autoComplete="country"
                fullWidth
                type="text"
                label="Country"
                margin={'normal'}
                {...getFieldProps('physicalAddress.country')}
                error={Boolean(
                  touched.physicalAddress?.country &&
                    errors.physicalAddress?.country
                )}
                helperText={
                  touched.physicalAddress?.country &&
                  errors.physicalAddress?.country
                }
              />
              <TextField
                autoComplete="town"
                type="text"
                label="Town"
                margin={'normal'}
                fullWidth
                {...getFieldProps('physicalAddress.town')}
                error={Boolean(
                  touched.physicalAddress?.town && errors.physicalAddress?.town
                )}
                helperText={
                  touched.physicalAddress?.town && errors.physicalAddress?.town
                }
              />
            </Stack>
            <TextField
              autoComplete="physicalAddress"
              type="text"
              label="Physical Address"
              margin={'normal'}
              {...getFieldProps('physicalAddress.physicalAddress')}
              fullWidth
              error={Boolean(
                touched.physicalAddress?.physicalAddress &&
                  errors.physicalAddress?.physicalAddress
              )}
              helperText={
                touched.physicalAddress?.physicalAddress &&
                errors.physicalAddress?.physicalAddress
              }
            />
            <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
              <TextField
                autoComplete="limit"
                type="number"
                label="Organization Limit"
                margin={'normal'}
                {...getFieldProps('limit')}
                fullWidth
                error={Boolean(touched.limit && errors.limit)}
                helperText={touched.limit && errors.limit}
              />
              <TextField
                autoComplete="limitCurrency"
                type="text"
                label="Limit Currency"
                margin={'normal'}
                {...getFieldProps('limitCurrency')}
                fullWidth
                inputProps={{ readOnly: true }}
                error={Boolean(touched.limitCurrency && errors.limitCurrency)}
                helperText={touched.limitCurrency && errors.limitCurrency}
              />
            </Stack>
            {isLoadingCreateOrganization ? (
              <LoadingButton />
            ) : (
              <Button
                variant="contained"
                endIcon={<AddOutlinedIcon />}
                type={'submit'}
                disabled={Object.keys(errors).length > 0}
                fullWidth
                sx={{
                  my: '1%',
                }}
              >
                Create Organization
              </Button>
            )}
          </Form>
        </FormikProvider>
      </Stack>
    </Stack>
  )
}
export default CreateOrganizationPage
