import React, { useEffect, useState } from 'react'
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { ArrowForwardIos } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import { getPayments } from '@/store/actions/transactions'
import {
  Payment,
  PaymentFilters,
  PaymentStatus,
} from '@/store/interfaces/transactions'
import { InvoiceEslip, Order } from '@/store/interfaces'
import { sortData } from '@/utils/sortTableData'
import TableSkeleton from '@/components/TableSkeleton'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { SearchEslipModal } from '@/features/payments/allocate/SearchEslipModal'
import { AllocationModal } from '@/features/payments/allocate/AllocationModal'
import {
  AccessControlWrapper,
  formatCurrency,
  formatDate,
} from '@dtbx/store/utils'
import { MoreVert } from '@dtbx/ui/icons'
import {
  resetInvoiceEslips,
  setAllocateEslip,
  setSelectedPayments,
} from '@/store/reducers'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { TooltipTableCell } from '@/components/TooltipTableCell'
import { useResetPageOnFilterChange } from '@/hooks/useResetPageOnFilterChange'

export interface PaymentsTableProps {
  status: PaymentStatus
  filters: PaymentFilters
  canSelect: boolean
  onTransactionCountChange?: (count: number) => void
  onSetCurrentTab: (tabIndex: number) => void
}

export const PaymentsTable = ({
  status,
  filters,
  onTransactionCountChange,
  onSetCurrentTab,
}: PaymentsTableProps) => {
  const dispatch = useAppDispatch()

  const { payments, isLoading } = useAppSelector((state) => state.transactions)

  const [openSearchModal, setOpenSearchModal] = useState<boolean>(false)
  const [openAllocationModal, setOpenAllocationModal] = useState<boolean>(false)

  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<keyof Payment>('dateCreated')

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof Payment)
  }

  const sortKey = orderBy as keyof Payment
  const transactionsSorted = sortData([...payments.data], sortKey, order)

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }

  const handleEslipSelected = (eslip: InvoiceEslip) => {
    setOpenSearchModal(false)
    setOpenAllocationModal(true)
    dispatch(setAllocateEslip(eslip))
  }

  const handleOpenSearchModal = (transaction: Payment) => {
    dispatch(resetInvoiceEslips())
    dispatch(setSelectedPayments(transaction))
    setOpenSearchModal(true)
  }

  const handleAllocationModalClose = (tabIndex: number | undefined) => {
    setOpenAllocationModal(false)
    if (tabIndex !== undefined) onSetCurrentTab(tabIndex)
  }

  const isAllocated = status === 'ALLOCATED'

  const resetPageRef = useResetPageOnFilterChange(
    filters,
    paginationOptions,
    setPaginationOptions
  )

  useEffect(() => {
    if (resetPageRef.current) {
      resetPageRef.current = false
      return
    }
    const fetchTransactions = async () => {
      await getPayments(dispatch, {
        ...filters,
        status,
        page: paginationOptions.page,
        size: paginationOptions.size,
        ascending: false,
      })
    }
    fetchTransactions()
  }, [dispatch, filters, paginationOptions.page])

  useEffect(() => {
    onTransactionCountChange && onTransactionCountChange(payments.totalElements)
  }, [payments.totalElements])

  return isLoading ? (
    <TableSkeleton rowCount={15} columnCount={6} />
  ) : payments.data.length === 0 ? (
    <EmptyPage
      title="No transactions found"
      message="Transactions will appear here once they are processed."
      bgUrl={'/eatta/combo.svg'}
    />
  ) : (
    <>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
          '& .MuiTableCell-root': {
            paddingInline: '1.5rem',
            paddingBlock: '0.5rem',
            textAlign: 'left',
          },
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="transactions table"
          size="small"
        >
          <CustomTableHeader
            order={'asc'}
            orderBy={'id'}
            headLabel={[
              {
                id: 'eslipNumber',
                label: 'E-Slip Number',
                alignRight: false,
              },
              { id: 'buyerName', label: 'Buyer', alignRight: false },
              { id: 'sourceName', label: 'Source Info', alignRight: false },
              {
                id: 'coreReference',
                label: 'Corebanking Ref',
                alignRight: false,
              },
              { id: 'dateCreated', label: 'Date Received', alignRight: false },
              { id: 'amount', label: 'Amount Received', alignRight: false },
              { id: 'status', label: 'Status', alignRight: false },
              { label: '' },
            ]}
            showCheckbox={false}
            rowCount={payments.data.length}
            numSelected={0}
            onRequestSort={handleRequestSort}
          />
          <TableBody>
            {transactionsSorted.map((row) => {
              const {
                id,
                buyerName,
                sourceName,
                sourceAddress,
                amount,
                coreReference,
                status,
                dateCreated,
                eslipNumber,
                description,
              } = row
              return (
                <TableRow hover key={id} tabIndex={-1} role="checkbox">
                  <TableCell>
                    {isAllocated ? (
                      eslipNumber
                    ) : (
                      <TooltipTableCell
                        error={description}
                        value={eslipNumber}
                      />
                    )}
                  </TableCell>
                  <TableCell>{buyerName || 'N/A'}</TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {sourceName || 'N/A'}
                    </Typography>
                    <Typography variant="body2">{sourceAddress}</Typography>
                  </TableCell>
                  <TableCell>{coreReference}</TableCell>
                  <TableCell>{formatDate(dateCreated ?? '')}</TableCell>
                  <TableCell>
                    {formatCurrency(amount, 'USD', 'en-US')}
                  </TableCell>
                  <TableCell>
                    <StatusChip
                      sx={{ textTransform: 'capitalize' }}
                      status={isAllocated ? 'success' : 'error'}
                      label={status.toLowerCase()}
                    />
                  </TableCell>

                  <TableCell>
                    {isAllocated ? (
                      <IconButton>
                        <MoreVert />
                      </IconButton>
                    ) : (
                      <AccessControlWrapper
                        rights={ACCESS_CONTROLS.ALLOCATE_PAYMENT}
                      >
                        <IconButton onClick={() => handleOpenSearchModal(row)}>
                          <ArrowForwardIos />
                        </IconButton>
                      </AccessControlWrapper>
                    )}
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell
                align="center"
                colSpan={12}
                sx={{ paddingInline: 0, height: 40 }}
              >
                {payments.totalNumberOfPages > 0 && (
                  <CustomPagination
                    options={{
                      ...paginationOptions,
                      totalPages: payments.totalNumberOfPages,
                    }}
                    handlePagination={handlePagination}
                  />
                )}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
      {openSearchModal && (
        <SearchEslipModal
          open={openSearchModal}
          onClose={() => setOpenSearchModal(false)}
          onEslipSelected={handleEslipSelected}
          year={filters.year}
          saleDate={filters.saleDate}
        />
      )}
      {openAllocationModal && (
        <AllocationModal
          open={openAllocationModal}
          onClose={(tabIndex) => handleAllocationModalClose(tabIndex)}
          actionType={'MAKER'}
        />
      )}
    </>
  )
}
