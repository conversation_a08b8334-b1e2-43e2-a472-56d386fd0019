import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
  Divider,
} from '@mui/material'

import { CustomTableHeader } from '@dtbx/ui/components/Table'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { CHIP_COLORS } from '@/utils/statusChips'
import { useAppSelector } from '@/store'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { BrokerInvoiceEntry } from '@/store/interfaces'
import AccountSalesSkeleton from './AccountSalesSkeleton'
import { GradeSummary } from '@/features/account-sales/GradeSummary'

export type AccountSalesTableProps = {
  factory?: string
  broker?: string
}

export const AccountSalesTable: React.FC<AccountSalesTableProps> = ({
  broker,
  factory,
}) => {
  const { isLoadingAccountSalesInfo, accountSalesEntryResponse } =
    useAppSelector((state) => state.invoices)

  return (
    <Stack sx={{ height: '100%', overflow: 'auto' }}>
      {isLoadingAccountSalesInfo ? (
        <AccountSalesSkeleton />
      ) : accountSalesEntryResponse.brokerInvoiceEntries.length === 0 ? (
        <EmptyPage
          title="No records found"
          message="No account sales found. Please reach out to your broker to confirm they have uploaded the latest catalogue or contact support on ********** for further assistance."
          bgUrl={'/combo.svg'}
        />
      ) : (
        <>
          <Stack
            direction="column"
            spacing={1}
            sx={{
              backgroundColor: '#FFFFFF',
              paddingInline: '1.5rem',
              paddingBlock: '1rem',
            }}
          >
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: '#101828',
                fontSize: '1.125rem',
                lineHeight: '1.75rem',
                textAlign: 'left',
              }}
            >
              {broker}
              <StatusChip
                label={`${accountSalesEntryResponse.brokerInvoiceEntries.length} Lots`}
                status="success"
                sx={{
                  ml: 1,
                  backgroundColor: CHIP_COLORS.success.bg,
                  color: CHIP_COLORS.success.label,
                  border: `0.5px solid ${CHIP_COLORS.success.border}`,
                  borderRadius: CHIP_COLORS.success.borderRadius,
                  fontSize: CHIP_COLORS.success.fontSize,
                  fontWeight: CHIP_COLORS.success.fontWeight,
                }}
              />
            </Typography>

            <Typography
              variant="subtitle2"
              sx={{ fontWeight: 400, color: '#475467', fontSize: '0.875rem' }}
            >
              Mark: {factory}
            </Typography>
          </Stack>

          <TableContainer
            component={Paper}
            sx={{
              boxShadow: 'none',
              '& .MuiTableCell-root': {
                paddingInline: '1.5rem',
                paddingBlock: '0.5rem',
                textAlign: 'left',
              },
            }}
          >
            <Table
              sx={{ minWidth: 650 }}
              aria-label="account sales table"
              size="small"
            >
              <CustomTableHeader
                order="asc"
                orderBy=""
                headLabel={[
                  { id: 'lotNo', label: 'Lot' },
                  { id: 'invoiceNo', label: 'Garden Invoice' },
                  { id: 'buyer', label: 'Buyer Name' },
                  { id: 'pkgs', label: 'Pkgs' },
                  { id: 'type', label: 'Pkgs type' },
                  { id: 'grade', label: 'Grade' },
                  { id: 'wareHouse', label: 'Warehouse' },
                  { id: 'soldWeight', label: 'Sold Weight (Kgs)' },
                  { id: 'purchasePrice', label: 'Purchase Price ($)' },
                  { id: 'value', label: 'Value ($)' },
                ]}
                showCheckbox={false}
                rowCount={accountSalesEntryResponse.brokerInvoiceEntries.length}
                numSelected={0}
                onRequestSort={() => {}}
              />

              <TableBody>
                {accountSalesEntryResponse.brokerInvoiceEntries.map(
                  (row: BrokerInvoiceEntry) => (
                    <TableRow key={row.id} hover tabIndex={-1}>
                      <TableCell>{row.lotNo}</TableCell>
                      <TableCell>{row.gardenInvoice}</TableCell>
                      <TableCell>{row.buyer}</TableCell>
                      <TableCell>{row.pkgs}</TableCell>
                      <TableCell>{row.type}</TableCell>
                      <TableCell>{row.grade}</TableCell>
                      <TableCell>{row.warehouse}</TableCell>
                      <TableCell>{row.totalWeight}</TableCell>
                      <TableCell>
                        ${new Intl.NumberFormat('en-US').format(row.pricePerKg)}
                      </TableCell>
                      <TableCell>
                        ${new Intl.NumberFormat('en-US').format(row.totalValue)}
                      </TableCell>
                    </TableRow>
                  )
                )}
              </TableBody>
              <TableFooter>
                <TableRow>
                  <TableCell colSpan={9}>
                    <Stack
                      direction="row"
                      spacing={4}
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        gap: 8,
                        mt: 2,
                      }}
                    >
                      <GradeSummary
                        title="MAIN GRADES"
                        data={accountSalesEntryResponse.mainGrades}
                      />
                      <GradeSummary
                        title="SECONDARY GRADES"
                        data={accountSalesEntryResponse.secondaryGrades}
                      />
                      <GradeSummary
                        title="ALL TEAS"
                        data={accountSalesEntryResponse.allGrades}
                      />
                    </Stack>
                  </TableCell>
                </TableRow>
              </TableFooter>
            </Table>
          </TableContainer>
        </>
      )}
    </Stack>
  )
}
