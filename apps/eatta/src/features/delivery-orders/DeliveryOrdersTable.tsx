'use client'
import React, { useEffect, useState } from 'react'
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  getDeliveryOrders,
  selectDeliveryOrderEntries,
} from '@/store/actions/invoices'
import {
  DeliveryOrderFilters,
  DeliveryOrderResponse,
  DeliveryOrderStatus,
  Order,
} from '@/store/interfaces'
import TableSkeleton from '@/components/TableSkeleton'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import {
  DELIVERY_ORDER_STATUS_COLOR,
} from '@/utils/statusChips'
import { ArrowForwardIos } from '@mui/icons-material'
import { useRouter } from 'next/navigation'
import { useResetPageOnFilterChange } from '@/hooks/useResetPageOnFilterChange'

export interface DeliveryOrdersTableProps {
  status: string
  filters: DeliveryOrderFilters
  canSelect: boolean
  onDeliveryOrderCountChange?: (count: number) => void
  onSetCurrentTab: (tabIndex: number) => void
}

export const DeliveryOrdersTable = ({
  status,
  filters,
  onDeliveryOrderCountChange,
  onSetCurrentTab,
}: DeliveryOrdersTableProps) => {
  const dispatch = useAppDispatch()
  const router = useRouter()
  const { deliveryOrdersResponse, isLoading } = useAppSelector(
    (state) => state.invoices
  )

  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] =
    useState<keyof DeliveryOrderResponse>('docNumber')

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }
  /*************************end pagination handlers***************************/

  const resetPageRef = useResetPageOnFilterChange(
    filters,
    paginationOptions,
    setPaginationOptions
  )

  useEffect(() => {
    if (resetPageRef.current) {
      resetPageRef.current = false
      return
    }
    const fetchOrders = async () => {
      await getDeliveryOrders(dispatch, {
        ...filters,
        status,
        page: paginationOptions.page,
        size: paginationOptions.size,
        ascending: false,
      })
    }
    fetchOrders()
  }, [dispatch, filters, paginationOptions])

  useEffect(() => {
    onDeliveryOrderCountChange &&
      onDeliveryOrderCountChange(deliveryOrdersResponse.totalElements)
  }, [deliveryOrdersResponse.totalElements])

  const handleViewDeliveryOrder = (orderId: string, status: string) => {
    if (status === 'PENDING_APPROVAL') {
      router.push(`/delivery-orders/${orderId}?step=checker`)
    } else {
      router.push(`/delivery-orders/${orderId}`)
    }
    selectDeliveryOrderEntries(dispatch, orderId, deliveryOrdersResponse.data)
  }

  return isLoading ? (
    <TableSkeleton rowCount={6} columnCount={8} />
  ) : deliveryOrdersResponse.data.length === 0 ? (
    <>
      <EmptyPage
        title="No Delivery Orders found"
        message="Delivery Orders will appear here"
        bgUrl={'/combo.svg'}
      />
    </>
  ) : (
    <>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
          '& .MuiTableCell-root': {
            paddingInline: '1.5rem',
            paddingBlock: '0.5rem',
            textAlign: 'left',
          },
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="transactions table"
          size="small"
        >
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={[
              {
                id: 'docNumber',
                label: 'Document Number',
                alignRight: false,
              },
              { id: 'trdDocNumber', label: 'TRD Number', alignRight: false },
              {
                id: 'brokerInvoice',
                label: 'Broker Invoice',
                alignRight: false,
              },
              {
                id: 'lotCount',
                label: 'No. of Fully Paid Lots',
                alignRight: false,
              },
              { id: 'buyerCode', label: 'Buyer', alignRight: false },
              { id: 'warehouse', label: 'Warehouse', alignRight: false },
              {
                id: 'soldWeight',
                label: 'Total Sold Weight (Kgs)',
                alignRight: false,
              },
              { id: 'status', label: 'Status', alignRight: false },
              { label: '', alignRight: false },
            ]}
            showCheckbox={false}
            rowCount={0}
            numSelected={0}
            onRequestSort={() => {}}
          />
          <TableBody>
            {deliveryOrdersResponse.data.map((row) => {
              const {
                docNumber,
                trdDocNumber,
                brokerInvoice,
                buyerCode,
                warehouse,
                soldWeight,
                lotCount,
                status,
              } = row
              return (
                <TableRow hover key={docNumber} tabIndex={-1} role="checkbox">
                  <TableCell>{docNumber}</TableCell>
                  <TableCell>{trdDocNumber || '_'}</TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {brokerInvoice || '_'}
                    </Typography>
                  </TableCell>
                  <TableCell>{lotCount}</TableCell>
                  <TableCell>{buyerCode}</TableCell>
                  <TableCell>{warehouse}</TableCell>
                  <TableCell>{soldWeight}</TableCell>
                  <TableCell>
                    <StatusChip
                      label={status}
                      status={
                        DELIVERY_ORDER_STATUS_COLOR[
                          status as DeliveryOrderStatus
                        ]
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleViewDeliveryOrder(row.id, row.status)}>
                      <ArrowForwardIos />
                    </IconButton>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell
                align="center"
                colSpan={12}
                sx={{ paddingInline: 0, height: 40 }}
              >
                {deliveryOrdersResponse.totalNumberOfPages > 0 && (
                  <CustomPagination
                    options={{
                      ...paginationOptions,
                      totalPages: deliveryOrdersResponse.totalNumberOfPages,
                    }}
                    handlePagination={handlePagination}
                  />
                )}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
    </>
  )
}
