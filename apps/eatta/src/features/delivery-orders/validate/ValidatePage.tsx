/**
 * <AUTHOR> on 02/07/2025
 */
'use client'
import { Stack, Typography } from '@mui/material'
import { Logo } from '@/components/SvgIcons/Logo'
import {
  DeliveryOrderResponse,
  ValidateEDODetails,
} from '@/store/interfaces/Edo'
import { FC } from 'react'
import { formatDate, formatTimeOnly, formatTimestamp } from '@dtbx/store/utils'

interface ValidatePageProps {
  edo: ValidateEDODetails
}
export const ValidatePage: FC<ValidatePageProps> = ({ edo }) => {
  const { docNumber, signedBy, brokerName, warehouseName, buyer, signingDate } =
    edo
  return (
    <Stack justifyContent="center" alignItems="center" spacing={3} flexGrow={1}>
      <Stack alignItems="center" spacing={2}>
        <Logo />
        <Typography fontWeight="bold">Delivery Order No.</Typography>
        <Typography>{docNumber}</Typography>
      </Stack>

      <Stack
        padding={2}
        spacing={1}
        sx={{
          width: '100%',
          border: '1px solid #D0D5DD',
          borderRadius: '6px',
        }}
      >
        <Typography>Approved & Dispatched by</Typography>
        <Typography fontWeight={'bold'}>{signedBy}</Typography>
        <Typography>{brokerName}</Typography>
      </Stack>
      <Stack
        padding={2}
        spacing={2}
        sx={{
          width: '100%',
          border: '1px solid #D0D5DD',
          borderRadius: '6px',
        }}
      >
        <Stack spacing={0.5}>
          <Typography>Warehouse</Typography>
          <Typography fontWeight="bold">{warehouseName}</Typography>
        </Stack>

        <Stack spacing={0.5}>
          <Typography>Buyer</Typography>
          <Typography fontWeight="bold">{buyer}</Typography>
        </Stack>

        <Stack spacing={0.5}>
          <Typography>Date Dispatched</Typography>
          <Typography fontWeight="bold">
            {`${formatDate(signingDate)} ${formatTimeOnly(signingDate)}`}
          </Typography>
        </Stack>
      </Stack>
    </Stack>
  )
}
