import React from 'react'

export interface IHeadCell {
  id?: string
  label?: string
  alignRight?: boolean
  alignCenter?: boolean
}

export interface ISidebarConfigItem {
  path: string
  title: string
  id: string
  icon: React.JSX.Element
  app: string
  isProductionReady?: boolean
  requiredRights: string[]
}

export interface IFilterOption {
  key: string
  value: string
  label: string
}
export type FilterType =
  | 'select'
  | 'dropdown/checkbox'
  | 'dropdown/single'
  | 'date'
export interface IFilter {
  filterName: string
  options: IFilterOption[]
  type: FilterType
}

export interface ILandingApps {
  name: string
  key: string
  channel: string
  icon?: React.JSX.Element
  url: string
  isProductionReady: boolean
  modules?: string[]
}
export interface ITableData {
  id: string
  event: string
  eventSource?: string
  eventDate: string
  maker?: string
  makerTimestamp?: string
  checker?: string
  checkerTimestamp?: string
}

export interface IRole {
  id: string
  name: string
  description: string
  creationDate: string
  custom: boolean
  status?: string
  createdBy?: string
  permissions: IPermission[]
  permissionsGroup: IPermissionGroup[]
  [key: string]:
    | string
    | boolean
    | IPermission[]
    | IPermissionGroup[]
    | undefined
}

export interface IPermission {
  id: string
  name: string
  description: string
  groupName: string
  visible: boolean
  module:
    | string
    | {
        id: string
        description: string
        moduleName: string
        approvalRequest: string | null
        dateCreated: string | null
        dateModified: string | null
        updatedBy: string | null
      }
}

export interface IPermissionGroup {
  id: string
  name: string
  description: string
  permissions: IPermission[]
}

export interface IUser {
  id: string
  firstName: string
  lastName: string
  middleName: string
  roles: IRole[]
  email: string
  phoneNumber: string
  dateCreated?: string
  status: string
  lastLoginDate?: string
  [key: string]: string | number | boolean | IRole[] | undefined
}
export type ClientType =
  | 'Buyer'
  | 'Broker'
  | 'Producer'
  | 'Partner'
  | 'Warehouse'

export interface IDecodeToken {
  username?: string
  last_name: string
  first_name: string
  user_id: string
  authorities: string[]
  sub: string
  name?: string
  clientName?: string
  aud?: string
  iat: number
  exp: number
  ext_sys_id?: string
  resources?: IResource[]
  clientType?: ClientType
}

export interface IResource {
  resourceType: string
  resourceIds: string[]
}

export interface IChannelModule {
  channel: string
  modules: string[]
}
