import { Box, Divider } from '@mui/material'

import { Stack, Typography } from '@mui/material'

import { useCustomRouter } from '@dtbx/ui/hooks'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { DateIcon } from '@dtbx/ui/components/SvgIcons'
import { DateRangePicker } from '@dtbx/ui/components/DropDownMenus'
import { GrowProvider } from '@dtbx/ui/components/Transitions'
import { SearchRounded } from '@mui/icons-material'
import { useState, type JSX } from 'react'
import dayjs from 'dayjs'

export interface PageHeaderProps {
  children: React.ReactNode
}

export function PageHeader({ children }: PageHeaderProps): JSX.Element {
  const router = useCustomRouter()
  const [filters, setFilters] = useState<{
    status?: string
    dateCreatedFrom?: string
    dateCreatedTo?: string
  }>({})
  const [openFilterBar, setOpenFilterBar] = useState(false)

  return (
    <Stack sx={{ height: '100%' }}>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            Transaction History
          </Typography>
        </Stack>
      </Stack>
      <Divider />
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
        }}
        direction="row"
        spacing={2}
        paddingInline={3}
        paddingBlock={2}
        alignItems="center"
        justifyContent="flex-start"
      >
        <CustomSearchInput
          placeholder="Search Transaction ref"
          startAdornment={
            <SearchRounded
              sx={{
                color: 'gray',
              }}
            />
          }
          sx={{
            width: '300px',
            background: '#FFFFFF',
          }}
        />
        <CustomSearchInput
          placeholder="Search Transaction type"
          endAdornment={
            <SearchRounded
              sx={{
                color: 'gray',
              }}
            />
          }
          sx={{
            width: '220px',
            background: '#FFFFFF',
          }}
        />
        <CustomSearchInput
          placeholder="Filter by Sale Date"
          value={
            filters.dateCreatedFrom && filters.dateCreatedTo
              ? `${filters.dateCreatedFrom} - ${filters.dateCreatedTo}`
              : ''
          }
          bgColor={
            filters.dateCreatedFrom && filters.dateCreatedTo ? '#26b43b' : ''
          }
          readOnly
          onClick={() => {
            setOpenFilterBar(!openFilterBar)
          }}
          endAdornment={<DateIcon />}
          sx={{
            width: '220px',
          }}
        />
      </Stack>
      <Stack
        sx={{ backgroundColor: '#FFFFFF' }}
        direction="row"
        spacing={2}
        paddingInline={3}
        alignItems="center"
      >
        {openFilterBar && (
          <GrowProvider in={openFilterBar}>
            <Box
              paddingBottom={2}
              sx={{
                backgroundColor: '#FFFFFF',
                display: 'flex',
                gap: '8px',
              }}
            >
              <DateRangePicker
                onApplyDateRange={(newDate: {
                  start: dayjs.Dayjs
                  end: dayjs.Dayjs
                }) => {
                  setFilters({
                    ...filters,
                    dateCreatedFrom: newDate.start.format('YYYY-MM-DD'),
                    dateCreatedTo: newDate.end.format('YYYY-MM-DD'),
                  })
                  setOpenFilterBar(false)
                }}
                buttonText="Sale Date"
              />
            </Box>
          </GrowProvider>
        )}
      </Stack>
      {children}
    </Stack>
  )
}
