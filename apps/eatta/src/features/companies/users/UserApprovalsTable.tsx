/**
 * <AUTHOR> on 30/05/2025
 */
import {
  CompanyFilters,
  CompanyUser,
  CompanyUserStatus,
  Order,
} from '@/store/interfaces'
import React, { useEffect, useState } from 'react'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch, useAppSelector } from '@/store'
import { sortData } from '@/utils/sortTableData'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { getUsersApprovalRequests } from '@/store/actions'
import TableSkeleton from '@/components/TableSkeleton'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import {
  Button,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { AccessControlWrapper, formatDate } from '@dtbx/store/utils'
import { ArrowForwardIos } from '@mui/icons-material'
import { IApprovalRequest } from '@/store/interfaces/makerChecker'
import { ACCESS_CONTROLS, EATTA_MODULES } from '@/utils/constants'
import { safeJsonParse } from '@/utils/objectUtil'
import { APPROVAL_STATUS_MAP } from '@/utils/statusChips'
import { resetOnboardingState, setOnboardingUser } from '@/store/reducers'

interface UserApprovalsTableProps {
  status: CompanyUserStatus
  filters: CompanyFilters
  onApprovalsCountChange?: (count: number) => void
}

const UserApprovalsTable: React.FC<UserApprovalsTableProps> = ({
  status,
  filters,
  onApprovalsCountChange,
}) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { selectedCompany, userApprovalRequests, isLoading } = useAppSelector(
    (state) => state.companies
  )

  const isPendingApproval = status === 'PENDING'

  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<keyof IApprovalRequest>('dateCreated')

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof IApprovalRequest)
  }

  const sortKey = orderBy as keyof IApprovalRequest
  const userDataSorted = sortData(
    [...userApprovalRequests.data],
    sortKey,
    order
  )

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
    totalPages: userApprovalRequests.totalNumberOfPages,
  })
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
  }

  const handleCreateUser = () => {
    dispatch(resetOnboardingState())
    router.push(`/backoffice/companies/${selectedCompany?.id}/users/create`)
  }

  const handleNextStage = (approvalRequest: IApprovalRequest) => {
    dispatch(setOnboardingUser(approvalRequest))
    const path = isPendingApproval
      ? '/backoffice/members/approve'
      : `/backoffice/companies/${selectedCompany?.id}/users/create`
    router.push(path)
  }

  useEffect(() => {
    if (!selectedCompany) return
    const fetchApprovals = async () => {
      await getUsersApprovalRequests(dispatch, {
        status: status,
        module: EATTA_MODULES.users,
        page: paginationOptions.page,
        size: paginationOptions.size,
        channel: 'EATTA',
        organizationCode: selectedCompany.code,
      })
    }
    fetchApprovals()
  }, [dispatch, filters, paginationOptions, selectedCompany, status])

  useEffect(() => {
    onApprovalsCountChange &&
      onApprovalsCountChange(userApprovalRequests.totalElements)
  }, [userApprovalRequests])

  return isLoading ? (
    <TableSkeleton rowCount={15} columnCount={6} />
  ) : userApprovalRequests.data.length === 0 ? (
    <EmptyPage
      title="No members found"
      message="Please create a new member to get started"
      bgUrl={'/eatta/combo.svg'}
      action={
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USER}>
          <Button
            variant="contained"
            type="submit"
            startIcon={<AddIcon />}
            onClick={handleCreateUser}
          >
            New User
          </Button>
        </AccessControlWrapper>
      }
    />
  ) : (
    <TableContainer
      component={Paper}
      sx={{
        boxShadow: 'none',
        '& .MuiTableCell-root': {
          paddingInline: '1.5rem',
          paddingBlock: '0.5rem',
          textAlign: 'left',
        },
      }}
    >
      <Table sx={{ minWidth: 650 }} aria-label="company table" size="small">
        <CustomTableHeader
          order={order}
          orderBy={orderBy}
          headLabel={[
            {
              id: 'nationalId',
              label: 'User ID',
              alignRight: false,
            },
            { id: 'name', label: 'Name/Contact', alignRight: false },
            { id: 'phone', label: 'Phone number', alignRight: false },
            {
              id: 'maker',
              label: 'Maker',
              alignRight: false,
            },
            {
              id: 'dateCreated',
              label: 'Date Created',
              alignRight: false,
            },
            {
              id: 'status',
              label: 'Status',
              alignRight: false,
            },
            { id: '', label: '', alignRight: false },
          ]}
          showCheckbox={false}
          rowCount={userApprovalRequests.data.length}
          numSelected={0}
          onRequestSort={handleRequestSort}
        />
        <TableBody>
          {userDataSorted.map((approvalRequest) => {
            const { id, entity, maker, dateCreated, status } = approvalRequest
            const user = safeJsonParse<CompanyUser>(entity)
            if (!user) return null
            const { firstName, lastName, email, phoneNumber, nationalId } = user

            return (
              <TableRow hover key={id} tabIndex={-1} role="checkbox">
                <TableCell component="th" scope="row" id={id}>
                  {nationalId}
                </TableCell>
                <TableCell>
                  <Stack>
                    <Typography variant="label1">
                      {firstName} {lastName}
                    </Typography>
                    <Typography variant="body2">{email}</Typography>
                  </Stack>
                </TableCell>
                <TableCell>{phoneNumber}</TableCell>
                <TableCell>{maker}</TableCell>
                <TableCell>{formatDate(dateCreated)}</TableCell>
                <TableCell>
                  <StatusChip label={APPROVAL_STATUS_MAP[status]} />
                </TableCell>
                <TableCell>
                  <AccessControlWrapper
                    rights={
                      isPendingApproval
                        ? ACCESS_CONTROLS.ACCEPT_CREATE_USER
                        : ACCESS_CONTROLS.CREATE_USER
                    }
                    isMake={!isPendingApproval}
                    makerId={maker}
                  >
                    <IconButton
                      onClick={() => handleNextStage(approvalRequest)}
                    >
                      <ArrowForwardIos />
                    </IconButton>
                  </AccessControlWrapper>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell
              sx={{ paddingInline: 0 }}
              align="center"
              height={40}
              colSpan={12}
            >
              {userApprovalRequests.totalNumberOfPages > 0 && (
                <CustomPagination
                  options={{
                    ...paginationOptions,
                    totalPages: userApprovalRequests.totalNumberOfPages,
                  }}
                  handlePagination={handlePagination}
                />
              )}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  )
}

export default UserApprovalsTable
