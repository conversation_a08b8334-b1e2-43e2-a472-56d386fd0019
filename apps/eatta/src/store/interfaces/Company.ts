import { PageFilters } from '@/store/interfaces/filters'
export type OrganizationSetUpStage = 'PROFILE' | 'PAYMENT' | 'SUBMISSION'
export type UserSetUpStage = 'VERIFICATION' | 'PROFILE' | 'SUBMISSION'
export type OrganizationStatus = 'ACTIVE' | 'STAGING' | 'PENDING'

export const COMPANY_TYPES = [
  'Broker',
  'Producer',
  'Buyer',
  'Warehouse',
] as const

export const COMPANY_TYPE_EXTRAS = ['Factory', 'Patner'] as const

export const COMPANY_TYPES_COMBINED = [
  ...COMPANY_TYPES,
  ...COMPANY_TYPE_EXTRAS,
] as const

export type CompanyType = (typeof COMPANY_TYPES)[number]

export type CompanyTypeExtra = (typeof COMPANY_TYPE_EXTRAS)[number]

export const CHANNELS = ['IFT', 'RTGS', 'SWIFT'] as const

export const CURRENCIES: Currency[] = [
  {
    name: 'Kenyan Shillings',
    code: 'KES',
  },
  {
    name: 'United States Dollar',
    code: 'USD',
  },
]

export const COMPANY_USER_ROLES = ['Contact Person', 'Team Member'] as const

export const EAST_AFRICAN_COUNTRIES = ['Kenya', 'Tanzania', 'Uganda', 'Burundi']

export type CompanyUserRole = (typeof COMPANY_USER_ROLES)[number]

export type PaymentChannel = (typeof CHANNELS)[number]

export interface Company {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  name: string
  type: CompanyType | CompanyTypeExtra
  code: string
  accountNumber: string
  bankAccountNumber: string
  accountCurrency: string
  currency: string
  bankCode: string
  swiftCode: string
  bankName: string
  bankBranchCode: string
  phoneNumber: string
  emailAddress: string
  locationAddress: string
  clientId: string
  channel: string
  bankBranchName: string
  registrationNumber: string
  certificateUrl?: string
  logoUrl?: string
  status: OrganizationStatus
  stepName: OrganizationSetUpStage
  brsRequestId?: string
}

export type PartialCompanyData = Partial<Company>;

export interface CompanyFilters extends PageFilters {
  name?: string
  code?: string
  type?: string
  dateCreated?: string
}

export interface MakeRequest {
  approvalId?: string
  stepName?: UserSetUpStage | OrganizationSetUpStage
}

export interface CompanyProfileRequest extends MakeRequest {
  name: string
  type: CompanyType
  code: string
  phoneNumber: string
  emailAddress: string
  certificateUrl?: string
  logoUrl?: string
}

export interface FactoryProfileRequest extends MakeRequest {
  name: string
  phoneNumber?: string
  emailAddress?: string
  type: 'Factory'
  parentOrganizationId: string
}

export interface CompanyPaymentRequest extends MakeRequest {
  bankName: string
  bankAccountNumber: string
  bankBranchCode: string
  bankBranchName: string
  channel: PaymentChannel
  swiftCode: string
  currency: string
}

export interface UserIdentityRequest extends MakeRequest {
  organizationCode: string
  citizenship: string
  firstName: string
  middleName: string
  lastName: string
  nationalId: string
}

export interface UserProfileRequest extends MakeRequest {
  isCompanyAdmin: boolean
  email: string
  phoneNumber: string
  phoneNumberCountryId: string
  accountCreationDocumentUrl?: string | null
}

// Edit company user
export interface EditCompanyUser {
  id?: string
  name: string
  email: string
  phoneNumber: string
  phoneNumberCountryId: string
  username: string
  companyCode?: string
  password?: string
  status: CompanyUserStatus

  resetToken?: string
}
export interface CompanyUser {
  id: string
  name: string
  email: string
  phoneNumber: string
  phoneNumberCountryId: string
  companyName: string
  companyType: string
  username: string
  companyCode: string
  password: string
  status: CompanyUserStatus
  lastLogin: string
  dateCreated: string
  resetToken: string
  organizationCode: string
  citizenship: string
  firstName: string
  middleName: string
  lastName: string
  nationalId: string
  isCompanyAdmin: boolean
  gender: string
  accountCreationDocumentUrl?: string
  stepName: UserSetUpStage
}
export type PartialCompanyUser = Partial<CompanyUser>
export type CompanyUserStatus =
  | 'ACTIVE'
  | 'INACTIVE'
  | 'BLOCKED'
  | 'DELETED'
  | 'DORMANT'
  | 'PENDING'
  | 'STAGING'

export interface Bank {
  bankCode: string
  bankName: string
  bic: string
  bicCode: string
  ccCode: string
  shortName: string
}

export interface BankBranch {
  code: string
  name: string
  city: string
  address: string
  phone: string
}

export interface Currency {
  name: string
  code: string
}

export interface UserFilters extends PageFilters {
  name?: string
  email?: string
  phoneNumber?: string
  username?: string
  status?: string
  companyName?: string
 
}

export interface CompanyKycCheckRequest {
  identifier: string
  companyName: string
  externalReference: string
  kycType: string
  businessType: string
  overwrite: boolean
  branchCode: string
  searchType: string
}

export interface CompanyKycCheckResponse {
  requestId: string
  status: string
  message: string
}

export interface ShareCapita {
  shareCount: number
  nominalValue: number
  sharePercentage: number
  name: string
}

export interface Share {
  shareCount: number
  nominalValue: number
  sharePercentage: number
  name: string
}

export interface Partner {
  shares: Share[]
  type: string
  name: string
  idType: string
  idNumber: string
}

export interface KycResults {
  verified: boolean
  status: string
  registrationNumber: string
  registrationDate: string
  postalAddress: string
  physicalAddress: string
  phoneNumber: string
  email: string
  businessName: string
  shareCapital: ShareCapita[]
  partners: Partner[]
}

export interface CompanyKycDetails {
  externalReference: string
  requestId: string
  statusCode: number
  statusMessage: string
  nested: boolean
  results: KycResults[]
}

export interface UserKycResults {
  status: 'success' | 'failed'
  isFirstNameValid: boolean
  isLastNameValid: boolean
  isOtherNameValid: boolean
  isDobValid: boolean
  isGenderValid: boolean
  data: {
    firstName: string
    lastName: string
    otherName: string
    dob: string
    gender: string
    citizenship: string
  }
}
export interface UserKycCheckRequest {
  idNumber: string
  firstName: string
  lastName: string
  otherName: string
  citizenship?: string
  gender?: string
  requestId: string
  tag: string
}
export type OnboardingStep = {
  title: string
  description?: string
}
