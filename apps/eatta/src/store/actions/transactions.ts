/**
 * <AUTHOR> on 05/06/2025
 */
import { Dispatch } from '@reduxjs/toolkit'
import { mapObjectToUrlParams, removeFalsyValues } from '@/utils/objectUtil'
import { apiclient } from '@/utils/apiclient'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { setNotification } from '@dtbx/store/reducers'
import {
  AllocateTransactionRequest,
  Transaction,
  TransactionFilters,
} from '@/store/interfaces/transactions'
import {
  setIsAllocatingTransactions,
  setIsLoadingTransactions,
  setTransactionApprovalsRequests,
  setTransactionsResponse,
} from '@/store/reducers'
import {
  ApprovalRequestFilters,
  IApprovalRequest,
} from '@/store/interfaces/makerChecker'

export const getTransactions = async (
  dispatch: Dispatch,
  params: TransactionFilters
) => {
  try {
    dispatch(setIsLoadingTransactions(true))

    const searchParams = mapObjectToUrlParams({ ...params, ascending: false })

    const res = await apiclient.get<PaginatedResponse<Transaction>>(
      `/backoffice-bff/eatta-service/invoice/payment?${searchParams}`
    )
    dispatch(setTransactionsResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingTransactions(false))
  }
}

export const getTransactionApprovalRequests = async (
  dispatch: Dispatch,
  filters: ApprovalRequestFilters
) => {
  try {
    dispatch(setIsLoadingTransactions(true))

    const searchParams = mapObjectToUrlParams({ ...filters })

    const url = `/backoffice-auth/maker-checker/approvals?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<IApprovalRequest>>(url)

    dispatch(setTransactionApprovalsRequests(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingTransactions(false))
  }
}

export const allocateTransaction = async (
  isSuper: boolean,
  dispatch: Dispatch,
  data: AllocateTransactionRequest,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsAllocatingTransactions(true))

    const url = `/backoffice-bff/eatta-service/invoice/payment/allocate/${isSuper ? '' : 'make'}`
    await apiclient.post(url, removeFalsyValues(data))
    dispatch(
      setNotification({
        message: `Transaction allocation saved successfully ${!isSuper ? 'and is pending approval' : ''}`,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message

    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsAllocatingTransactions(false))
  }
}

export const approveTransactionRequest = async (
  dispatch: Dispatch,
  approvalId: string,
  comments: string,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsAllocatingTransactions(true))

    const url = `/backoffice-bff/eatta-service/invoice/payment/allocate/approve/${approvalId}`
    await apiclient.post(url, { comments })
    dispatch(
      setNotification({
        message: 'Transaction allocation approved successfully',
        type: 'success',
      })
    )
    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsAllocatingTransactions(false))
  }
}
