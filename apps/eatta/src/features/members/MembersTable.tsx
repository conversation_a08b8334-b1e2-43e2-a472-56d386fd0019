import { Company, CompanyFilters, CompanyUser, Order } from '@/store/interfaces'
import React, { useEffect, useState } from 'react'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch, useAppSelector } from '@/store'
import { sortData } from '@/utils/sortTableData'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { getUsersByOrganizationCode } from '@/store/actions'
import TableSkeleton from '@/components/TableSkeleton'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import {
  Button,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { ArrowForwardIos } from '@mui/icons-material'
import { AccessControlWrapper, formatDate } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { resetOnboardingState } from '@/store/reducers'
import { useResetPageOnFilterChange } from '@/hooks/useResetPageOnFilterChange'

interface MemberTableProps {
  filters: CompanyFilters
  onMembersCountChange?: (count: number) => void
}

const MembersTable: React.FC<MemberTableProps> = ({
  filters,
  onMembersCountChange,
}) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { selectedCompany, companyUsersResponse, isLoading } = useAppSelector(
    (state) => state.companies
  )

  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<keyof Company>('dateCreated')

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof Company)
  }

  const sortKey = orderBy as keyof CompanyUser
  const companiesDataSorted = sortData(
    [...companyUsersResponse.data],
    sortKey,
    order
  )

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
  }

  const handleCreateUser = () => {
    dispatch(resetOnboardingState())
    router.push(`/backoffice/companies/${selectedCompany?.id}/users/create`)
  }

  const resetPageRef = useResetPageOnFilterChange(
    filters,
    paginationOptions,
    setPaginationOptions
  )

  useEffect(() => {
    if (resetPageRef.current) {
      resetPageRef.current = false
      return
    }
    if (!selectedCompany) return
    const fetchUsers = async () => {
      // fetch list of users
      // await getUsersByOrganizationCode(dispatch, selectedCompany.code, {
      //   ...filters,
      //   page: paginationOptions.page,
      //   size: paginationOptions.size,
      //   ascending: false,
      // })
    }
    fetchUsers()
  }, [dispatch, filters, paginationOptions, selectedCompany])

  useEffect(() => {
    onMembersCountChange &&
      onMembersCountChange(companyUsersResponse.totalElements)
  }, [companyUsersResponse])

  return isLoading ? (
    <TableSkeleton rowCount={15} columnCount={6} />
  ) : companyUsersResponse.data.length === 0 ? (
    <EmptyPage
      title="No members found"
      message="Please create a new member to get started"
      bgUrl={'/eatta/combo.svg'}
      action={
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USER}>
          <Button
            variant="contained"
            type="submit"
            startIcon={<AddIcon />}
            onClick={handleCreateUser}
          >
            New member
          </Button>
        </AccessControlWrapper>
      }
    />
  ) : (
    <TableContainer
      component={Paper}
      sx={{
        boxShadow: 'none',
        '& .MuiTableCell-root': {
          paddingInline: '1.5rem',
          paddingBlock: '0.5rem',
          textAlign: 'left',
        },
      }}
    >
      <Table sx={{ minWidth: 650 }} aria-label="company table" size="small">
        <CustomTableHeader
          order={order}
          orderBy={orderBy}
          headLabel={[
            {
              id: 'nationalId',
              label: 'User ID',
              alignRight: false,
            },
            { id: 'name', label: 'Name/Contact', alignRight: false },
            { id: 'phone', label: 'Phone number', alignRight: false },
            {
              id: 'dateCreated',
              label: 'Date Created',
              alignRight: false,
            },
            {
              id: 'status',
              label: 'Status',
              alignRight: false,
            },
            {
              id: 'lastLogin',
              label: 'Last Login',
              alignRight: false,
            },
            { id: '', label: '', alignRight: false },
          ]}
          showCheckbox={false}
          rowCount={companyUsersResponse.data.length}
          numSelected={0}
          onRequestSort={handleRequestSort}
        />
        <TableBody>
          {companiesDataSorted.map((row) => {
            const {
              id,
              name,
              email,
              phoneNumber,
              status,
              lastLogin,
              nationalId,
              dateCreated,
            } = row
            return (
              <TableRow hover key={id} tabIndex={-1} role="checkbox">
                <TableCell component="th" scope="row" id={id}>
                  {nationalId ?? '-'}
                </TableCell>
                <TableCell>
                  <Stack>
                    <Typography variant="label1">{name}</Typography>
                    <Typography variant="body2">{email}</Typography>
                  </Stack>
                </TableCell>
                <TableCell>{phoneNumber}</TableCell>
                <TableCell>
                  {dateCreated ? formatDate(dateCreated) : '-'}
                </TableCell>
                <TableCell>
                  <StatusChip label={status} />
                </TableCell>
                <TableCell>{lastLogin ? formatDate(lastLogin) : '-'}</TableCell>
                <TableCell>
                  <IconButton onClick={() => {}}>
                    <ArrowForwardIos />
                  </IconButton>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell
              sx={{ paddingInline: 0 }}
              align="center"
              height={40}
              colSpan={12}
            >
              {companyUsersResponse.totalNumberOfPages > 0 && (
                <CustomPagination
                  options={{
                    ...paginationOptions,
                    totalPages: companyUsersResponse.totalNumberOfPages,
                  }}
                  handlePagination={handlePagination}
                />
              )}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  )
}

export default MembersTable
