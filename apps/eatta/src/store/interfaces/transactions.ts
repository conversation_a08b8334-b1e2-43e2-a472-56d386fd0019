import { PageFilters } from './filters'
import { InvoiceEslip } from '@/store/interfaces/invoices'

export type TransactionStatus = 'PENDING' | 'ALLOCATED' | 'UNALLOCATED'

export interface TransactionFilters extends PageFilters {
  year: number
  saleDate: string
  transactionRef?: string
  buyer?: string
  amount?: number
  status?: string
  eslipNumber?: string
}
export interface Transaction {
  id: string
  buyerName: string
  amount: number
  coreReference: string
  status: string
  description: string
  eslipNumber: string
  sourceAddress: string
  sourceName: string
  dateCreated?: string
}

export interface AllocateTransactionRequest {
  invoicePaymentId: string
  invoiceId: string
  comments: string
}

export interface TransactionApprovalEntity {
  invoicePayment: Transaction
  invoice: InvoiceEslip
}
