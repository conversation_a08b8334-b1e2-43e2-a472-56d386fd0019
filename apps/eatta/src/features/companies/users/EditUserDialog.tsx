import React, { useMemo, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import * as Yup from 'yup'
import { Form, FormikProvider, useFormik } from 'formik'
import {
  COMPANY_USER_ROLES,
  CompanyUserRole,
  EAST_AFRICAN_COUNTRIES,
  PartialCompanyUser,
} from '@/store/interfaces'
import { useAppSelector } from '@/store'
import CloseIcon from '@mui/icons-material/Close'
import { AddFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { FileUpload } from '@/components/FileUpload'
import {
  getUsersByOrganizationCode,
  updateUserDetails,
  uploadOnboardingDocuments,
} from '@/store/actions'
import { useAppDispatch } from '@dtbx/store'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'

const validationSchema = Yup.object({
  citizenship: Yup.string().required('Nationality is required'),
  isCompanyAdmin: Yup.boolean().required('Role is required'),
  nationalId: Yup.string()
    .required('National ID is required')
    .min(7, 'National ID must be at least 7 characters')
    .max(15, 'National ID must be at most 10 characters'),
  firstName: Yup.string().required('FirstName is required'),
  lastName: Yup.string().required('LastName is required'),
  email: Yup.string()
    .email('Invalid email format')
    .required('Email is required'),
  file: Yup.mixed().required('File is required'),
})

interface EditUserDialogProps {
  open: boolean
  handleClose: () => void
  UserData: PartialCompanyUser | null
}

export const EditUserForm = ({
  onCreateSuccess,
  onCancel,
  UserData,
}: {
  onCreateSuccess: () => void
  onCancel: () => void
  UserData: PartialCompanyUser | null
}) => {
  const { isEditingUser, selectedCompany } = useAppSelector(
    (state) => state.companies
  )

  const [documentUrl, setDocumentUrl] = useState<string>(
    UserData?.accountCreationDocumentUrl ?? ''
  )

  const dispatch = useAppDispatch()
  const user: PartialCompanyUser | null = UserData
  const [role, setRole] = React.useState<CompanyUserRole | null>(
    user?.isCompanyAdmin !== undefined && user?.isCompanyAdmin !== null
      ? user.isCompanyAdmin
        ? 'Contact Person'
        : 'Team Member'
      : null
  )

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_UPDATE_USER),
    []
  )
  const formik = useFormik({
    initialValues: {
      citizenship: UserData?.citizenship ?? '',
      nationalId: user?.nationalId ?? '',
      firstName: user?.firstName ?? '',
      middleName: user?.middleName ?? '',
      lastName: user?.lastName ?? '',
      email: user?.email ?? '',
      phoneNumber: UserData?.phoneNumber ?? '',
      isCompanyAdmin:
        user?.isCompanyAdmin !== undefined && user?.isCompanyAdmin !== null
          ? user?.isCompanyAdmin
          : false,
      file: undefined as File | undefined,
      makerStep:"SUBMISSION"
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      try {
        const { file, ...otherValues } = values
        if (!UserData || !UserData.id) {
          console.error('User or User Id missing')
          return
        }

        const userUpdateData: PartialCompanyUser = {
          ...otherValues,
          accountCreationDocumentUrl: documentUrl,
        }

        try {
          if (!documentUrl && file) {
            const documentUrl =
              (await uploadOnboardingDocuments(dispatch, file, {})) ?? ''
            setDocumentUrl(documentUrl)
            userUpdateData.accountCreationDocumentUrl = documentUrl
          }
        } catch (error) {
          return
        }

        await updateUserDetails(
          isSuper,
          dispatch,
          UserData.id,
          userUpdateData,
          () => onCreateSuccess()
        )
        if (selectedCompany) {
          await getUsersByOrganizationCode(dispatch, selectedCompany.code, {
            page: 1,
            size: 10,
            ascending: false,
          })
        }
      } catch (err) {
        console.error('User update failed:', err)
      }
    },
  })

  return (
    <FormikProvider value={formik}>
      <Form onSubmit={formik.handleSubmit}>
        <Stack spacing={2} paddingBlock={2}>
          <Stack
            direction="row"
            spacing={2}
            sx={{ width: '100%', alignItems: 'flex-start' }}
          >
            {/* Nationality */}
            <Stack spacing={1} sx={{ width: '50%' }}>
              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                Nationality
                <Box component="span" sx={{ color: 'primary.main' }}>
                  *
                </Box>
              </Typography>
              <Autocomplete
                disablePortal
                size="small"
                id="citizenship"
                options={EAST_AFRICAN_COUNTRIES}
                value={formik.values.citizenship}
                onChange={(_, value) =>
                  formik.setFieldValue('citizenship', value)
                }
                onBlur={() => formik.setFieldTouched('citizenship', true)}
                getOptionLabel={(option) => option}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Select Nationality"
                    size="small"
                    fullWidth
                    margin="none"
                    error={
                      formik.touched.citizenship &&
                      Boolean(formik.errors.citizenship)
                    }
                    helperText={
                      formik.touched.citizenship && formik.errors.citizenship
                        ? formik.errors.citizenship
                        : ''
                    }
                  />
                )}
              />
            </Stack>

            {/* ID Number */}
            <Stack spacing={1} sx={{ width: '50%' }}>
              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                ID Number
                <Box component="span" sx={{ color: 'primary.main' }}>
                  *
                </Box>
              </Typography>
              <TextField
                hiddenLabel
                size="small"
                type="text"
                placeholder="Enter ID Number"
                fullWidth
                margin="none"
                {...formik.getFieldProps('nationalId')}
                error={
                  formik.touched.nationalId && Boolean(formik.errors.nationalId)
                }
                helperText={
                  formik.touched.nationalId && formik.errors.nationalId
                }
              />
            </Stack>
          </Stack>

          <Stack spacing={1} sx={{ width: '100%' }}>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              FIrst Name{' '}
              <Box component="span" sx={{ color: 'primary.main' }}>
                *
              </Box>
            </Typography>
            <TextField
              hiddenLabel
              size="small"
              type="text"
              placeholder="Enter first name"
              sx={{ marginBlock: '0' }}
              margin="normal"
              fullWidth
              {...formik.getFieldProps('firstName')}
              error={
                formik.touched.firstName && Boolean(formik.errors.firstName)
              }
              helperText={formik.touched.firstName && formik.errors.firstName}
            />
          </Stack>

          <Stack spacing={1} sx={{ width: '100%' }}>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              Middle Name(s)
            </Typography>
            <TextField
              hiddenLabel
              size="small"
              type="text"
              placeholder="Enter middle name(s)"
              sx={{ marginBlock: '0' }}
              margin="normal"
              fullWidth
              {...formik.getFieldProps('middleName')}
              error={
                formik.touched.middleName && Boolean(formik.errors.middleName)
              }
              helperText={formik.touched.middleName && formik.errors.middleName}
            />
          </Stack>

          <Stack spacing={1} sx={{ width: '100%' }}>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              Last Name{' '}
              <Box component="span" sx={{ color: 'primary.main' }}>
                *
              </Box>
            </Typography>
            <TextField
              hiddenLabel
              size="small"
              type="text"
              placeholder="Enter last name"
              sx={{ marginBlock: '0' }}
              margin="normal"
              fullWidth
              {...formik.getFieldProps('lastName')}
              error={formik.touched.lastName && Boolean(formik.errors.lastName)}
              helperText={formik.touched.lastName && formik.errors.lastName}
            />
          </Stack>
          <Stack spacing={1} sx={{ width: '100%' }}>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              Role
              <Box component="span" sx={{ color: 'primary.main' }}>
                *
              </Box>
            </Typography>
            <Autocomplete
              disablePortal
              size="small"
              id="bankCode"
              options={COMPANY_USER_ROLES}
              value={role}
              onChange={(_, value) => {
                setRole(value)
                formik.setFieldValue(
                  'isCompanyAdmin',
                  value === 'Contact Person'
                )
              }}
              getOptionLabel={(option) => option}
              renderInput={(params) => (
                <TextField
                  hiddenLabel
                  {...params}
                  placeholder="Select role"
                  error={Boolean(
                    formik.touched.isCompanyAdmin &&
                      formik.errors.isCompanyAdmin
                  )}
                  helperText={
                    formik.touched.isCompanyAdmin &&
                    formik.errors.isCompanyAdmin
                      ? formik.errors.isCompanyAdmin
                      : ''
                  }
                />
              )}
            />
          </Stack>
        
          <Stack
            direction="row"
            spacing={2}
            sx={{ width: '100%', alignItems: 'flex-start' }}
          >
            <Stack spacing={1} sx={{ width: '100%' }}>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              Email
              <Box component="span" sx={{ color: 'primary.main' }}>
                *
              </Box>
            </Typography>
            <TextField
              hiddenLabel
              size="small"
              type="text"
              placeholder="Enter Email"
              sx={{ marginBlock: '0' }}
              margin="normal"
              fullWidth
              {...formik.getFieldProps('email')}
              error={formik.touched.lastName && Boolean(formik.errors.email)}
              helperText={formik.touched.lastName && formik.errors.email}
            />
          </Stack>
          <Stack spacing={1} sx={{ width: '100%' }}>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              Phone Number
              <Box component="span" sx={{ color: 'primary.main' }}>
                *
              </Box>
            </Typography>
            <TextField
  hiddenLabel
  size="small"
  type="text"
  placeholder="Enter PhoneNumber"
  sx={{ marginBlock: '0' }}
  margin="normal"
  fullWidth
  {...formik.getFieldProps('phoneNumber')}
  error={formik.touched.lastName && Boolean(formik.errors.phoneNumber)}
  helperText={formik.touched.lastName && formik.errors.phoneNumber}
  InputProps={{ readOnly: true }}
/>
          </Stack>

           
          </Stack>
          <Stack spacing={1} sx={{ width: '100%' }}>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              Attach account creation request form :{' '}
              <Box component="span" sx={{ color: 'primary.main' }}>
                *
              </Box>
              <Typography>
                Usually a signed document provided by EATTA to authorize
                creation of the user's account.
              </Typography>
              <FileUpload
                fileUrl={user?.accountCreationDocumentUrl}
                description="Only PDF file allowed."
                mimeTypes={['application/pdf']}
                required={true}
                disabled={isEditingUser}
                onFileChange={(file) => formik.setFieldValue('file', file)}
                onFileDeleted={() => {
                  setDocumentUrl('')
                  formik.setFieldValue('file', undefined)
                }}
              />
            </Typography>
          </Stack>

          <Stack
            direction="row"
            spacing={2}
            sx={{
              mt: 3,
              width: '100%',
            }}
          >
            <Button
              sx={{ width: '40%' }}
              disabled={isEditingUser}
              variant="outlined"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <AccessControlWrapper rights={ACCESS_CONTROLS.UPDATE_USER}>
              <Button
                fullWidth
                variant="contained"
                type="submit"
                disabled={!formik.isValid || isEditingUser}
                endIcon={
                  isEditingUser ? (
                    <CircularProgress size={20} thickness={3.0} />
                  ) : undefined
                }
              >
                Update
              </Button>
            </AccessControlWrapper>
          </Stack>
        </Stack>
      </Form>
    </FormikProvider>
  )
}

export const EditUserDialog = ({
  open,
  handleClose,
  UserData,
}: EditUserDialogProps) => {
  return (
    <Dialog maxWidth={'sm'} open={open} onClose={handleClose}>
      <DialogTitle fontWeight={600}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            spacing={1}
          >
            <AddFactoryIcon height={32} width={32} />
            <Typography variant="subtitle1" fontWeight="bold">
              Edit User -{UserData?.firstName} {UserData?.lastName}
            </Typography>
          </Stack>
          <IconButton
            sx={{
              height: '2rem',
              width: '2rem',
              border: '1px solid #D0D5DD',
              borderRadius: '0.5rem',
            }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>
      <DialogContent>
        <EditUserForm
          onCreateSuccess={handleClose}
          onCancel={handleClose}
          UserData={UserData}
        />
      </DialogContent>
    </Dialog>
  )
}
