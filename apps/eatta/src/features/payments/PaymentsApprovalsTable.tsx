import React, { useEffect, useState } from 'react'
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { ArrowForwardIos } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import { getPaymentsApprovalRequests } from '@/store/actions/transactions'
import {
  Payment,
  PaymentApprovalEntity,
  PaymentFilters,
  PaymentStatus,
} from '@/store/interfaces/transactions'
import { InvoiceEslip, Order } from '@/store/interfaces'
import { sortData } from '@/utils/sortTableData'
import TableSkeleton from '@/components/TableSkeleton'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { IApprovalRequest } from '@/store/interfaces/makerChecker'
import { safeJsonParse } from '@/utils/objectUtil'
import { AllocationModal } from '@/features/payments/allocate/AllocationModal'
import { formatCurrency, formatDate } from '@dtbx/store/utils'
import {
  resetAllocatePayment,
  setAllocateEslip,
  setSelectedTransactionApprovalsRequests,
  setSelectedPayments,
} from '@/store/reducers'
import { EATTA_MODULES } from '@/utils/constants'

export interface TransactionsTableProps {
  status: PaymentStatus
  filters: PaymentFilters
  canSelect: boolean
  onSetCurrentTab: (tabIndex: number) => void
  onApprovalCountChange?: (count: number) => void
}

export const PaymentsApprovalsTable = ({
  status,
  filters,
  onSetCurrentTab,
  onApprovalCountChange,
}: TransactionsTableProps) => {
  const dispatch = useAppDispatch()

  const { approvalRequestResponse, isLoading } = useAppSelector(
    (state) => state.transactions
  )

  const [openAllocationModal, setOpenAllocationModal] = useState<boolean>(false)

  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<keyof IApprovalRequest>('dateCreated')

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof IApprovalRequest)
  }

  const sortKey = orderBy as keyof IApprovalRequest
  const approvalsSorted = sortData(
    [...approvalRequestResponse.data],
    sortKey,
    order
  )

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }

  const handleApproval = (
    approvalRequest: IApprovalRequest,
    eslip: InvoiceEslip,
    transaction: Payment
  ) => {
    dispatch(setSelectedPayments(transaction))
    dispatch(setAllocateEslip(eslip))
    dispatch(setSelectedTransactionApprovalsRequests(approvalRequest))
    setOpenAllocationModal(true)
  }

  const handleCloseAllocationModal = (tabIndex: number | undefined) => {
    setOpenAllocationModal(false)
    dispatch(resetAllocatePayment())
    if (tabIndex !== undefined) onSetCurrentTab(tabIndex)
  }

  useEffect(() => {
    const fetchApprovals = async () => {
      await getPaymentsApprovalRequests(dispatch, {
        status,
        module: EATTA_MODULES.allocations,
        page: paginationOptions.page,
        size: paginationOptions.size,
        channel: 'EATTA',
      })
    }
    fetchApprovals()
  }, [dispatch, filters, paginationOptions.page])

  useEffect(() => {
    onApprovalCountChange &&
      onApprovalCountChange(approvalRequestResponse.totalElements)
  }, [approvalRequestResponse.totalElements])

  return isLoading ? (
    <TableSkeleton rowCount={15} columnCount={6} />
  ) : approvalRequestResponse.data.length === 0 ? (
    <EmptyPage
      title="No transactions approvals found"
      message="Transactions approvals will appear here once they are processed."
      bgUrl={'/eatta/combo.svg'}
    />
  ) : (
    <>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
          '& .MuiTableCell-root': {
            paddingInline: '1.5rem',
            paddingBlock: '0.5rem',
            textAlign: 'left',
          },
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="transactions table"
          size="small"
        >
          <CustomTableHeader
            order={'asc'}
            orderBy={'id'}
            headLabel={[
              {
                id: 'eslipNumber',
                label: 'E-Slip Number',
                alignRight: false,
              },
              { id: 'buyerName', label: 'Buyer', alignRight: false },
              { id: 'sourceName', label: 'Source Info', alignRight: false },
              {
                id: 'coreReference',
                label: 'Corebanking Ref',
                alignRight: false,
              },
              { id: 'maker', label: 'Maker', alignRight: false },
              { id: 'dateCreated', label: 'Date Received', alignRight: false },
              { id: 'amount', label: 'Amount Received', alignRight: false },
              { id: 'status', label: 'Status', alignRight: false },
              { label: '' },
            ]}
            showCheckbox={false}
            rowCount={approvalRequestResponse.data.length}
            numSelected={0}
            onRequestSort={handleRequestSort}
          />
          <TableBody>
            {approvalsSorted.map((approvalRequest) => {
              const { id, entity, maker, dateCreated, status } = approvalRequest
              const approval = safeJsonParse<PaymentApprovalEntity>(entity)
              if (!approval) return null
              const {
                buyerName,
                amount,
                coreReference,
                eslipNumber,
                sourceName,
                sourceAddress,
              } = approval.invoicePayment
              return (
                <TableRow hover key={eslipNumber} tabIndex={-1} role="checkbox">
                  <TableCell>{eslipNumber}</TableCell>
                  <TableCell>{buyerName || 'N/A'}</TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {sourceName || 'N/A'}
                    </Typography>
                    <Typography variant="body2">{sourceAddress}</Typography>
                  </TableCell>
                  <TableCell>{coreReference}</TableCell>
                  <TableCell>{maker}</TableCell>
                  <TableCell>{formatDate(dateCreated ?? '')}</TableCell>
                  <TableCell>
                    {formatCurrency(amount, 'USD', 'en-US')}
                  </TableCell>
                  <TableCell>
                    <StatusChip status="info" label="Pending Approval" />
                  </TableCell>

                  <TableCell>
                    <IconButton
                      onClick={() =>
                        handleApproval(
                          approvalRequest,
                          approval.invoice,
                          approval.invoicePayment
                        )
                      }
                    >
                      <ArrowForwardIos />
                    </IconButton>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell
                align="center"
                colSpan={12}
                sx={{ paddingInline: 0, height: 40 }}
              >
                {approvalRequestResponse.totalNumberOfPages > 0 && (
                  <CustomPagination
                    options={{
                      ...paginationOptions,
                      totalPages: approvalRequestResponse.totalNumberOfPages,
                    }}
                    handlePagination={handlePagination}
                  />
                )}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>

      {openAllocationModal && (
        <AllocationModal
          open={openAllocationModal}
          onClose={handleCloseAllocationModal}
          actionType={'CHECKER'}
        />
      )}
    </>
  )
}
