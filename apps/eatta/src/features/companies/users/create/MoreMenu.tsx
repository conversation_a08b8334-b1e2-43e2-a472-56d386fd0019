import { IconButton } from '@mui/material'
import { DotsVerticalIcon } from '@dtbx/ui/components/SvgIcons'
import { CustomMenu, StyledMenuItem } from '@/components/CustomMenu'
import { EditFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { useState } from 'react'
import { SwitchButton } from '@/components/SwitchButton'
import { UpdateEmailModal } from './UpdateEmailModal'
import { CompanyUser } from '@/store/interfaces'

type UserMoreMenuProps = {
  user: CompanyUser
}

export const UserMoreMenu = ({ user }: UserMoreMenuProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [updateEmailModalOpen, setUpdateEmailModalOpen] = useState(false)

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleToggle = (_event: React.ChangeEvent<HTMLInputElement>) => {
    console.log('Toggle')
  }

  const open = Boolean(anchorEl)

  const handleUpdateEmail = () => {
    // setAnchorEl(null)
    setUpdateEmailModalOpen(true)
  }
  return (
    <>
      <IconButton onClick={handleClick}>
        <DotsVerticalIcon stroke="#FFFFFF" />
      </IconButton>

      <CustomMenu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={{
          list: {
            'aria-labelledby': 'basic-button',
          },
        }}
      >
        <StyledMenuItem onClick={handleUpdateEmail}>
          Update Email
          <EditFactoryIcon />
        </StyledMenuItem>

        <StyledMenuItem>
          Update Phone Number
          <EditFactoryIcon />
        </StyledMenuItem>

        <StyledMenuItem>
          Deactivate User
          <SwitchButton onHandleToggle={handleToggle} isActive={'ACTIVE'} />
        </StyledMenuItem>
      </CustomMenu>

      <UpdateEmailModal
        open={updateEmailModalOpen}
        handleClose={() => setUpdateEmailModalOpen(false)}
        user={user}
      />
    </>
  )
}
