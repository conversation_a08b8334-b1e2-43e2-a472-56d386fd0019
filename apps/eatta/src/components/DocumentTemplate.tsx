import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ack, Typography } from '@mui/material'
import { QRCode } from '@/components/SvgIcons/QRCode'
import { Logo } from '@/components/SvgIcons/Logo'
import Image from 'next/image'
import React from 'react'
import { FileDownloadIcon } from '@dtbx/ui/components/SvgIcons'

const DELIVERY_DETAILS = [
  {
    label: 'Warehouse',
    value: 'Delivery Order No',
    subValue: 'AS01/001',
  },
  {
    label: 'Afriport Logistics Limited',
    value: 'Invoice No.',
    subValue: 'AS01/001',
  },
  {
    label: 'Buyer',
    value: 'Auction No.',
    subValue: '2025/23',
  },
  {
    label: 'AB Exports Limited',
    value: 'Sale Date.',
    subValue: 'June 15, 2025',
  },
]

const TEA_DETAILS = [
  {
    label: 'Mark',
    value: 'Munobwa',
  },
  {
    label: 'Warrant #',
    value: '489384',
  },
  {
    label: 'Lot Number',
    value: '123456',
  },
  { label: 'Pkgs', value: '20' },
  { label: 'Pkg Type', value: 'PB' },
  { label: 'Grade', value: 'DUST' },
  { label: 'Net Kgs', value: '1400.00' },
  { label: 'Pkg Wt', value: '70' },
]

const BROKER_DETAILS = [
  {
    label: 'Initiated by',
    value: 'Michael Mwangi, Tea Broker’s East Africa',
  },
  {
    label: 'Approved By',
    value: 'Jane Mwangi, Tea Broker’s East Africa',
  },
  {
    label: 'Digitally Signed on [Date & Time] by:',
    value: 'SIGNED: JANE MIRITII MWANGI',
  },
]

const Page = () => {
  return (
    <>
      <Stack
        sx={{
          background: '#FFF',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          margin: '2rem auto',
          width: '50vw',
          padding: '2rem',
          textAlign: 'center',
          fontSize: '1.2rem',
          height: '100%',
        }}
      >
        <Stack sx={{ gap: '1rem', width: '100%' }}>
          <Stack
            sx={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              alignSelf: 'stretch',
              alignContent: 'space-between',
              marginTop: '8rem',
            }}
          >
            <Image src="/image.png" alt="Logo" width={260} height={95} />
            <Typography
              sx={{
                fontWeight: 600,
                fontSize: '18px',
                color: '#000',
                marginRight: '2rem',
              }}
            >
              Delivery Order
            </Typography>
            <Stack sx={{ border: '1px solid #D0D5DD' }}>
              <Typography>Scan to Verify</Typography>
              <QRCode />
            </Stack>
          </Stack>
          <Stack
            sx={{ alignItems: 'flex-start', color: '#000', gap: '0.5rem' }}
          >
            <Typography
              sx={{ fontWeight: 700, fontSize: '18px', color: '#000' }}
            >
              To: Afriport Logistics Limited
            </Typography>
            <Typography sx={{ color: '#000' }}>
              Please deliver this tea to AB Exports Limited on payment of all
              charges after Jan 20, 2025
            </Typography>
          </Stack>

          {/* Delivery Details */}

          <Stack sx={{ width: '100%' }}>
            <Typography
              sx={{
                color: '#344054',
                fontWeight: '600',
                textAlign: 'left',
                marginBottom: '0.5rem',
              }}
            >
              Delivery Details
            </Typography>
            <Stack
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '3px',
                padding: '1rem',
                gap: '0.5rem',
                width: '100%',
              }}
            >
              {DELIVERY_DETAILS.map((row, index) => (
                <Stack
                  key={index}
                  sx={{
                    flexDirection: 'row',
                    gap: '2rem',
                    justifyContent: 'space-between',
                  }}
                >
                  <Typography>{row.label}</Typography>
                  <Stack
                    direction="row"
                    sx={{ textAlign: 'right', gap: '0.5rem' }}
                  >
                    <Typography>{row.value}</Typography>
                    <Typography sx={{ color: '#344054', fontWeight: 600 }}>
                      {row.subValue}
                    </Typography>
                  </Stack>
                </Stack>
              ))}
            </Stack>
          </Stack>

          {/* Tea Details */}
          <Stack>
            <Typography
              sx={{
                color: '#344054',
                fontWeight: '600',
                textAlign: 'left',
                marginBottom: '0.5rem',
              }}
            >
              Tea Details
            </Typography>
            <Stack
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '3px',
                gap: '0.5rem',
                padding: '1rem',
              }}
            >
              {TEA_DETAILS.map((row, index) => (
                <React.Fragment key={index}>
                  <Stack
                    sx={{
                      flexDirection: 'row',
                      gap: '2rem',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Typography>{row.label}</Typography>
                    <Typography sx={{ color: '#344054', fontWeight: 600 }}>
                      {row.value}
                    </Typography>
                  </Stack>
                  {index < TEA_DETAILS.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </Stack>
          </Stack>

          {/* Broker Approval and Signature */}
          <Stack>
            <Stack direction="row" alignItems="center">
              <Typography
                sx={{
                  fontWeight: 600,
                  color: '#344054',
                }}
              >
                Broker Approval and Signature
              </Typography>
              <Button
                variant="text"
                endIcon={<FileDownloadIcon stroke="#029327" />}
                sx={{
                  color: '#029327',
                  fontSize: '1rem',
                  fontWeight: 600,
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                }}
              >
                View TRD
              </Button>
            </Stack>

            <Stack
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '3px',
                padding: '1rem',
                gap: '0.5rem',
              }}
            >
              {BROKER_DETAILS.map((row, index) => (
                <React.Fragment key={index}>
                  <Stack
                    sx={{
                      flexDirection: 'row',
                      gap: '2rem',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Typography>{row.label}</Typography>
                    <Typography sx={{ color: '#344054', fontWeight: 600 }}>
                      {row.value}
                    </Typography>
                  </Stack>
                  {index < BROKER_DETAILS.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </Stack>
          </Stack>
          <Stack sx={{ alignItems: 'center', gap: '1rem' }}>
            <Typography>Facilitated by Diamond Trust Bank</Typography>
            <Logo />
          </Stack>
        </Stack>
      </Stack>
    </>
  )
}

export default Page
