import React from 'react'
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import * as Yup from 'yup'
import { Form, FormikProvider, useFormik } from 'formik'
import CloseIcon from '@mui/icons-material/Close'
import { AddFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { createWarehouse } from '@/store/actions/companies'
import { useAppDispatch } from '@/store'
import { ALPHA_NUMERIC_REGEX, ORG_NAME_REGEX } from '@/utils/validators'

const validationSchema = Yup.object({
  godownCode: Yup.string()
    .required('EATTA Godown Code should not be empty')
    .matches(ALPHA_NUMERIC_REGEX, 'Only alphanumeric characters are allowed')
    .min(3, 'EATTA Godown Code must be at least 3 characters'),
  name: Yup.string()
    .required('Name/Location should not be empty')
    .matches(
      ORG_NAME_REGEX,
      'Only letters, numbers, spaces, and brackets are allowed. No leading or trailing spaces.'
    )
    .test(
      'alpha',
      'Name/Location must contain at least three letters.',
      (value) => (value.match(/[a-zA-Z]/g) || []).length >= 3
    ),
})

type CreateWarehouseFormProps = {
  organizationId: string
  godownCode: string
  name: string
  onCreateWarehouseSuccess: () => void
  onCancel: () => void
}

export const CreateWarehouseForm = ({
  organizationId,
  godownCode,
  name,
  onCreateWarehouseSuccess,
  onCancel,
}: CreateWarehouseFormProps) => {
  const dispatch = useAppDispatch()
  const formik = useFormik({
    initialValues: {
      godownCode: godownCode,
      name: name,
      warehouseId: organizationId,
    },
    validationSchema,
    onSubmit: async (values) => {
      await createWarehouse(dispatch, values, onCreateWarehouseSuccess)
      onCreateWarehouseSuccess()
    },
  })

  return (
    <FormikProvider value={formik}>
      <Form onSubmit={formik.handleSubmit}>
        <Stack spacing={3} paddingBlock={2}>
          <Stack spacing={1}>
            <Typography fontWeight={600}>Enter Godown EATTA Code</Typography>
            <TextField
              hiddenLabel
              size="medium"
              type="text"
              placeholder="MCK001"
              {...formik.getFieldProps('godownCode')}
              fullWidth
              error={Boolean(
                formik.touched.godownCode && formik.errors.godownCode
              )}
              helperText={formik.touched.godownCode && formik.errors.godownCode}
            />
          </Stack>
          <Stack spacing={1}>
            <Typography fontWeight={600}>Name/Location</Typography>
            <TextField
              hiddenLabel
              size="medium"
              type="text"
              placeholder="Mitchell Cotts Freight Kenya Voi St. Godown No.1"
              {...formik.getFieldProps('name')}
              fullWidth
              error={Boolean(formik.touched.name && formik.errors.name)}
              helperText={formik.touched.name && formik.errors.name}
            />
          </Stack>
          <Stack direction="row" spacing={2} sx={{ mt: 3, width: '100%' }}>
            <Button
              sx={{ width: '40%' }}
              variant="outlined"
              onClick={onCancel}
              type="button"
            >
              Cancel
            </Button>
            <Button
              fullWidth
              variant="contained"
              type="submit"
              sx={{
                backgroundColor: '#00C33D',
                color: '#fff',
                '&:hover': { backgroundColor: '#00A32A' },
              }}
              disabled={!formik.isValid}
            >
              Save
            </Button>
          </Stack>
        </Stack>
      </Form>
    </FormikProvider>
  )
}

type CreateWarehouseDialogProps = {
  organizationName: string
  organizationId: string
  open: boolean
  handleClose: () => void
}

export const CreateWarehouseDialog = ({
  organizationName,
  organizationId,
  open,
  handleClose,
}: CreateWarehouseDialogProps) => {
  return (
    <Dialog fullWidth open={open} onClose={handleClose}>
      <DialogTitle fontWeight={600}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Stack direction="row" alignItems="center" spacing={1}>
            <AddFactoryIcon height={32} width={32} />
            <Typography variant="h4" fontWeight="bold">
              Add new Go-down
            </Typography>
          </Stack>
          <IconButton
            sx={{
              height: '2rem',
              width: '2rem',
              border: '1px solid #D0D5DD',
              borderRadius: '0.5rem',
            }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>
      <DialogContent>
        <DialogContentText sx={{ mb: 3 }}>
          Godown will be registered under <b>{organizationName}</b>
        </DialogContentText>
        <CreateWarehouseForm
          organizationId={organizationId}
          godownCode={''}
          name={''}
          onCreateWarehouseSuccess={handleClose}
          onCancel={handleClose}
        />
      </DialogContent>
    </Dialog>
  )
}
