'use client'

import { Divider, Stack } from '@mui/material'
import ApprovalRequestsTable from './ApprovalRequestTable'
import { ApprovalRequestHeader } from './ApprovalRequestsHeader'
import {
  ApprovalRequestFilters,
  ApprovalRequestStatus,
} from '@/store/interfaces/makerChecker'
import { useCallback, useEffect, useState } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { CustomTabs, TabType } from '@/components/CustomTabs'
import { useAppDispatch, useAppSelector } from '@/store'
import { resetApprovalRequest } from '@/store/reducers/approvalRequestsReducer'

const TABS: TabType<ApprovalRequestStatus>[] = [
  {
    title: 'Pending Requests',
    canSelect: true,
    status: 'PENDING',
  },
  {
    title: 'All Requests',
    canSelect: true,
    status: 'APPROVED',
  },
]

const initialFilters: ApprovalRequestFilters = {
  page: 1,
  size: 10,
  makerFirstName: '',
  makerLastName: '',
  requestType: '',
  createDateFrom: '',
  createDateTo: '',
  status: '',
  organizationCode: '',
  channel: '',
  module: '',
}

const ApprovalRequestPage = () => {
  const dispatch = useAppDispatch()
  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState<TabType<ApprovalRequestStatus>[]>(TABS)
  const [selectedTab, setSelectedTab] = useState(initialTab)
  const [filters, setFilters] = useState<ApprovalRequestFilters>(initialFilters)

  const { isLoadingApprovals, approvalRequests } = useAppSelector(
    (state) => state.approvals
  )

  const handleChange = (field: keyof ApprovalRequestFilters, value: string) => {
    setFilters({
      ...filters,
      [field]: value,
    })
  }

  const handlePageChange = useCallback((page: number, size: number) => {
    setFilters((prev) => ({
      ...prev,
      page,
      size,
    }))
  }, [])

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    replace(`${pathname}?${params.toString()}`)
  }

  const handleTabSelected = (index: number) => {
    dispatch(resetApprovalRequest())
    setSelectedTab(index)
  }

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab])

  return (
    <>
      <Stack sx={{ backgroundColor: '#fff' }}>
        <ApprovalRequestHeader
          filters={filters}
          onSearchChange={handleChange}
        />
        <Divider />
        <Stack>
          <CustomTabs
            tabs={tabs}
            selectedTab={selectedTab}
            onTabSelected={handleTabSelected}
            isLoading={isLoadingApprovals}
          />
        </Stack>
        <ApprovalRequestsTable
          filters={filters}
          status={tabs[selectedTab].status}
          onPageChange={handlePageChange}
        />
      </Stack>
    </>
  )
}

export default ApprovalRequestPage
