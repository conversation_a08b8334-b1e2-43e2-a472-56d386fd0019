export interface WarehouseFilters {
  name?: string
  godownCode?: string
  warehouseId?: string
  page?: number
  size?: number
  dateCreated?: string
  ascending?: boolean
}

export interface WarehouseCreationRequest {
  godownCode: string
  name: string
  warehouseId: string | undefined
}

export interface WarehouseResponse {
  id: string
  godownCode: string
  name: string
  warehouseId: string
}

export interface WarehouseUpdateRequest extends WarehouseCreationRequest {}
