import {
  BrokerCommissionStatus,
  BrokerInvoiceStatus,
  InvoiceEntryStatus,
  InvoiceStatus,
} from '@/store/interfaces'
import { DeliveryOrderStatus } from '@/store/interfaces/Edo'
import { ApprovalRequestStatus } from '@/store/interfaces/makerChecker'
import { TransactionStatus } from '@/store/interfaces/transactions'
import { SeverityStatus } from '@dtbx/ui/components/Chip'

export const STATUS_COLOR: Record<
  InvoiceStatus,
  'info' | 'warn' | 'success' | 'error' | 'processing'
> = {
  NEW: 'info',
  PARTIAL: 'warn',
  PAID: 'success',
  BATCHED: 'warn',
  FAILED: 'error',
  INVOICED: 'processing',
}

export const BROKER_INVOICE_STATUS_COLOR: Record<
  BrokerInvoiceStatus,
  'info' | 'warn' | 'success' | 'error' | 'processing'
> = {
  NEW: 'info',
  READY: 'success',
  PARTIAL: 'warn',
  PAID: 'success',
  SETTLED: 'success',
  INVALID: 'error',
  UNPAID: 'info',
  BATCHED: 'warn',
  FAILED: 'error',
  INVOICED: 'processing',
}

export const DELIVERY_ORDER_STATUS_COLOR: Record<
  DeliveryOrderStatus,
  'info' | 'warn' | 'success' | 'error' | 'processing'
> = {
  PENDING_APPROVAL: 'warn',
  NEW: 'info',
  SIGNED: 'success',
}

export const INVOICE_ENTRY_STATUS_COLOR: Record<
  InvoiceEntryStatus,
  'info' | 'warn' | 'success' | 'error'
> = {
  PAID: 'success',
  UNPAID: 'info',
  PAID_AT_EQUITY: 'success',
  PAID_AT_STANBIC: 'success',
  SETTLED: 'success',
  PENALIZED: 'error',
}

export const INVOICE_ENTRY_STATUS_MAP = (
  status: InvoiceEntryStatus,
  lateDays: number | null
): string => {
  if (status === 'PENALIZED' && lateDays !== null) {
    return `Late + ${lateDays}`
  }
  return status
}

export const COMMISSION_STATUS_COLOR: Record<
  BrokerCommissionStatus,
  'info' | 'warn' | 'success' | 'error'
> = {
  PENDING: 'error',
  Active: 'success',
  Expired: 'error',
}

export const CHIP_COLORS = {
  success: {
    bg: '#ECFDF5',
    label: '#047857',
    border: '#B0FFC4',
    color: 'primary.main',
    borderColor: 'primary.main',
    borderRadius: '1rem',
    fontSize: '0.75rem',
    fontWeight: 500,
  },
  info: {
    bg: '#F0F9FF',
    label: '#0369A1',
    border: '#BAE6FD',
    color: 'info.main',
    borderColor: 'info.main',
    borderRadius: '1rem',
    fontSize: '0.75rem',
    fontWeight: 500,
  },
  warn: {
    bg: '#FFFBEB',
    label: '#B45309',
    border: '#FDE68A',
    color: 'warning.main',
    borderColor: 'warning.main',
    borderRadius: '1rem',
    fontSize: '0.75rem',
    fontWeight: 500,
  },
  error: {
    bg: '#FEF2F2',
    label: '#B91C1C',
    border: '#FECACA',
    color: 'error.main',
    borderColor: 'error.main',
    borderRadius: '1rem',
    fontSize: '0.75rem',
    fontWeight: 500,
  },
}

export const APPROVAL_STATUS_MAP: Record<ApprovalRequestStatus, string> = {
  STAGING: 'Ongoing',
  PENDING: 'Pending Checker Review',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
}

export const TRANSACTION_STATUS_MAP: Record<TransactionStatus, SeverityStatus> =
  {
    PENDING: 'warn',
    FAILED: 'error',
    SUCCESS: 'success',
  }

export const APPROVAL_STATUS_COLOR: Record<
  ApprovalRequestStatus,
  'info' | 'warn' | 'success' | 'error'
> = {
  STAGING: 'success',
  PENDING: 'info',
  APPROVED: 'success',
  REJECTED: 'error',
}
