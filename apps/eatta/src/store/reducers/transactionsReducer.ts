/**
 * <AUTHOR> on 05/06/2025
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { Transaction } from '@/store/interfaces/transactions'
import { IApprovalRequest } from '@/store/interfaces/makerChecker'
import { number } from 'yup'
import { InvoiceEslip } from '@/store/interfaces'

export interface TransactionState {
  approvalRequestResponse: PaginatedResponse<IApprovalRequest>
  isLoading: boolean
  transactions: PaginatedResponse<Transaction>
  selectedTransaction: Transaction | null
  isAllocatingTransaction: boolean
  allocateEslip: InvoiceEslip | null
  approvalRequest: IApprovalRequest | null
}

const initialPaginatedState = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}

const initialState: TransactionState = {
  isLoading: false,
  transactions: initialPaginatedState,
  selectedTransaction: null,
  approvalRequestResponse: initialPaginatedState,
  isAllocatingTransaction: false,
  allocateEslip: null,
  approvalRequest: null,
}

const transactionsSlice = createSlice({
  name: 'transactions',
  initialState,
  reducers: {
    setIsLoadingTransactions: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setTransactionsResponse: (
      state,
      action: PayloadAction<PaginatedResponse<Transaction>>
    ) => {
      state.transactions = action.payload
    },
    setSelectedTransactions: (state, action: PayloadAction<Transaction>) => {
      state.allocateEslip = null
      state.selectedTransaction = action.payload
    },
    setAllocateEslip: (state, action: PayloadAction<InvoiceEslip>) => {
      state.allocateEslip = action.payload
    },
    setTransactionApprovalsRequests: (
      state,
      action: PayloadAction<PaginatedResponse<IApprovalRequest>>
    ) => {
      state.approvalRequestResponse = action.payload
    },
    setSelectedTransactionApprovalsRequests: (
      state,
      action: PayloadAction<IApprovalRequest>
    ) => {
      state.approvalRequest = action.payload
    },
    setIsAllocatingTransactions: (state, action: PayloadAction<boolean>) => {
      state.isAllocatingTransaction = action.payload
    },
    resetAllocateTransaction: (state) => {
      state.allocateEslip = null
      state.approvalRequest = null
      state.selectedTransaction = null
    },
    resetState: (state) => initialState,
  },
})
export const {
  setIsLoadingTransactions,
  setTransactionsResponse,
  setTransactionApprovalsRequests,
  setSelectedTransactionApprovalsRequests,
  setIsAllocatingTransactions,
  setSelectedTransactions,
  setAllocateEslip,
  resetAllocateTransaction,
} = transactionsSlice.actions
export default transactionsSlice.reducer
