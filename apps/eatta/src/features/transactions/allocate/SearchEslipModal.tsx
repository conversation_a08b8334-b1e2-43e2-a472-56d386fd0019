/**
 * <AUTHOR> on 05/06/2025
 */
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Stack,
  Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import React, { FC, useState } from 'react'
import { EslipDetails } from '@/features/transactions/allocate/EslipDetails'
import { InvoiceEslip } from '@/store/interfaces'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import { useAppDispatch, useAppSelector } from '@/store'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { getInvoiceEslips } from '@/store/actions'
import { resetInvoiceEslips } from '@/store/reducers'

interface SearchEslipModalProps {
  open: boolean
  onClose: () => void
  onEslipSelected?: (eslip: InvoiceEslip) => void
  year: number
  saleDate: string
}
export const SearchEslipModal: FC<SearchEslipModalProps> = ({
  open,
  onClose,
  onEslipSelected,
  year,
  saleDate,
}) => {
  const dispatch = useAppDispatch()
  const [searchValue, setSearchValue] = useState<string>('')

  const { invoiceEslipsResponse, isLoading } = useAppSelector(
    (state) => state.invoices
  )

  const searchEslip = async () => {
    dispatch(resetInvoiceEslips())
    await getInvoiceEslips(dispatch, {
      invoiceNumber: searchValue,
      ascending: false,
      status: 'NEW',
      year,
      saleDate,
      page: 1,
      size: 10,
    })
  }

  return (
    <Dialog fullWidth maxWidth="md" open={open} onClose={onClose}>
      <DialogTitle sx={{ backgroundColor: 'primary.main' }} fontWeight={600}>
        <Stack spacing={2}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography color={'#FFFFFF'} variant="subtitle1" fontWeight="bold">
              Find e-slip
            </Typography>
            <IconButton
              sx={{
                height: '2rem',
                width: '2rem',
                border: '1px solid #FFFFFF',
                borderRadius: '0.5rem',
              }}
              onClick={onClose}
            >
              <CloseIcon sx={{ color: '#FFFFFF' }} />
            </IconButton>
          </Stack>

          <Stack spacing={2} direction="row" justifyContent="space-between">
            <CustomSearchInput
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              sx={{
                width: '100%',
                backgroundColor: '#FFFFFF',
                borderRadius: '0.5rem',
              }}
              placeholder="E-slip number..."
            />

            <Button
              variant="contained"
              disabled={!searchValue}
              onClick={searchEslip}
              sx={{
                flexShrink: 0,
                padding: '0.5rem',
                height: '2.5rem',
                borderRadius: '0.5rem',
                color: '#FFFFFF',
                border: '1px solid #E4E7EC',
                '&:disabled': {
                  color: 'text.disabled',
                  borderColor: 'text.disabled',
                },
              }}
              endIcon={
                isLoading ? (
                  <CircularProgress
                    sx={{ color: '#FFFFFF' }}
                    size={20}
                    thickness={3.0}
                  />
                ) : (
                  <SearchOutlinedIcon
                    sx={{ color: !searchValue ? 'text.disabled' : '#FFFFFF' }}
                  />
                )
              }
            >
              Search E-Slip
            </Button>
          </Stack>
        </Stack>
      </DialogTitle>
      <Divider />
      <DialogContent>
        <Stack spacing={2}>
          {invoiceEslipsResponse.data.length === 0 ? (
            <>
              <EmptyPage
                title="No Eslip found"
                message="Start typing e-slip number on the search input above and click search"
                bgUrl={'/eatta/combo.svg'}
              />
            </>
          ) : (
            <>
              <Stack spacing={1} justifyContent="space-between">
                <Typography variant="body1" fontWeight={600}>
                  Found {invoiceEslipsResponse.totalElements} Item
                </Typography>
                <Typography variant="body1">
                  Select E-Slip to allocate.
                </Typography>
              </Stack>
              {invoiceEslipsResponse.data.map((eslip) => (
                <EslipDetails
                  key={eslip.id}
                  eslip={eslip}
                  canSelect={true}
                  onEslipSelected={onEslipSelected}
                />
              ))}
            </>
          )}
        </Stack>
      </DialogContent>
    </Dialog>
  )
}
