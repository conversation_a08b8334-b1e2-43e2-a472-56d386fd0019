'use client'
import {
  <PERSON><PERSON>,
  <PERSON>,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined'
import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { formatDate } from '@dtbx/store/utils'
import { CustomTableHeader, PaginationOptions } from '@dtbx/ui/components/Table'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { CustomPagination } from '@dtbx/ui/components/Table'

import { CategoriesMoreMenu } from '../../settings/categories/MoreMenu'
import { getLoanProductCategories } from '@/store/actions'

const headerList = [
  { id: 'code', label: 'Code', alignRight: false },
  { id: 'name', label: 'Name', alignRight: false },
  { id: 'createdBy', label: 'Created By', alignRight: false },
  { id: 'dateCreated', label: 'Date Created', alignRight: false },
  { id: 'dateModified', label: 'Date Modified', alignRight: false },
  { id: 'actions', label: '', alignRight: false },
]
export const CategoriesList = () => {
  const dispatch = useAppDispatch()
  const { productCategories, productCategoriesRequest } = useAppSelector((state) => state.loans)
  const [page, setPage] = useState(0)
  const [rowsPerPage] = useState(10)
  const [selected, setSelected] = useState<readonly string[]>([])
  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
    totalPages: 0,
  })
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    let newSelected: readonly string[] = []
    if (event.target.checked) {
      newSelected = productCategories.map((category) => category.id)
    }
    setSelected(newSelected)
  }
  const handleSelectOne = (event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: readonly string[] = []
    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
  }
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPage(newOptions.page)
    await getLoanProductCategories(dispatch, newOptions.page, 10)
  }
  /*************************end pagination handlers**************************/

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: '8px',
        border: '1px solid #EAECF0',
        background: '#FFFFFF',
        my: '1%',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          padding: '2%',
        }}
      >
        <Stack>
          <Typography variant="subtitle1">Categories</Typography>
          <Typography variant="subtitle3">
            Showing {productCategories.length} categories
          </Typography>
        </Stack>
        <Button variant="outlined" startIcon={<FilterListOutlinedIcon />}>
          Filter
        </Button>
      </Stack>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="designations table"
          size="small"
        >
          <CustomTableHeader
            order={'asc'}
            orderBy={'id'}
            headLabel={headerList}
            showCheckbox={true}
            rowCount={10}
            numSelected={0}
            onRequestSort={() => {}}
            onSelectAllClick={handleSelectAll}
          />
          <TableBody>
            {productCategories &&
              productCategories
                .map((row) => {
                  const {
                    id,
                    code,
                    createdBy,
                    dateCreated,
                    dateModified,
                    name,
                  } = row
                  const isItemSelected = selected.indexOf(id) !== -1
                  return (
                    <TableRow
                      hover
                      key={id}
                      tabIndex={-1}
                      role="checkbox"
                      onClick={(event) => handleSelectOne(event, row.id)}
                      selected={isItemSelected}
                      aria-checked={isItemSelected}
                    >
                      <TableCell padding="checkbox">
                        <CustomCheckBox
                          checked={isItemSelected}
                          inputProps={{
                            'aria-labelledby': id,
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="subtitle3">{code}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography
                          variant="subtitle2"
                          sx={{ color: 'primary.main' }}
                        >
                          {name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{createdBy}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(dateCreated)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(dateModified)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <CategoriesMoreMenu category={row} />
                      </TableCell>
                    </TableRow>
                  )
                })}
          </TableBody>
        </Table>
      </TableContainer>
      {productCategoriesRequest?.totalNumberOfPages > 0 && (
        <CustomPagination
          options={{
            ...paginationOptions,
            totalPages: 
              productCategoriesRequest?.totalNumberOfPages,
          }}
          handlePagination={handlePagination}
        />
      )}
    </Paper>
  )
}
