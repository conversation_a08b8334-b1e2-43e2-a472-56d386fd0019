/**
 * <AUTHOR> on 06/05/2025
 */
import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { StatusChip } from '@dtbx/ui/components/Chip'
import React, { FC, useEffect, useState } from 'react'
import { getBrokerInvoiceEntries } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { BrokerInvoiceStatus, InvoiceEntry, Order } from '@/store/interfaces'
import { BROKER_INVOICE_STATUS_COLOR } from '@/utils/statusChips'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import {
  resetInvoicesStore,
  setSelectedInvoiceLot,
  setSelectedInvoiceLots,
} from '@/store/reducers'
import TableSkeleton from '@/components/TableSkeleton'
import { TooltipTableCell } from '@/components/TooltipTableCell'
import { formatCurrency } from '@dtbx/store/utils'

interface InvoiceLotsTableProps {
  invoiceId: string
  canSelect: boolean
}

export const InvoiceLotsTable: FC<InvoiceLotsTableProps> = ({
  invoiceId,
  canSelect,
}) => {
  const dispatch = useAppDispatch()
  const isBackOffice = checkIfBackOffice()
  const { brokerInvoicesEntryResponse, isLoading, selectedInvoiceLots } =
    useAppSelector((state) => state.invoices)
  const { data } = brokerInvoicesEntryResponse
  const selectableInvoiceEntries = data.filter(
    (entry) => entry.status === 'UNPAID'
  )

  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<keyof InvoiceEntry>('lotNumber')

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
    totalPages: brokerInvoicesEntryResponse.totalNumberOfPages,
  })

  const handlePagination = (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
  }

  const handleSelectOne = (invoice: InvoiceEntry) => {
    if (invoice.status !== 'UNPAID' || !canSelect) return
    dispatch(setSelectedInvoiceLot(invoice))
  }

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    let invoices: InvoiceEntry[] = []
    if (event.target.checked) {
      invoices = selectableInvoiceEntries
    }
    dispatch(setSelectedInvoiceLots(invoices))
  }

  useEffect(() => {
    const fetchInvoicedLots = async () => {
      await getBrokerInvoiceEntries(dispatch, {
        size: paginationOptions.size,
        page: paginationOptions.page,
        ascending: true,
        brokerInvoiceId: invoiceId,
      })
    }
    fetchInvoicedLots()
  }, [dispatch, invoiceId, paginationOptions])

  //Clear selected invoice
  useEffect(() => {
    return () => {
      dispatch(resetInvoicesStore())
    }
  }, [])

  return isLoading ? (
    <TableSkeleton rowCount={4} columnCount={14} />
  ) : data.length === 0 ? (
    <EmptyPage
      bgUrl={isBackOffice ? '/eatta/combo.svg' : '/combo.svg'}
      title="No Lots found"
      message="Your Invoiced lots will appear here."
    />
  ) : (
    <Stack sx={{ backgroundColor: '#FFFFFF' }}>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
          '& .MuiTableCell-root': {
            paddingInline: '1.5rem',
            paddingBlock: '0.5rem',
            textAlign: 'left',
          },
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="post auction table"
          size="small"
        >
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={[
              { id: 'lotNo', label: 'Lot', alignRight: false },
              { id: 'invoiceNo', label: 'Garden Invoice', alignRight: false },
              { id: 'buyerCode', label: 'Buyer', alignRight: false },
              { id: 'wareHouse', label: 'Warehouse', alignRight: false },
              { id: 'grade', label: 'Grade', alignRight: false },
              { id: 'pkgs', label: 'Pkgs', alignRight: false },
              { id: 'unitWeight', label: 'Unit weight', alignRight: false },
              {
                id: 'netWeight',
                label: 'Sold Weight (Kgs)',
                alignRight: false,
              },
              {
                id: 'pricePerKg',
                label: 'Purchase Price ($)',
                alignRight: false,
              },
              { id: 'totalValue', label: 'Total Value ($)', alignRight: false },
              {
                id: 'brokerShare',
                label: 'Broker($)',
                alignRight: false,
              },
              { id: 'withholdingTax', label: 'WHT ($)', alignRight: false },
              { id: 'totalPenalties', label: 'Penalties', alignRight: false },
              {
                id: 'netAmount',
                label: 'Amount to Pay ($)',
                alignRight: false,
              },
              { id: 'status', label: 'Status', alignRight: false },
            ]}
            showCheckbox={canSelect}
            rowCount={selectableInvoiceEntries.length}
            numSelected={selectedInvoiceLots.length}
            onRequestSort={() => {}}
            onSelectAllClick={handleSelectAll}
          />
          <TableBody>
            {data.map((row) => {
              const { id } = row
              const isItemSelected = selectedInvoiceLots.some(
                (row) => row.id === id
              )
              const unpaid = row.status === 'UNPAID'
              return (
                <TableRow
                  hover
                  key={row.id}
                  onClick={() => handleSelectOne(row)}
                  tabIndex={-1}
                  role="checkbox"
                  selected={isItemSelected}
                  aria-checked={isItemSelected}
                >
                  {canSelect && (
                    <TableCell padding="checkbox">
                      <CustomCheckBox
                        disabled={!unpaid}
                        checked={isItemSelected}
                        customColor={'#26b43b'}
                        style={{
                          padding: '5px',
                        }}
                      />
                    </TableCell>
                  )}
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['lotNumber']}
                      value={row.lotNumber}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['gardenInvoice']}
                      value={row.gardenInvoice}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['buyerCode']}
                      value={row.buyerCode}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['warehouse']}
                      value={row.warehouse}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['grade']}
                      value={row.grade}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['bags']}
                      value={row.bags}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['unitWeight']}
                      value={row.unitWeight}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['totalWeight']}
                      value={row.totalWeight}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['pricePerKg']}
                      value={formatCurrency(row.pricePerKg, 'USD', 'en-US')}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['totalValue']}
                      value={formatCurrency(row.totalValue, 'USD', 'en-US')}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['brokerShare']}
                      value={formatCurrency(row.brokerShare, 'USD', 'en-US')}
                    />
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['withholdingTax']}
                      value={formatCurrency(row.withholdingTax, 'USD', 'en-US')}
                    />
                  </TableCell>
                  <TableCell>
                    {formatCurrency(row.totalPenalties, 'USD', 'en-US')}
                  </TableCell>
                  <TableCell>
                    <TooltipTableCell
                      error={row.errors?.['netAmount']}
                      value={`$${new Intl.NumberFormat('en-US').format(row.netAmount)}`}
                    />
                  </TableCell>
                  <TableCell>
                    <StatusChip
                      label={row.status as BrokerInvoiceStatus}
                      status={
                        BROKER_INVOICE_STATUS_COLOR[
                          row.status as BrokerInvoiceStatus
                        ]
                      }
                    />
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell
                align="center"
                colSpan={12}
                sx={{ paddingInline: 0, height: 40 }}
              >
                {brokerInvoicesEntryResponse.totalNumberOfPages > 0 && (
                  <CustomPagination
                    options={{
                      ...paginationOptions,
                      totalPages:
                        brokerInvoicesEntryResponse.totalNumberOfPages,
                    }}
                    handlePagination={handlePagination}
                  />
                )}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
    </Stack>
  )
}
